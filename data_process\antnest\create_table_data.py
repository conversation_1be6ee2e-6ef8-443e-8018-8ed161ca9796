import pandas as pd
import json

# 读取 Excel 文件
file_path = 'data/MASH_Samples_merged_biota_analysis.xlsx'  # 替换为你的文件路径
df = pd.read_excel(file_path)

# 定义列的映射关系
columns_mapping = {
    'RunID': 'runId',
    'Source Type': 'sourceType',
    'BioprojectID': 'projectId',
    'tOmics_type': 'omicsType',
    'Technology_type': 'technologyType',
    'Lat_lon': 'latLon',
    'Critical Zone': 'criticalZone',
    'Sampling Substrate': 'sampSubstrate',
    'Country': 'country',
    'Water Body Type': 'waterType',
    'Water Body Name': 'waterName'
}

# 定义用于确定 featureDataset 的列
feature_columns = ['MASH-Lake', 'MASH-Ocean', 'MASH-Chinasea', 'MASH-China', 'MEER']

# 函数：格式化 LatLon 保留 4 位小数
def format_lat_lon(lat_lon):
    try:
        lat, lon = map(float, lat_lon.split(','))
        return f"{lat:.4f}, {lon:.4f}"
    except Exception:
        return ""

# 构造 JS 数组对象
result = []
for _, row in df.iterrows():
    # 处理基础字段
    obj = {columns_mapping[col]: row[col] for col in columns_mapping if col in row}

    # 格式化 latLon 字段
    if 'Lat_lon' in row and pd.notna(row['Lat_lon']):
        obj['latLon'] = format_lat_lon(row['Lat_lon'])

    # 确定 featureDataset 的值
    feature_dataset = None
    for col in feature_columns:
        if col in row and row[col] == 'IN':
            feature_dataset = col
            break
    obj['featureDataset'] = feature_dataset if feature_dataset else ''

    result.append(obj)

# 输出为 JSON 格式的字符串
js_array = json.dumps(result, indent=2, ensure_ascii=False)
print(js_array)

# 将结果保存为文件（可选）
with open('table_data.js', 'w', encoding='utf-8') as f:
    f.write(f'const data = {js_array} \nexport default data;\n')
