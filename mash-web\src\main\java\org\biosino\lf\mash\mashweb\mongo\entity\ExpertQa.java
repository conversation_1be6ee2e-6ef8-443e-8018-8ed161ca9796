package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;


/**
 * 专家问答
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
@Document(collection = "expert_qa")
public class ExpertQa {

    @Id
    private String id;

    private String selectedType;

    private String question;

    private String title;

    private String name;

    private String organization;

    private String email;

    private String ip;

    private String status;

    private String msg;

    private Date createDate;

    private Date updateDate;

    @Transient
    private List<String> questionParagraphs;
    @Transient
    private String selectedTypeText;


}
