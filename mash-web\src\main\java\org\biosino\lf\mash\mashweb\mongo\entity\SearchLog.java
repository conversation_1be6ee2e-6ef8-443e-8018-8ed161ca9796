package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * 热词（Hot spot）
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Data
@Document(collection = "search_log")
public class SearchLog implements Serializable {
    @Id
    private String id;

    private String text;

    @Field("text_lowercase")
    @Indexed(name = "uni_search_log_text", unique = true)
    private String textLowercase;

    /**
     * 前端热词按照num数值倒序显示
     */
    private long num = 1;

    @Field("create_date")
    private Date createDate;

    @Field("update_date")
    private Date updateDate;

}
