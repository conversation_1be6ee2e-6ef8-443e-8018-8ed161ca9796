import axios from 'axios';
import { nanoid } from 'nanoid';

// 创建axios实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  headers: {
    'Content-Type': 'application/json',
    // API密钥需要替换为实际的API密钥
    'Authorization': 'Bearer ' + import.meta.env.VITE_DIFY_TOKEN,
  },
});

// 创建新的axios实例，用于调用 zsk 服务
const metadataClient = axios.create({
  baseURL: `${import.meta.env.VITE_APP_BASE_API}/zsk`,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + import.meta.env.VITE_DIFY_TOKEN,
  },
});

// 生成随机用户ID
const generateUserId = () => {
  // return 'mash-user-' + Math.random().toString(36).substring(2, 15);
  return 'mash-user-' + nanoid();
};

// 保存或获取用户ID
const getUserId = () => {
  let userId = localStorage.getItem('mash_qa_user_id');
  if (!userId) {
    userId = generateUserId();
    localStorage.setItem('mash_qa_user_id', userId);
  }
  return userId;
};

export default {
  // 发送问题并获取回答
  async askQuestion(question, conversation_id = null) {
    try {
      const userId = getUserId();
      // console.log(question, selectedType);

      // 创建EventSource用于接收流式响应
      const controller = new AbortController();
      const signal = controller.signal;

      const apiParams = {
        query: question,
        response_mode: 'streaming',
        user: userId,
        inputs: {},
      };

      // 如果有会话ID，添加到请求参数中实现多轮对话
      if (conversation_id) {
        apiParams.conversation_id = conversation_id;
      }

      const response = await fetch(
        `${apiClient.defaults.baseURL}/chat-messages`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': apiClient.defaults.headers.Authorization,
          },
          body: JSON.stringify(apiParams),
          signal,
        },
      );

      return { response, controller };
    } catch (error) {
      console.error('提问失败:', error);
      throw error;
    }
  },

  // 停止响应
  async stopResponse(taskId) {
    try {
      const userId = getUserId();
      await apiClient.post(`/chat-messages/${taskId}/stop`, {
        user: userId,
      });
    } catch (error) {
      console.error('停止响应失败:', error);
      throw error;
    }
  },

  // 根据多个 PMID 查询 metadata 信息
  async queryMetadata(pmid, child_text = null) {
    try {
      // 构建请求体数据
      const requestData = {
        pmid,
      };

      // 如果提供了 child_text，则添加到请求数据中
      if (child_text) {
        requestData.child_text = child_text;
      }

      // 发送 POST 请求
      const response = await metadataClient.post('/metadata', requestData);

      return response.data;
    } catch (error) {
      console.error('查询 Metadata 失败:', error);
      throw error;
    }
  },
};
