import json
import sys


def read_lakes_list(file_path):
    """
    读取包含湖泊名称列表的文件，并去掉每个名称中的"Lake"字符

    参数:
        file_path: 包含湖泊名称的文件路径

    返回:
        处理后的湖泊名称集合（全部转为小写）
    """
    lakes = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                lake_name = line.strip()
                if lake_name:  # 跳过空行
                    # 去掉名称中的"Lake"字符，使用replace方法，并忽略大小写
                    lake_name_lower = lake_name.lower()
                    clean_name = lake_name_lower.replace('lake', '').replace('river', '').strip()
                    lakes.add(clean_name)
                    # 同时保留原始名称的小写形式，以防有些记录使用完整名称
                    lakes.add(lake_name_lower)
        print(f"成功从 {file_path} 读取了 {len(lakes)} 个湖泊名称")
        return lakes
    except Exception as e:
        print(f"读取湖泊列表出错: {e}")
        sys.exit(1)


def clean_geojson(input_geojson_path, output_geojson_path, lakes_list):
    """
    清理GeoJSON文件，只保留湖泊列表中包含的湖泊数据

    参数:
        input_geojson_path: 输入GeoJSON文件路径
        output_geojson_path: 输出GeoJSON文件路径
        lakes_list: 要保留的湖泊名称集合（已转为小写）
    """
    try:
        # 读取GeoJSON文件
        with open(input_geojson_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否是JS文件并包含export default
        is_js_file = input_geojson_path.endswith('.js')
        has_export_default = False

        # 调试信息
        print(f"原始内容开头: {content[:50]}")

        if is_js_file:
            # 使用更简单直接的方式检测和替换
            if content.strip().startswith('export default'):
                print("检测到export default前缀")
                # 找到第一个大括号的位置
                first_brace_index = content.find('{')
                if first_brace_index > 0:
                    # 保留从第一个大括号开始的内容
                    content = content[first_brace_index:]
                    has_export_default = True
                    print(f"去除前缀后内容开头: {content[:50]}")

        # 解析JSON内容
        try:
            geojson_data = json.loads(content)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"内容开头: {content[:200]}")
            raise

        # 检查GeoJSON文件格式
        if 'features' not in geojson_data:
            print("错误: 输入的GeoJSON文件没有'features'字段")
            return

        original_feature_count = len(geojson_data['features'])
        print(f"原始GeoJSON包含 {original_feature_count} 个地理特征")

        # 筛选要保留的特征
        filtered_features = []
        for feature in geojson_data['features']:
            # 检查该特征是否包含湖泊名称属性
            properties = feature.get('properties', {})
            lake_name = properties.get('name') or properties.get('Name') or properties.get('NAME')

            if lake_name:
                # 将湖泊名称转为小写，方便比较
                lake_name_lower = lake_name.lower()
                # 去掉名称中的"Lake"字符以进行匹配，忽略大小写
                clean_name = lake_name_lower.replace('lake', '').replace('river', '').strip()

                # 检查原始名称或处理后的名称是否在列表中（忽略大小写）
                if lake_name_lower in lakes_list or clean_name in lakes_list:
                    filtered_features.append(feature)

        # 创建新的GeoJSON数据
        filtered_geojson = geojson_data.copy()
        filtered_geojson['features'] = filtered_features

        # 将过滤后的GeoJSON转换为JSON字符串
        json_string = json.dumps(filtered_geojson, ensure_ascii=False)

        # 如果原文件是JS文件且有export default，添加回export default前缀
        if is_js_file and has_export_default:
            json_string = f"export default{json_string}"

        # 保存到输出文件
        with open(output_geojson_path, 'w', encoding='utf-8') as f:
            f.write(json_string)

        print(f"处理完成: 从 {original_feature_count} 个特征中筛选出 {len(filtered_features)} 个特征")
        print(f"已保存到 {output_geojson_path}")

    except Exception as e:
        print(f"处理GeoJSON出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    lakes_list = read_lakes_list('./lakes_in_data.txt')

    clean_geojson('../../app/src/assets/geojson/sample_lakes.js', './sample_lakes.js', lakes_list)
    clean_geojson('../../app/src/assets/geojson/sample_rivers.js', './sample_rivers.js', lakes_list)
