# -*- coding: utf-8 -*-

"""
根据pmid读取PLOSP接口处理3W+文献
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import logging
import os

import pandas as pd
import requests
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import str_val, time_stat
from custom_log_config import init_default_logger
from download_pdf import download_file_by_pmid
from fill_mash_excel import to_int_num
from fill_more_mash_excel import find_by_pmid_in_plosp
from find_pmid_by_title import HEADERS, mongo_uri, mongo_db_name

logger = init_default_logger('process_supp_table_publication.log')

filepath = 'data/excel/Supp_Table_publication.xlsx'
all_result_filepath = 'data/excel/Supp_Table_publication_plus.xlsx'
supp_collection_name = 'supp_tp_articles'
download_dir = 'data/excel/downloads'


def add_col(df: pd.DataFrame, index, one_item):
    pmid = to_int_num(one_item.get('pmid'))
    if pmid:
        # 更新'名称'列和添加新列
        df.at[index, 'Authors'] = str_val(one_item, 'author')
        df.at[index, 'Title'] = str_val(one_item, 'title')
        df.at[index, 'Journal'] = str_val(one_item, 'journalTitle')
        df.at[index, 'Year'] = str_val(one_item, 'year')
        df.at[index, 'PMID'] = str(pmid)

        pdf_info = one_item.get('pdf', None)
        has_pdf = True if pdf_info and pdf_info.get('path', None) else False
        df.at[index, 'PDF2RAG'] = 'YES' if has_pdf else 'NO'

        df.at[index, 'DOI'] = str_val(one_item, 'doi')
    else:
        raise ValueError(f'pmid为空，请检查数据: {one_item}')


def add_no_pmid_col(df: pd.DataFrame, index, one_item):
    df.at[index, 'PDF2RAG'] = 'NO'


def find_item_by_pmid_in_plosp(pmid, _session: requests.Session, logger_obj: logging.Logger):
    json_data = find_by_pmid_in_plosp(pmid, _session)
    status = json_data.get('status', '')
    msg = json_data.get('msg', '')
    one_item = json_data.get('article', None)
    if status == 'success':
        return one_item
    else:
        logger_obj.error(f'api查询出错: {pmid}，错误信息: {msg}')
        return None


def process_row(collection: Collection, df: pd.DataFrame, index, pmid: int, _session: requests.Session):
    try:
        one_item = collection.find_one({'pmid': pmid})
        if not one_item:
            one_item = find_item_by_pmid_in_plosp(pmid, _session, logger)
            if one_item:
                collection.insert_one(one_item)

        if one_item:
            add_col(df, index, one_item)
        else:
            # add_no_pmid_col(df, index, one_item)
            logger.info(f'未找到pmid: {pmid}')

    except BaseException as e:
        logger.error(f'行：{index + 1}，处理pmid失败: {pmid}，错误信息: {str(e)}')


def download_pdf_by_pmid(pmid, _session: requests.Session, logger_obj: logging.Logger, dir_name=None):
    download_dir_path = dir_name if dir_name else download_dir
    file_path = f'{download_dir_path}/{pmid}.pdf'
    if os.path.exists(file_path) and os.path.isfile(file_path):
        # 若文件大于500字节，则认为文件已下载
        if os.path.getsize(file_path) > 500:
            logger_obj.info(f'文件已下载: {pmid}')
            return False

    download_file_by_pmid(_session, pmid, download_dir_path, None)


def download_pdf(collection: Collection, _session: requests.Session):
    # 仅查询存在pdf的数据
    query = {"pdf.path": {"$exists": True}}
    projection = {"pmid": 1, "_id": 0}
    results = collection.find(query, projection)
    # 遍历结果
    for doc in results:
        pmid = doc.get('pmid')
        download_pdf_by_pmid(pmid, _session, logger)


@time_stat(logger)
def process_supp_table_publication(download_only: bool = False):
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[supp_collection_name]
    _session = None
    try:
        _session = requests.session()
        _session.headers = HEADERS

        if not download_only:
            # 检查文件是否存在
            if not os.path.exists(filepath):
                logger.error(f'文件不存在: {filepath}')
                return

            # 读取Excel文件
            df = pd.read_excel(filepath, engine='openpyxl')
            if df.empty or 'PMID' not in df.columns:
                logger.error(f'文件缺少"PMID"列或为空: {filepath}')
                return

            # 打印文件路径和列内容
            logger.info(f'处理文件：{filepath}')

            for index, row in df.iterrows():
                pmid = str_val(row, 'PMID')
                if pmid:
                    pmid = to_int_num(pmid)
                    if not pmid:
                        continue
                    print(f'正在处理行{index + 1}: {pmid}')
                    process_row(collection, df, index, pmid, _session)

            df.to_excel(all_result_filepath, index=False, engine='openpyxl')
            logger.info(f'更新后的文件已保存到: {all_result_filepath}')

        logger.info(f'正在下载PDF文件...')
        download_pdf(collection, _session)
        logger.info(f'正在下载PDF文件完毕')
    except Exception as e:
        logger.error(f'处理文件失败: {filepath}，错误信息: {str(e)}')
    finally:
        client.close()
        if _session:
            _session.close()


if __name__ == '__main__':
    process_supp_table_publication()
