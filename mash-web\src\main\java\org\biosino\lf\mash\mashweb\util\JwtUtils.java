package org.biosino.lf.mash.mashweb.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Jwt工具类
 *
 * <AUTHOR>
 */
public class JwtUtils {
    public final static String TOKEN_KEY = "api_token_key";
    public final static String TOKEN_VAL = "mash_api_val";

    /**
     * jwt令牌秘钥
     */
    private final static String secret = "ZjdlZDI0ZjE2MzA3ZTliODEzN2I1NTdkMGU5ZDFjZmFITzU2Q0JUWlVEZnVsV3czM0dvcURjSGM=";
    // 动态生成 HS384 安全密钥（无废弃 API）
    private static final SecretKey SECRET_KEY = generateSecureKey();


    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    public static String createToken(Map<String, Object> claims) {
        return Jwts.builder().claims(claims).signWith(SECRET_KEY).compact();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    public static Claims parseToken(String token) {
        return Jwts.parser().verifyWith(SECRET_KEY).build().parseSignedClaims(token).getPayload();
    }


    private static SecretKey generateSecureKey() {
        // 若密钥是明文字符串（自动转为字节数组）
        // byte[] decodedKey = secret.getBytes(StandardCharsets.UTF_8);
        final byte[] decodedKey = Base64.getDecoder().decode(secret); // Base64 模式
        if (decodedKey.length < 48) {
            throw new IllegalArgumentException("HS384 算法要求密钥至少 48 字节");
        }
        return new SecretKeySpec(decodedKey, "HmacSHA384");
    }

    /**
     * 生成jwt token
     */
    public static void main(String[] args) {
        final Map<String, Object> claims = new HashMap<>();
        claims.put("active_profile", "dev");
        claims.put(TOKEN_KEY, TOKEN_VAL);
        System.out.println(createToken(claims));
    }

}
