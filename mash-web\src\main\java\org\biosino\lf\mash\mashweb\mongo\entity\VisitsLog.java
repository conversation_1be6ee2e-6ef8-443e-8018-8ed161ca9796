package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;


/**
 * 访问日志表
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "visits_log")
public class VisitsLog implements Serializable {

    @Id
    private String id;

    /**
     * 前端生成随机ID，放在sessionStorage中
     */
    private String sessionId;

    /**
     * 访问的URL(当前页面url的上下文之后的部分)
     */
    private String url;

    /**
     * 访问的功能模块
     */
    private String functionModule;

    /**
     * 访问的IP
     */
    private String ip;

    /**
     * 从请求头中获取访问的浏览器的User-Agent
     */
    private String browser;


    /**
     * 访问时间
     */
    private Date createTime;

    /**
     * URL请求参数(?a=1&b=2)
     */
    private String query;

    /**
     * URL请求参数(/home/<USER>/:param2)
     */
    private String params;

    /**
     * post请求体
     */
    private String data;

}
