package org.biosino.lf.mash.mashweb;

import cn.hutool.crypto.SecureUtil;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

// @SpringBootTest
class MashWebApplicationTests {

    @Test
    void contextLoads() {
        String API_AES_SALT = "******TEST@NODE*";
        String API_AES_TOKEN = "pdms"; // bmdc中的用户
        String token = SecureUtil.aes(API_AES_SALT.getBytes(StandardCharsets.UTF_8)).encryptHex(API_AES_TOKEN);
        System.out.println(token);
    }

}
