package org.biosino.lf.mash.mashweb.mongo.repository;

import org.biosino.lf.mash.mashweb.dto.HomeMapQueryDTO;
import org.biosino.lf.mash.mashweb.dto.SamplesQueryDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SamplesCustomRepository {
    Page<Samples> queryPage(SamplesQueryDTO samplesQueryDTO);

    List<String> findFieldDistinct(SamplesQueryDTO samplesQueryDTO);

    /**
     * 查询所有不重复的lat_lon字段值
     *
     * @return lat_lon字段的不重复值列表
     */
    List<String> findDistinctLatLon(SamplesQueryDTO queryDTO);

    /**
     * 查询所有不重复的lat_lon字段值，同时返回水体类型信息
     *
     * @param queryDTO 查询条件
     * @return 包含经纬度和水体类型的数据列表
     */
    List<Map<String, Object>> findDistinctLatLonWithType(SamplesQueryDTO queryDTO);

    List<String> findFieldDistinct(String field, String keyword);

    Map<String, Long> getWaterBodyTypeByClassificationDistribution(SamplesQueryDTO queryDTO);

    Map<String, Long> getTechnologyTypeDistribution(SamplesQueryDTO queryDTO);

    Map<String, Long> getWaterBodyTypeByGeographicDistribution(SamplesQueryDTO queryDTO);

    List<Map<String, Object>> getHomeMapLocation(HomeMapQueryDTO homeMapQueryDTO);

    /**
     * 根据水文圈类型查询指定字段的不重复值
     *
     * @param field           要查询的字段
     * @param hydrosphereType 水文圈类型
     * @return 符合条件的不重复字段值列表
     */
    List<String> findFieldDistinctByHydrosphere(String field, String hydrosphereType);

    List<String> findDistinctRunId(SamplesQueryDTO queryDTO);
}
