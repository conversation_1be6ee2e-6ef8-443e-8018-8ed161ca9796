# -*- coding: utf-8 -*-

"""
处理1800篇基金论文数据
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import os

import pandas as pd
import requests
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import time_stat, trim_to_none, str_val
from custom_log_config import init_default_logger
from fill_mash_excel import to_int_num
from find_pmid_by_title import mongo_uri, mongo_db_name, HEADERS
from process_supp_table_publication import supp_collection_name, find_item_by_pmid_in_plosp, download_pdf_by_pmid, \
    all_result_filepath

logger = init_default_logger('process_fund_paper.log')

input_file = r'data/基金论文原始数据.xlsx'
pdf_dir = "data/excel/1800"


def read_all_pmid_of_excel() -> set[int]:
    df = pd.read_excel(all_result_filepath)
    # 转换nums列为数字
    # df['PMID'] = pd.to_numeric(df['PMID'], errors='coerce').astype('Int64')
    # 转为 float，并丢弃无效行
    df_cleaned = df[pd.to_numeric(df['PMID'], errors='coerce').notna()]
    # 再转为 int
    df_cleaned['PMID'] = df_cleaned['PMID'].astype(int)
    return set(df_cleaned['PMID'].dropna())


def add_new_row(collection: Collection):
    pmid_set = read_all_pmid_of_excel()
    logger.info(f'excel中已存在的pmid数量: {len(pmid_set)}')

    new_data = {
        'Source': [],
        'Authors': [],
        'Title': [],
        'Journal': [],
        'Year': [],
        'ProjectID': [],
        'PMID': [],
        'PDF2RAG': [],
        'DOI': [],
    }
    # results = collection.find({"$and": [{"pmid": {"$gte": 38862603}}, {"pmid": {"$lte": 38862703}}]})
    results = collection.find({})
    new_pmids = []
    for one_item in results:
        pmid = one_item.get('pmid', None)
        if pmid not in pmid_set:
            new_pmids.append(pmid)
            print(f'新pmid: {pmid}')
            new_data['Source'].append('MASH')

            new_data['Authors'].append(str_val(one_item, 'author'))
            new_data['Title'].append(str_val(one_item, 'title'))
            new_data['Journal'].append(str_val(one_item, 'journalTitle'))
            new_data['Year'].append(str_val(one_item, 'year'))
            new_data['ProjectID'].append(None)
            new_data['PMID'].append(str(pmid))

            pdf_info = one_item.get('pdf', None)
            has_pdf = True if pdf_info and pdf_info.get('path', None) else False
            new_data['PDF2RAG'].append('YES' if has_pdf else 'NO')
            new_data['DOI'].append(str_val(one_item, 'doi'))

    # 创建一个新的DataFrame
    new_df = pd.DataFrame(new_data)
    # 使用pd.concat()将两个DataFrame连接起来
    df = pd.read_excel(all_result_filepath)
    df = pd.concat([df, new_df], ignore_index=True)

    # 保存到Excel文件
    df.to_excel(all_result_filepath, index=False, engine='openpyxl')
    logger.info(f'更新后的文件已保存到: {all_result_filepath}')


def find_new_by_pmid(collection: Collection, _session: requests.Session):
    df = pd.read_excel(input_file)
    all_pmids = []
    for index, row in df.iterrows():
        pmid = trim_to_none(row.get('Pubmed Id'))
        if pmid:
            pmid = to_int_num(pmid)
            if not pmid:
                logger.error(f'行{index + 1}: {pmid} 不是数字')
                continue
            if pmid not in all_pmids:
                all_pmids.append(pmid)

    # query = {"pmid": {"$in": all_pmids}, "pdf_doc_content": {"$exists": False}}
    query = {"pmid": {"$in": all_pmids}}
    results = collection.find(query)

    saved_pmids = []
    for doc in results:
        pmid = doc.get('pmid', None)
        saved_pmids.append(pmid)

    i = 0
    for pmid in all_pmids:
        if pmid not in saved_pmids:
            one_item = find_item_by_pmid_in_plosp(pmid, _session, logger)
            if one_item:
                i += 1
                # if i == 3:
                #     print(float('aad23d'))
                # 存入MongoDB
                collection.insert_one(one_item)

                pdf_info = one_item.get('pdf', None)
                has_pdf = True if pdf_info and pdf_info.get('path', None) else False
                if has_pdf:
                    download_pdf_by_pmid(pmid, _session, logger, pdf_dir)

    logger.info(f'new_pmids数量: {i}')


@time_stat(logger)
def do_process():
    # 检查文件是否存在
    if not os.path.exists(input_file):
        logger.error(f'文件不存在: {input_file}')
        return

    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[supp_collection_name]
    _session = None
    try:
        _session = requests.session()
        _session.headers = HEADERS

        find_new_by_pmid(collection, _session)
        _session.close()

        add_new_row(collection)
    finally:
        client.close()
        if _session:
            _session.close()


if __name__ == '__main__':
    do_process()
