package org.biosino.lf.mash.mashweb.dto;

import cn.hutool.core.lang.RegexPool;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.lf.mash.mashweb.core.validator.ValidEnum;
import org.biosino.lf.mash.mashweb.enums.ExpertQaTypeEnum;


/**
 * 专家问答
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpertQaDTO extends CaptchaDTO {

    @ValidEnum(enumClass = ExpertQaTypeEnum.class, allowNull = false, message = "Question type error")
    private String selectedType;

    @NotBlank(message = "Question cannot be empty")
    @Size(max = 2500, message = "Question cannot exceed 2500 characters")
    private String question;

    @NotBlank(message = "Title cannot be empty")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    private String title;

    @NotBlank(message = "Name cannot be empty")
    @Size(max = 125, message = "Name cannot exceed 125 characters")
    private String name;

    @NotBlank(message = "Organization cannot be empty")
    @Size(max = 300, message = "Organization cannot exceed 300 characters")
    private String organization;

    @NotBlank(message = "Email cannot be empty")
    @Pattern(regexp = RegexPool.EMAIL, message = "Email format incorrect")
    @Size(max = 90, message = "Email cannot exceed 90 characters")
    private String email;


}
