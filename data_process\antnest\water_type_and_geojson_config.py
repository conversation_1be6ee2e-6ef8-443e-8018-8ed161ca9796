# -*- coding: utf-8 -*-

import copy

"""
GeoJSON的配置枚举

目前未配置的water_type
Groundwater
Wetlands
Atmosphere
Biota

fields数组必须包含三个元素，依次对应：类型type、英文名name、中文名
1. 河、2. 全球湖 3. 世界主要湖泊  4. 内部水域  5. 冰川  6. 南极冰架 7. Ocean
"""
geojson_files = [
    {
        'file_name': 'final_lakes',
        'fields': [None, 'Name', None],
        'des': '世界湖泊',
        'water_type': ['Freshwater lakes', 'Saline lakes'],
    },
    {
        'file_name': 'ne_final_rivers',
        'fields': [None, 'name_en', 'name_zh'],
        'des': '河流和湖泊中心线',
        'water_type': ['River', 'Rivers'],
    },
    # {
    #     'file_name': 'geoserver_internal_waters',
    #     'fields': ['pol_type', 'geoname', None],
    #     'des': '内部水域',
    # },
    {
        'file_name': 'ne_10m_glaciated_areas',
        'fields': ['featurecla', 'name', None],
        'des': '冰川',
        'water_type': ['Ice Caps & Glaciers'],
    },
    {
        'file_name': 'ocean',
        'fields': [None, 'name', None],
        # 海洋数据类型固定为 Ocean
        'fix_type': 'Ocean',
        'water_type': ['Oceans'],
        'des': '海洋',
    }
]


def get_water_type_name_map():
    map_info = {}
    for geo_item in geojson_files:
        geojson_file_name = geo_item.get('file_name', None)
        if geojson_file_name is None:
            continue

        water_type = geo_item.get('water_type', None)
        if water_type is None:
            continue

        for type_val in water_type:
            type_val_lower = type_val.lower()
            file_names = map_info.get(type_val_lower, None)
            if file_names is None:
                file_names = set()
            file_names.add(geojson_file_name)
            map_info[type_val_lower] = file_names

    return map_info


def get_file_name_map():
    map_info = {}
    for geo_item in geojson_files:
        file_name = geo_item.get('file_name', None)
        if file_name is None:
            continue

        map_info[file_name] = copy.deepcopy(geo_item)

    return map_info


def get_water_types_by_file_name(curr_file_name):
    water_type = None
    for geo_item in geojson_files:
        file_name = geo_item.get('file_name', None)
        if file_name is None:
            continue

        if file_name == curr_file_name:
            water_type = geo_item.get('water_type', None)
            break

    return water_type if water_type is not None else []
