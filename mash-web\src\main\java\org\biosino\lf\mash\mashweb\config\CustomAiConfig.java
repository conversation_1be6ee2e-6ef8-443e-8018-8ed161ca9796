package org.biosino.lf.mash.mashweb.config;

import org.springframework.ai.transformer.splitter.TextSplitter;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/3/12
 */
@Configuration
public class CustomAiConfig {

    /*@Bean
    public TransformersEmbeddingClient embeddingClient() {
        return TransformersEmbeddingClient.builder()
                .modelPath("path/to/all-MiniLM-L6-v2.onnx") // 本地 ONNX 模型路径
                .tokenizerPath("path/to/tokenizer.json")    // 分词器配置文件
                .build();
    }*/

    /*@Bean
    public EmbeddingModel embeddingModel() throws Exception {
//        TransformersEmbeddingClient embeddingClient = new TransformersEmbeddingClient();
//        embeddingClient.setTokenizerResource("classpath:/onnx/all-MiniLM-L6-v2/tokenizer.json");
//        embeddingClient.setModelResource("classpath:/onnx/all-MiniLM-L6-v2/model.onnx");
//        embeddingClient.setModelOutputName("token_embeddings");
//        embeddingClient.afterPropertiesSet();
        TransformersEmbeddingModel transformersEmbeddingModel = new TransformersEmbeddingModel();
        transformersEmbeddingModel.setTokenizerResource("D:\\Temp\\mash_data\\sentence-transformers\\all-MiniLM-L6-v2\\tokenizer.json");
        transformersEmbeddingModel.setModelResource("D:\\Temp\\mash_data\\sentence-transformers\\all-MiniLM-L6-v2\\onnx\\model.onnx");
        transformersEmbeddingModel.setModelOutputName("token_embeddings");
        transformersEmbeddingModel.afterPropertiesSet();
        return transformersEmbeddingModel;
    }*/


    @Bean
    public TextSplitter textSplitter(Environment environment) throws IOException {
        final String maxLengthKey = "spring.ai.embedding.transformer.tokenizer.options.maxLength";
        final String maxLengthStr = environment.getProperty(maxLengthKey);
        int maxLength = 512;
        if (maxLengthStr != null) {
            maxLength = Integer.parseInt(maxLengthStr);
        }

        // TokenTextSplitter 参数说明：
        // chunkSize: 每个文本块的目标token数量
        // minChunkSizeChars: 每个文本块的最小字符数
        // minChunkLengthToEmbed: 丢弃短于此长度的块
        // maxNumChunks: 从文本生成的最大块数
        // keepSeparator: 是否保留分隔符

        // 安全配置：确保不超过模型限制
        int chunkSize = maxLength - 10; // 留出10个token的安全边距
        int minChunkSizeChars = 100;    // 最小100个字符
        int minChunkLengthToEmbed = 10; // 最小10个字符才进行嵌入
        int maxNumChunks = 2000;        // 最多2000个块
        boolean keepSeparator = true;   // 保留分隔符

        // 按 Token 数量分块
        return new TokenTextSplitter(chunkSize, minChunkSizeChars, minChunkLengthToEmbed, maxNumChunks, keepSeparator);
    }

}
