import { casLoginRequest, getInfo, login, tokenLogout } from '@/api/login';
import { getToken, removeToken, setToken } from '@/utils/auth';
// import defAva from '@/assets/images/profile.jpg';
import { defineStore } from 'pinia';

const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
  }),
  getters: {
    logged() {
      return this.name && this.name !== '' && this.name !== undefined;
    },
  },
  // 开启数据缓存
  persist: {
    enabled: true,
  },
  actions: {
    // cas 单点登录
    casLogin() {
      return new Promise((resolve, reject) => {
        // 访问接口拿到用户信息和token
        casLoginRequest()
          .then(res => {
            console.log('登录了', res);
            setToken(res.token);
            this.token = res.token;
            this.id = res.userId;
            // setExpiresIn(res.expires_in)
            // this.expires_in = res.expires_in
            this.name = res.name;
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then(res => {
            let data = res.data;
            setToken(data.access_token);
            this.token = data.access_token;
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(res => {
            const user = res.user;
            // const avatar =
            // user.avatar == '' || user.avatar == null ? defAva : user.avatar;

            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.roles;
              this.permissions = res.permissions;
            } else {
              this.roles = ['ROLE_DEFAULT'];
            }
            this.id = user.userId;
            this.name = user.userName;
            // this.avatar = avatar;
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 退出系统
    // cas单点登出
    casLogout() {
      return new Promise((resolve, reject) => {
        // 删除后端的token
        tokenLogout(this.token)
          .then(() => {
            // 删除pinia的用户信息
            this.token = '';
            this.id = '';
            this.name = '';
            this.roles = [];
            this.permissions = [];
            // 删除cookie中的token
            removeToken();
            // 访问后台认证服务登出接口
            window.location.replace(import.meta.env.VITE_LOGOUT_URL);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
  },
});

export default useUserStore;
