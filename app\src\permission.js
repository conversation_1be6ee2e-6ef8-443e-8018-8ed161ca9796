import router from './router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';

NProgress.configure({ showSpinner: false });

// 白名单
// const whiteList = [
//   '/visualization',
//   '/home',
//   '/search',
//   '/analysis',
//   '/about',
//   '/submit',
//   '/manage',
//   '/fileManage',
// ];
//
// router.beforeEach((to, from, next) => {
//   NProgress.start();
//   // 获取cookie
//   if (getToken()) {
//     /* has token*/
//     if (to.path === '/login') {
//       next({ path: '/' });
//       NProgress.done();
//     } else {
//       next();
//       NProgress.done();
//     }
//   } else {
//     // 没有token
//     let pass = false;
//     whiteList.forEach(it => {
//       if (to.path.indexOf(it) !== -1) {
//         // 在免登录白名单，直接进入
//         pass = true;
//       }
//     });
//     if (pass) {
//       next();
//       NProgress.done();
//     } else {
//       useUserStore()
//         .casLogin()
//         .then(res => {
//           console.log(res);
//           next();
//           NProgress.done();
//         })
//         .catch(() => {
//           console.log('------loginerr-----');
//         });
//     }
//   }
// });
//
// router.afterEach(() => {
//   NProgress.done();
// });
