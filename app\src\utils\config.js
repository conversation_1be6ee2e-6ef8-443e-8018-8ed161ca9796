import { computed } from 'vue';
import moment from 'moment/moment';

export function getFileSize(size) {
  if (!size) {
    return '';
  }
  const num = 1024; //byte
  if (size < num) {
    return size;
  }
  if (size < Math.pow(num, 2)) {
    return (size / num).toFixed(2) + 'K';
  } //kb
  if (size < Math.pow(num, 3)) {
    return (size / Math.pow(num, 2)).toFixed(2) + 'M';
  } //M
  if (size < Math.pow(num, 4)) {
    return (size / Math.pow(num, 3)).toFixed(2) + 'G';
  } //G
  return (size / Math.pow(num, 4)).toFixed(2) + 'T'; //T
}

export const formatDatetime = computed(() => {
  return function (datetime) {
    if (datetime && datetime !== 'NA') {
      return moment(datetime).format('YYYY-MM-DD HH:mm:ss');
    } else {
      return datetime;
    }
  };
});

export const formatDate = computed(() => {
  return function (datetime) {
    if (datetime && datetime !== 'NA') {
      return moment(datetime).format('YYYY-MM-DD');
    } else {
      return datetime;
    }
  };
});
export const formatNum = computed(() => {
  return function (s, n) {
    n = n > 0 && n <= 20 ? n : 2;
    s = parseFloat((s + '').replace(/[^\d\\.-]/g, '')).toFixed(n) + '';
    const l = s.split('.')[0].split('').reverse();
    const r = s.split('.')[1];
    let t = '';
    for (let i = 0; i < l.length; i++) {
      t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? ',' : '');
    }
    if (r == '00' || !r) {
      return t.split('').reverse().join('');
    } else {
      return t.split('').reverse().join('') + '.' + r;
    }
  };
});
