package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.util.JwtUtils;
import org.springframework.http.MediaType;

/**
 * 公共控制层
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Slf4j
public abstract class BaseController {
    public static final String TOKEN_PREFIX = "Bearer ";


    //private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0";
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36";

    public static boolean validateLocalToken(String auth) {
        if (StrUtil.isBlank(auth) || !auth.startsWith(TOKEN_PREFIX)) {
            return false;
        }
        try {
            final String token = auth.substring(7);
            final Claims claims = JwtUtils.parseToken(token);
            if (claims == null) {
                return false;
            } else {
                return JwtUtils.TOKEN_VAL.equals(claims.get(JwtUtils.TOKEN_KEY));
            }
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    public static HttpRequest post(final String urlString, final String bodyJsonData,
                                   final String token) {
        return HttpRequest.post(urlString)
                .body(bodyJsonData).timeout(60 * 1000)
                .header("User-Agent", USER_AGENT, true)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .header("Authorization", TOKEN_PREFIX + token);
    }

}
