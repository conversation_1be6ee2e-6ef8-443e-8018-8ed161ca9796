# -*- coding: utf-8 -*-

"""
 脚本配置信息
 包含文件路径、常量等
 Author: 尚尉
 Date: 2025-01-07
 Version: 1.0.0
"""

import copy

# 当前激活的profile(AntNest、node)
CURR_ACTIVE_PROFILE = 'AntNest'

# 国家简称字典文件路径
COUNTRY_DICT_FILE = 'data/country.xlsx'

"""
 待处理的文件项目配置
 Author: 尚尉
 Date: 2024-12-25
 Version: 1.0.0
"""
process_configs = {
    'AntNest': {
        'all_file_path': 'data/node_new_sample.xlsx',
        # 'all_file_path': 'data/mash_all_samples.xlsx',
        'merge_columns': ['BioprojectID', 'Geo_loc_name', 'Biome'],
        'merge_id_column': 'BioprojectID',
        'main_process_input_filepath': 'data/node_new_sample_merge.xlsx',
        # 'main_process_input_filepath': 'data/mash_all_samples_merge.xlsx',
    },
    'node': {
        # 'all_file_path': 'data/node_all_samples.xlsx',
        'all_file_path': 'data/node_new_sample.xlsx',
        'merge_columns': ['BioprojectID', 'Geo_loc_name', 'Biome'],
        'merge_id_column': 'BioprojectID',
        'main_process_input_filepath': 'data/node_new_sample_merge.xlsx',
    },
}


def get_curr_process_item():
    process_item = process_configs.get(CURR_ACTIVE_PROFILE, None)
    if process_item is None:
        raise ValueError(f'未知的profile: {CURR_ACTIVE_PROFILE}')
    return copy.deepcopy(process_item)


def is_ant_nest():
    return CURR_ACTIVE_PROFILE == 'AntNest'
