# -*- coding: utf-8 -*-

"""
pdf下载
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import os
import shutil
import traceback
from time import sleep

import pandas as pd
import requests

from cust_utils import time_stat, str_val
from custom_log_config import init_default_logger
from fill_mash_excel import to_int_num
from fill_more_mash_excel import final_300_filepath
from find_pmid_by_title import HEADERS, api_token, base_api_url

# 初始化日志
logger = init_default_logger('download_pdf.log')

# 访问API配置
api_download_url = base_api_url + '/article/downPdfByPmid.do'

# HEADERS = {
#     "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
#     "Accept-Encoding": "gzip, deflate",
#     "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
#     "Cache-Control": "max-age=0",
#     "Connection": "keep-alive",
#     "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36",
# }

download_dir = "data/downloads"


def download_file_by_pmid(_session: requests.Session, pmid, output_dir, new_path_name):
    """
    根据PMID调用API下载文件。
    """
    pmid = int(float(pmid))
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"创建目录: {output_dir}")

    # 构造API URL和请求头
    url = api_download_url  # 从上下文中获取的API地址
    params = {
        'pmid': pmid,
        'token': api_token,
    }

    response = None
    file_path = None
    try:
        filename = f"{pmid}.pdf"

        # file_path = os.path.join(output_dir, filename)
        file_path = new_path_name if new_path_name else os.path.join(output_dir, filename)

        # 创建一个Request对象
        req = requests.Request('GET', url, params=params)
        # 准备请求
        prepared = req.prepare()
        # 打印完整的URL
        logger.info(f"完整的URL: {prepared.url}")

        # 发送GET请求
        response = _session.get(url, params=params, stream=True, timeout=(30, 360))
        response.raise_for_status()
        content_type = response.headers.get('Content-Type', '').lower()
        if 'application/json' in content_type:
            # 响应为JSON，解析错误信息
            error_info = response.json()
            logger.error(f"下载失败: {error_info}")
        else:
            # content_type为application/pdf，判断响应状态码
            if response.status_code == 200:
                # 成功，处理文件流
                # content_disposition = response.headers.get('Content-Disposition', '')
                # if 'filename=' in content_disposition:
                #     filename = content_disposition.split('filename=')[1].strip('"')
                # else:
                #     filename = f"{pmid}.pdf"

                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                logger.info(f"文件已保存: {file_path}")
            else:
                # 状态码错误，下载失败，解析JSON错误信息
                error_info = response.json()
                logger.error(f"下载失败: {error_info}，状态码：{response.status_code}")

    except Exception as e:
        logger.error(f"下载PDF请求异常: {str(e)}")
        logger.error("下载PDF请求异常详细信息: \n" + traceback.format_exc())
        if file_path and os.path.exists(file_path):
            # 删除文件
            os.remove(file_path)
    finally:
        if response:
            response.close()
        sleep(1.1)


@time_stat(logger)
def download_pdf():
    new_filepath = final_300_filepath
    # 检查文件是否存在
    if not os.path.exists(new_filepath):
        logger.error(f'文件不存在: {new_filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(new_filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {new_filepath}')
        return
    # 打印文件路径和列内容
    logger.info(f'处理文件：{new_filepath}')
    _session = None
    try:
        _session = requests.session()
        _session.headers = HEADERS

        for index, row in df.iterrows():
            has_pdf = str_val(row, '是否存在PDF')
            has_pdf = has_pdf.upper() if has_pdf else None

            project_id = to_int_num(str_val(row, '项目编号'))
            order_num = to_int_num(str_val(row, '序号'))
            if project_id is None or order_num is None:
                raise ValueError('项目编号或序号为空')

            new_path_name = f'{download_dir}/{project_id}.{str(order_num).zfill(2)}.pdf'

            pmid = str_val(row, 'pmid')
            if pmid and has_pdf == 'YES':
                pmid = to_int_num(pmid)
                file_path = f'{download_dir}/{pmid}.pdf'
                pdf_downloaded = False
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    # 若文件大于500字节，则认为文件已下载
                    pdf_downloaded = os.path.getsize(file_path) > 500
                    # 文件已移动并重命名
                    shutil.move(file_path, new_path_name)

                if not pdf_downloaded and os.path.exists(new_path_name) and os.path.isfile(new_path_name):
                    # 若文件大于500字节，则认为文件已下载
                    pdf_downloaded = os.path.getsize(new_path_name) > 500

                if pdf_downloaded:
                    logger.info(f'文件已存在：{new_path_name}')
                else:
                    file_path = new_path_name
                    logger.info(f'开始下载：{file_path}')
                    if os.path.exists(file_path):
                        # 删除旧文件
                        shutil.rmtree(file_path)
                    download_file_by_pmid(_session, pmid, download_dir, new_path_name)
    finally:
        if _session:
            _session.close()


if __name__ == '__main__':
    download_pdf()
