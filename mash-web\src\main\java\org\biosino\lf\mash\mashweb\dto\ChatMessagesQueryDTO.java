package org.biosino.lf.mash.mashweb.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * AI问答请求参数
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Data
public class ChatMessagesQueryDTO implements Serializable {

    @NotBlank(message = "Question cannot be empty")
    @Size(max = 2000, message = "Question cannot exceed 2000 characters")
    private String query;

    @NotBlank(message = "user ID cannot be empty")
    @Size(max = 200, message = "Question cannot exceed 200 characters")
    private String user;

    private String conversation_id;

    private AiInputs inputs = new AiInputs();

    @Data
    public static class AiInputs implements Serializable {
        private String qa_type;
    }

}
