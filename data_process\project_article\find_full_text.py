# -*- coding: utf-8 -*-

"""
调用plosp接口 获取pdf全文
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import re
from time import sleep

import requests
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import time_stat, trim_to_none
from custom_log_config import init_default_logger
from find_pmid_by_title import mongo_uri, mongo_db_name, mongo_collection_name, base_api_url, HEADERS, api_token

# 初始化日志
logger = init_default_logger('find_full_text.log')

# 访问API配置
api_extract_url = base_api_url + '/article/extractArticlePdf2Text.do'

# node id正则表达式
# node_id_pattern = r'(?:OEP|OEX|OES|OER|OED|OEZ)\d{6}(?:\d{2})?'
# node_id_pattern = r'\bOE[PXSRDZ]\d{6}(?:\d{2})?\b'
node_id_pattern = r"""
    \b                  # 单词边界，确保匹配完整独立ID
    (                   # 捕获组开始（提取完整ID）
        (?:OEP|OEX|OES|OER|OED|OEZ)  # 匹配指定前缀（非捕获分组）
        (?:             # 非捕获分组，匹配6位或8位数字
            \d{6}       # 6位数字
            |           # 或
            \d{8}       # 8位数字
        )
    )
    \b                  # 结束单词边界
"""
# 编译正则表达式（忽略空格和注释）
node_id_regex = re.compile(node_id_pattern, flags=re.VERBOSE)

# ncbi sra id 正则表达式
ncbi_sra_id_pattern = r"""
                \b  # 单词边界，确保匹配完整独立ID
                (
                    PRJ(NA|EB)\d+           # Project（如 PRJNA727023）
                    | (SRP|ERP|DRP)\d+      # Study（如 SRP000123）
                    | (SRS|ERS|DRS)\d+      # Sample（如 SRS1234567）
                    | (SRX|ERX|DRX)\d+      # Experiment（如 SRX000456）
                    | (SRR|ERR|DRR)\d+      # Run（如 SRR12345678）
                )
                \b  # 结束单词边界
            """
# 编译正则表达式（忽略空格和注释）
ncbi_sra_id_regex = re.compile(ncbi_sra_id_pattern, flags=re.VERBOSE)


def extract_by_pmid(_session: requests.Session, pmid: int, collection):
    params = {
        'pmid': pmid,
        'token': api_token,
    }

    response = _session.get(api_extract_url, params=params, timeout=50)

    response.raise_for_status()  # 检查请求是否成功
    json_data = response.json()
    status = json_data.get('status', '')
    msg = json_data.get('msg', '')
    if status == 'success':
        content = trim_to_none(json_data.get('content', ''))
        if content:
            # 根据pmid更新数据
            collection.update_one({'pmid': pmid}, {'$set': {'pdf_content': content}})
        else:
            logger.error(f'全文为空: {pmid}')
    else:
        logger.error(f'api查询出错: {pmid}，错误信息: {msg}')

    sleep(1.2)


def find_fulltext_by_plosp(collection: Collection):
    _session = None
    try:
        # 仅查询存在pdf的数据
        query = {"pdf.path": {"$exists": True}}
        projection = {"pmid": 1, "pdf_content": 1, "_id": 0}
        results = collection.find(query, projection)

        _session = requests.session()
        _session.headers = HEADERS

        # 遍历结果
        for doc in results:
            pmid = doc.get('pmid')
            pdf_content = trim_to_none(doc.get('pdf_content'))
            if not pdf_content:
                logger.info(f'开始补充全文信息，pmid: {pmid}')
                extract_by_pmid(_session, pmid, collection)
            else:
                logger.info(f'全文已存在，跳过补充，pmid: {pmid}')
    finally:
        if _session:
            _session.close()


def find_node_id(str_val: str) -> list[str]:
    str_val = trim_to_none(str_val)
    if str_val:
        matches = node_id_regex.findall(str_val)
        if matches:
            return list(set(matches))
        else:
            return []

        # 分词并提取节点 ID
        # words = re.split(r'\W+', str_val)
        # id_set = set()
        # for word in words:
        #     match = re.search(node_id_pattern, word)
        #     if match:
        #         id_set.add(match.group())
        # result = list(id_set)
        # result.sort()
        # return result
    else:
        return []


def find_ncbi_sra_id(str_val: str) -> list[str]:
    #  Project（项目） ：以PRJNA、 PRJEB开头（例如：PRJNA727023）。
    #  Study（研究项目） ：以SRP、ERP、DRP开头（例如：SRP000123）。
    #  Sample（样本） ：以SRS、ERS、DRS开头（例如：SRS1234567）。
    #  Experiment（实验） ：以SRX、ERX、DRX开头（例如：SRX000456）。
    #  Run（测序运行） ：以SRR、ERR、DRR开头（例如：SRR12345678）。
    # 前缀差异：SR（NCBI）、ER（EBI）、DR（DDBJ）分别对应不同数据库来源。
    str_val = trim_to_none(str_val)
    if str_val:
        # 提取所有匹配的SRA ID
        sra_ids = ncbi_sra_id_regex.findall(str_val)
        if sra_ids:
            # 合并结果（因分组捕获会返回元组，需取第一个元素）
            sra_ids = [match[0] for match in sra_ids]
            return list(set(sra_ids))
        else:
            return []
    else:
        return []


def parse_node(field_val: str, node_ids: [], ncbi_sra_ids: []):
    # 提取node ID
    node_ids_of_field = find_node_id(field_val)
    if node_ids_of_field and len(node_ids_of_field) > 0:
        for item in node_ids_of_field:
            node_ids.append(item)

    # # 提取ncbi sra id
    ncbi_sra_ids_of_field = find_ncbi_sra_id(field_val)
    if ncbi_sra_ids_of_field and len(ncbi_sra_ids_of_field) > 0:
        for item in ncbi_sra_ids_of_field:
            ncbi_sra_ids.append(item)


def find_node_or_ncbi_sra_id(collection: Collection, by_pmid: bool = False):
    logger.info('\n\n开始补充node id和ncbi sra id...')
    # query = {"pmid": {"$exists": True}}
    query = {}
    # projection = {"pmid": 1, "title": 1, "abs": 1, "pdf_doc_content": 1, "node_ids": 1, "ncbi_sra_ids": 1, "_id": 0}
    result_cursor = collection.find(query)
    parse_fields = ["title", "abs", "pdf_doc_content.abstract", "pdf_doc_content.body"]

    node_count = 0
    ncbi_sra_count = 0

    i = 0
    for doc in result_cursor:
        i += 1
        if i % 100 == 0:
            logger.info(f'已处理{i}条记录')

        project_id = doc.get('project_id')
        order_num = doc.get('order_num')
        pmid = doc.get('pmid')

        node_ids = []
        ncbi_sra_ids = []
        for field in parse_fields:
            if '.' in field:
                # 解析pdf_doc_content
                split = field.split('.')
                pdf_doc = doc.get(split[0], None)
                if not pdf_doc:
                    continue
                val_list = pdf_doc.get(split[1], None)
                if val_list and len(val_list) > 0:
                    for val in val_list:
                        if not val:
                            continue
                        parse_node(val, node_ids, ncbi_sra_ids)

            else:
                # 解析title、abs
                field_val = trim_to_none(doc.get(field, None))
                if not field_val:
                    continue
                parse_node(field_val, node_ids, ncbi_sra_ids)

        if len(node_ids) > 0:
            ids = list(set(node_ids))
            ids.sort()
            node_count += 1
            if by_pmid:
                collection.update_one({'pmid': pmid}, {'$set': {'node_ids': ids}})
            else:
                collection.update_one({'project_id': project_id, 'order_num': order_num}, {'$set': {'node_ids': ids}})

        if len(ncbi_sra_ids) > 0:
            ids = list(set(ncbi_sra_ids))
            ids.sort()
            ncbi_sra_count += 1

            if by_pmid:
                collection.update_one({'pmid': pmid}, {'$set': {'ncbi_sra_ids': ids}})
            else:
                collection.update_one({'project_id': project_id, 'order_num': order_num},
                                      {'$set': {'ncbi_sra_ids': ids}})

    logger.info(f'补充node id和ncbi sra id完成，存在node id:{node_count}，存在ncbi sra id:{ncbi_sra_count}')


@time_stat(logger)
def read_fulltext_by_plosp():
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[mongo_collection_name]
    try:
        # find_fulltext_by_plosp(collection)
        find_node_or_ncbi_sra_id(collection,True)
    finally:
        client.close()


if __name__ == '__main__':
    read_fulltext_by_plosp()
