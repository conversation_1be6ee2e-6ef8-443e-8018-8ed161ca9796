package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.constant.CacheConstants;
import org.biosino.lf.mash.mashweb.core.CustomCache;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.CaptchaDTO;
import org.biosino.lf.mash.mashweb.dto.ExpertQaDTO;
import org.biosino.lf.mash.mashweb.enums.ExpertQaTypeEnum;
import org.biosino.lf.mash.mashweb.enums.MailTemplateEnum;
import org.biosino.lf.mash.mashweb.mapstruct.ExpertQaStructMapper;
import org.biosino.lf.mash.mashweb.mongo.entity.ExpertInfo;
import org.biosino.lf.mash.mashweb.mongo.entity.ExpertQa;
import org.biosino.lf.mash.mashweb.mongo.repository.ExpertInfoRepository;
import org.biosino.lf.mash.mashweb.mongo.repository.ExpertQaRepository;
import org.biosino.lf.mash.mashweb.util.ThreadPoolUtil;
import org.biosino.lf.mash.mashweb.vo.ExpertQaVO;
import org.biosino.lf.mash.mashweb.vo.SelectVO;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 专家问答
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpertQaService {
    private final MailService mailService;
    private final AppConfig appConfig;
    private final CustomCache customCache;

    private final Environment environment;

    private final ExpertQaRepository expertQaRepository;
    private final ExpertInfoRepository expertInfoRepository;
    // 新增限流相关字段
    private static final int MAX_EMAILS_PER_15_MINUTES = 400;
    private static final Map<Long, AtomicInteger> rateLimitMap = new ConcurrentHashMap<>();


    public void ask(final ExpertQaDTO expertQaDTO, final HttpServletRequest request) {
        // 校验验证码
        checkCaptcha(expertQaDTO);

        final ExpertQaTypeEnum typeEnum = ExpertQaTypeEnum.valueOf(expertQaDTO.getSelectedType());
        final List<ExpertInfo> expertInfos = expertInfoRepository.findByLabelsIn(CollUtil.toList(typeEnum.getLabels()));
        if (CollUtil.isEmpty(expertInfos)) {
            throw new ServiceException("No experts found for the selected question type");
        }

        final List<ExpertInfo> experts = expertInfos.stream().filter(x -> x != null && StrUtil.isNotBlank(x.getEmail())).distinct().toList();
        if (CollUtil.isEmpty(experts)) {
            throw new ServiceException("No expert email found for the selected question type");
        }

        final ExpertQa qa = new ExpertQa();
        ExpertQaStructMapper.INSTANCE.copyToDb(expertQaDTO, qa);
        qa.setIp(JakartaServletUtil.getClientIP(request));
        qa.setCreateDate(new Date());
        qa.setUpdateDate(qa.getCreateDate());
        qa.setStatus("create");
        expertQaRepository.save(qa);

        // 在实际发送邮件前再检查全局限流
        /*if (!checkRateLimit()) {
            qa.setStatus("rate_limited");
            qa.setUpdateDate(new Date());
            qa.setMsg("系统繁忙，邮件发送受限");
            expertQaRepository.save(qa);
            throw new ServiceException("System busy");
        }*/

        ThreadPoolUtil.getExecutor().execute(() -> sendEmail(qa, experts));
    }

    private boolean checkRateLimit() {
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime / (15 * 60 * 1000) * (15 * 60 * 1000);

        // 清理过期窗口
        rateLimitMap.entrySet().removeIf(entry ->
                entry.getKey() < windowStart - (15 * 60 * 1000));

        AtomicInteger counter = rateLimitMap.computeIfAbsent(windowStart, k -> new AtomicInteger(0));

        if (counter.get() >= MAX_EMAILS_PER_15_MINUTES) {
            log.warn("邮件发送超过15分钟400次限制，当前时间窗口: {}",
                    new Date(windowStart));
            return false;
        }

        counter.incrementAndGet();
        return true;
    }

    /**
     * 校验验证码
     */
    public void checkCaptcha(CaptchaDTO dto) {
        final boolean captchaEnabled = appConfig.isCaptchaEnabled();
        if (captchaEnabled) {
            final String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StrUtil.trimToEmpty(dto.getCaptchaId());
            final String captcha = (String) customCache.getCacheObject(verifyKey);
            if (captcha == null) {
                // 验证码过期
                throw new ServiceException("The verification code has expired");
            }
            customCache.deleteObject(verifyKey);
            if (!captcha.equalsIgnoreCase(dto.getCaptchaCode())) {
                // 验证码错误
                throw new ServiceException("Verification code error");
            }
        }
    }

    private void sendEmail(final ExpertQa qa, final List<ExpertInfo> experts) {
        qa.setStatus("sending");
        qa.setUpdateDate(new Date());
        expertQaRepository.save(qa);

        final String content = initEmailContent(qa);

        boolean sendSuccess = false;
        /*final Set<String> allEmails = new HashSet<>();
        for (ExpertInfo expertInfo : experts) {
            final String email = expertInfo.getEmail();
            if (email == null || allEmails.contains(email)) {
                // 已发送过的邮箱地址，不再重复发送
                continue;
            }

            if (sendToExpert(expertInfo, content, qa.getEmail())) {
                sendSuccess = true;
            }
            allEmails.add(email);
        }*/
        // 发送给管理员时，仅需要发送一封邮件
        final ExpertInfo expertInfo = experts.get(0);
        final String email = expertInfo.getEmail();
        if (email == null) {
            return;
        }
        if (sendToExpert(expertInfo, content, qa.getEmail())) {
            sendSuccess = true;
        }

        if (sendSuccess) {
            // mailService.sendBccEmail(emails.toArray(new String[]{}), null, subject, content, null);
            qa.setStatus("success");
            expertQaRepository.save(qa);
        } else {
            qa.setStatus("fail");
            qa.setUpdateDate(new Date());
            qa.setMsg("所有专家发送失败");
            expertQaRepository.save(qa);
            log.error("Send MASH Expert Q&A email error, 发送记录id: {}", qa.getId());
        }
    }

    private synchronized boolean sendToExpert(ExpertInfo expertInfo, final String content, final String askEmail) {
        try {
            // 获取专家姓名（去除逗号后面的职称）
            /*String name = expertInfo.getName();
            int titleIndex = name.indexOf(",");
            if (titleIndex > -1) {
                name = StrUtil.trimToEmpty(name.substring(0, titleIndex));
            }*/

            // 抄送人
            final List<String> ccEmails = appConfig.getCcEmails();
            final String[] cc = CollUtil.isEmpty(ccEmails) ? null : ccEmails.stream().distinct().toArray(String[]::new);

            // 收件人
            final String adminEmail = appConfig.getAdminEmail();
            String toEmail;
            if (StrUtil.isNotBlank(adminEmail)) {
                toEmail = adminEmail;
            } else {
                toEmail = "<EMAIL>";
            }
            // 开发/测试环境，发送给指定邮箱
            /*final String[] activeProfiles = environment.getActiveProfiles();
            if (ArrayUtil.containsAny(activeProfiles, "dev", "test")) {
                toEmail = "<EMAIL>";
            } else {
                toEmail = expertInfo.getEmail();
            }*/

            // 发送HTML邮件，使用StrUtil.format将专家名字加入邮件中，并抄送给指定邮箱
//            mailService.sendHtmlMail(toEmail, "【MASH项目-Expert Q&A专家知识问答模块】邀请您协助解答用户提出的问题", StrUtil.format(content, name), cc);
            // 发送HTML邮件，发给管理员，不添加专家名，同时抄送给指定邮箱
            mailService.sendHtmlMail(toEmail, "【MASH项目-Expert Q&A专家知识问答模块】邀请您协助解答用户提出的问题", content, cc);
            log.info("MASH Expert Q&A email sent successfully to {} for email: {}", expertInfo.getEmail(), askEmail);
            // 单发邮件时，防止频繁发送邮件
            ThreadUtil.safeSleep(10L * 1000);
            return true;
        } catch (Exception e) {
            log.error("Failed to send MASH Expert Q&A email: {}", expertInfo.getEmail(), e);
            return false;
        }
    }

    private String initEmailContent(final ExpertQa qa) {
        // 对java bean的String字段进行HTML转义，防止XSS攻击
        // final ExpertQa escape = escapeBean(ExpertQaStructMapper.INSTANCE.copyDb(qa));

        // 使用FreeMarker读取HTML邮件模板（.ftlh）时会自动进行html转义
        final ExpertQa escape = ExpertQaStructMapper.INSTANCE.copyDb(qa);

        //  获取问答类型名称
        final String selectedType = escape.getSelectedType();
        if (selectedType != null) {
            final ExpertQaTypeEnum typeEnum = ExpertQaTypeEnum.findByName(selectedType).orElseThrow(() -> new ServiceException("Unknown question type！"));
            escape.setSelectedTypeText(typeEnum.getText());
        }

        // 构建多行问答内容
        final String[] split = escape.getQuestion().split("\n");
        if (ArrayUtil.isNotEmpty(split)) {
            final List<String> questionParagraph = new ArrayList<>();
            for (String s : split) {
                s = StrUtil.trimToNull(s);
                if (s != null) {
                    questionParagraph.add(s);
                }
            }
            escape.setQuestionParagraphs(questionParagraph);
        }
        return mailService.getTemplateContent((JSONObject) JSON.toJSON(escape), MailTemplateEnum.expert_qa);
    }


    /**
     * 对java bean的String字段进行HTML转义，防止XSS攻击
     */
    public static <T> T escapeBean(T bean, String... ignoreFields) {
        return BeanUtil.edit(bean, (field) -> {
            if (ignoreFields != null && ArrayUtil.containsIgnoreCase(ignoreFields, field.getName())) {
                // 不处理忽略的Fields
                return field;
            }
            if (String.class.equals(field.getType())) {
                // 只有String的Field才处理
                final String val = (String) ReflectUtil.getFieldValue(bean, field);
                if (val != null) {
                    final String escape = HtmlUtils.htmlEscape(val);
                    if (!val.equals(escape)) {
                        // 仅处理改变的值
                        ReflectUtil.setFieldValue(bean, field, escape);
                    }
                }
            }
            return field;
        });
    }

    public ExpertQaVO initInfo() {
        final List<ExpertInfo> expertInfos = expertInfoRepository.findAll(Sort.by(Sort.Direction.ASC, "order"));
        if (CollUtil.isNotEmpty(expertInfos)) {
            // 去除敏感信息
            for (ExpertInfo expertInfo : expertInfos) {
                expertInfo.setEmail(null);
                expertInfo.setOrder(null);
            }
        }

        final ExpertQaVO vo = new ExpertQaVO();
        vo.setExpertInfos(expertInfos);

        final ExpertQaTypeEnum[] enums = ExpertQaTypeEnum.values();
        final List<SelectVO> questionTypes = new ArrayList<>();
        for (int i = 0; i < enums.length; i++) {
            final ExpertQaTypeEnum item = enums[i];
            final SelectVO selectVO = new SelectVO();
            selectVO.setLabel(item.getText());
            selectVO.setValue(item.name());
            selectVO.setDescription(item.getDescription());
            if (i == 0) {
                selectVO.setSelected(true);
            }
            questionTypes.add(selectVO);
        }

        vo.setQuestionTypes(questionTypes);
        return vo;
    }
}
