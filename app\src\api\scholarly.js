import request from '@/utils/request';

// 文献列表
export function scholarlyList(data) {
  return request({
    url: `/scholarly_archive/list`,
    method: 'post',
    data: data,
  });
}

// 筛选项下拉框
export function scholarlySelectItem() {
  return request({
    url: `/scholarly_archive/selectItem`,
    method: 'get',
  });
}

// 热点检索词
export function findHotWordAndMostRecent() {
  return request({
    url: `/scholarly_archive/findHotWordAndMostRecent`,
    method: 'get',
  });
}
