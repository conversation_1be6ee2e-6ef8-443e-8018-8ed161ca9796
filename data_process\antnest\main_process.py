# -*- coding: utf-8 -*-

"""
# 治理蚁巢数据大部分治理
# 国家、采样基质、关键带 等字段
# 水体数据[单条处理逻辑，可用来补充部分少量缺失数据，批量建议采用water_body_batch_*脚本]
# Author: LRJ
# Date: 2024-12-03
# Version: 1.0.0
"""

import json

import pandas as pd
from geopy.geocoders import Nominatim
from zhipuai import ZhipuAI

from cust_utils import time_stat, load_data, write_result_data
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item, COUNTRY_DICT_FILE, is_ant_nest

# 获取当前配置项
curr_process_item = get_curr_process_item()
# 输入文件
filepath = curr_process_item.get('main_process_input_filepath')
# 保存处理后的文件
output_path = filepath
# output_path = filepath.replace(".xlsx", "") + "_Processed_v1.xlsx"

# 配置日志
log_file_name = 'main_process.log'
logger = init_default_logger(log_file_name)


# 项目启动入口
@time_stat(logger)
def main():
    logger.info("main_process 开始处理")
    # 加载国家字典
    country_dict = load_country_dict(COUNTRY_DICT_FILE)
    if not country_dict:
        logger.error("No country data available, aborting process.")
        return

    # 加载Excel数据
    df = load_data(filepath)

    df = convert_lat_lon(df)
    df = process_country(df, country_dict)
    # if is_ant_nest():
    #     # 治理国家、采样基质、关键带数据（按需放开注释执行）
    #     df = process_sampling_substrate(df)
    #     df = process_country(df, country_dict)
    #     df = process_critical_zone(df)
    # else:
    #     # 如果是NODE的数据，则需要经纬度转换
    #     df = convert_lat_lon(df)

    # 填充水体类型和名称，调用大模型处理
    # df = process_water_body(df)

    output_file = write_result_data(df, output_path)
    logger.info("Processed data saved to %s", output_file)


# Sampling Substrate（采样基质）
def process_sampling_substrate(df):
    logger.info("Processing Sampling Substrate column.")

    def infer_substrate(organism):
        if not pd.isnull(organism):
            organism_lower = organism.lower()
            # if "water" in organism_lower or "lake" in organism_lower or "aquatic" in organism_lower or "spring" in organism_lower or "marine" in organism_lower or "estuary" in organism_lower or "glacier" in organism_lower:
            #     return "Water"
            # elif "soil" in organism_lower:
            #     return "Soil"
            # elif "sediment" in organism_lower or "mud" in organism_lower:
            #     return "Sediment"
            # elif "air" in organism_lower:
            #     return "Air"

            # 新版采样基质要求
            if "soil" in organism_lower:
                return "Soil"
            elif "sediment" in organism_lower:
                return "Sediment"
            else:
                return "Water"
        return "unspecified"

    df['Sampling Substrate'] = df['Organism'].apply(infer_substrate)
    logger.info("Sampling Substrate processing completed.")
    return df


# 读取国家字典文件（包含国家简称和全名）
def load_country_dict(country_dict_file_path):
    try:
        country_df = pd.read_excel(country_dict_file_path)
        country_dict = dict(zip(country_df['CountryCode'].str.upper(), country_df['CountryName']))
        logger.info("Country dictionary loaded successfully.")
        return country_dict
    except Exception as e:
        logger.error(f"Error loading country dictionary: {e}")
        return {}


# 使用Geopy的Nominatim API通过经纬度查询国家
geolocator = Nominatim(user_agent="geoapi")


def infer_country(lat_lon):
    # if not pd.isnull(lat_lon):
    #     try:
    #         location = geolocator.reverse(lat_lon, language='en', timeout=10)
    #         # 控制API调用频率，避免请求过于频繁
    #         time.sleep(300)
    #         if location and 'country' in location.raw['address']:
    #             return location.raw['address']['country']
    #     except Exception as e:
    #         logger.error("Error in geocoding: %s", str(e))
    return "unspecified"


# 通过Geo_loc_name填充Country列
def process_country(df, country_dict):
    logger.info("Processing Country column using Geo_loc_name matching.")

    # 缓存字典，用于存储已处理过的经纬度和对应的国家
    lat_lon_cache = {}

    # 为每行Geo_loc_name查找匹配的国家并填充
    for index, row in df.iterrows():
        # 如果Country列的值不为空且不为'unspecified'，跳过
        if pd.notnull(row['Country']) and row['Country'].strip().lower() != 'unspecified':
            logger.info(f"Row {index + 1}: Skipping processing, Country is '{row['Country']}'")
            continue

        geo_loc_name = str(row['Geo_loc_name']).strip().lower()
        matched_country = None

        # 首先尝试通过Geo_loc_name匹配国家简称
        for country_code, country_name in country_dict.items():
            if country_code.lower() in geo_loc_name:
                matched_country = country_name
                break

        # 如果通过Geo_loc_name没有匹配到国家，再通过经纬度查询
        if not matched_country:
            lat_lon = row.get('Lat_lon', None)

            if pd.notnull(lat_lon) and lat_lon != "" and lat_lon != "无":
                # 查找缓存
                if lat_lon in lat_lon_cache:
                    matched_country = lat_lon_cache[lat_lon]
                    logger.info(f"Row {index + 1}: Found country {matched_country} from cache.")
                else:
                    matched_country = infer_country(lat_lon)
                    # 将结果缓存
                    lat_lon_cache[lat_lon] = matched_country
                    logger.info(
                        f"Row {index + 1}: Geo_loc_name match failed, found country {matched_country} using Lat/Lon.")
            else:
                matched_country = 'unspecified'
                logger.info(f"Row {index + 1}: No Lat/Lon available, country set to 'unspecified'.")

        # 填充Country列
        df.at[index, 'Country'] = matched_country
        logger.info(f"Row {index + 1}: Matched Country：{matched_country}")

    logger.info("Country column processing completed.")
    return df


# 调用 ZhipuAI 大模型 提取水体名称
def extract_water_body_name(context):
    try:
        # 设置 ZhipuAI API 密钥
        client = ZhipuAI(api_key="da66e988dcb81b5c4660eabd54dfc017.YOC7UrSlvgNbDHW1")  # LRJ的私人密钥，请勿泄露或另作他用

        search_prompt = f"""
        ## Goals
        - 从描述性文本中准确识别出水体类型和水体名称。
        
        ## Constrains
        - 必须识别以下类别的实体：水体类型、水体名称。
        - 水体类型必须属于以下类别之一：Oceans, Ice Caps & Glaciers, Groundwater, Freshwater lakes, Wetlands, Soil moisture, Rivers, Atmosphere, Biota, Saline lakes, Inland Seas。
        - 温泉的水体类型是Groundwater。
        - 如果描述性文本中出现Hypersaline和Saline关键词，则水体类型是Saline lakes。
        - 如果无法识别水体名称可根据经纬度地理信息推测，尽量提供官方的标准名称。
        - 水体名称请提供具体地点的水体名称，不能是Mangrove swamp等非具体地点数据，可以是自然保护区名称。
        - 输出内容仅限于水体类型和水体名称，且只提供最匹配的实体。
        - 如果无法识别，回答“unspecified”。
        
        ## Example 
        - 请严格按照如下格式仅输出JSON，不要返回多余信息。
        {{
          "水体类型": "Rivers",
          "水体名称": "Pearl River"
        }}
        
        ## Workflow
        - 读取并分析文本内容。
        - 识别并输出水体类型和水体名称。
        
        ## Initialization
        {context}
        """

        response = client.chat.completions.create(
            model="glm-4-plus",  # 收费
            # model="glm-4-flash",  # 免费
            messages=[
                {"role": "system",
                 "content": "水体类型与水体名称识别专家"},
                {"role": "user",
                 "content": search_prompt}
            ],
            temperature=0.1,  # [0, 1] 值越大，生成的文本越随机，值越小，生成的文本越稳定；
            tools=[{"type": "web_search", "web_search": {"search_result": True}}],
            stream=True,
        )
        water_body_result = ""
        for chunk in response:
            water_body_result += chunk.choices[0].delta.content

        water_body_result = water_body_result.replace("```json", "").replace("```", "")

        # 解析 JSON 字符串
        data = json.loads(water_body_result)

        water_body_type = data["水体类型"]
        water_body_name = data["水体名称"]

        if water_body_name.lower() == "mangrove swamp":
            water_body_name = "unspecified"

        return water_body_type, water_body_name
    except Exception as e:
        logger.error("Error calling ZhipuAI API: %s", str(e))
        return "unspecified", "unspecified"


# 处理 Water_Body_Type 和 Water_Body_Name 字段
def process_water_body(df):
    logger.info("Processing Water Body Name and Water_Body_Name column using ZhipuAI.")

    for index, row in df.iterrows():
        current_value = row.get('Water Body Name', None)
        # 如果有数据则跳过
        # if pd.isnull(current_value) or current_value.strip().lower() == "unspecified":
        if pd.isnull(current_value):
            # 合并上下文：只取非空值字段并排除"无"
            context_fields = {
                'Geo_loc_name': "Geographical location name:",
                'Sample_title': "Sample title:",
                'Sample_description': "Sample description:",
                'BioProject_description': "Project description:",
                'BioProject_name': "Project name:",
                'BioProject_title': "Project title:",
                'Biome': "Biome:",
                'Lat_lon': "Latitude and longitude:"
            }

            # 拼接上下文，前面添加字段名称
            context = "; ".join([
                f"{context_fields[field]} {str(row[field])}"
                for field in context_fields if pd.notnull(row[field]) and row[field] != "" and row[field] != "无"
            ])

            # 调用模型提取水体名称
            logger.info("输入文本：" + context)
            water_body_type, water_body_name = extract_water_body_name(context)

            df.at[index, 'Water Body Type'] = water_body_type
            df.at[index, 'Water Body Name'] = water_body_name

            # 记录进度
            logger.info(
                f"Processed row {index + 1}/{len(df)}: water_body_type:'{water_body_type}'   water_body_name:'{water_body_name}'")

            # if index % 100 == 0:  # 每处理100条就保存一次，防止中断
            # df.to_excel(output_path, index=False)
            # 控制调用频率，避免超出限额
            # time.sleep(1)

        else:
            logger.info(f"Skipped row {index + 1}/{len(df)}: Water Body Name already specified as '{current_value}'")

    logger.info("Water_Body_Name processing completed.")
    return df


# 处理 Critical Zone 列
def process_critical_zone(df):
    logger.info("Processing Critical Zone column based on Biome keywords.")

    # 定义关键字
    critical_keywords = ['Mangrove', 'Coastal', 'estuary', 'Swamp', 'Marsh', 'Flooded flat', 'Faline',
                         'Salt marsh', 'Tidal flat', 'Delta']

    # 遍历 DataFrame 中的每一行
    for index, row in df.iterrows():
        biome_value = str(row['Biome']).lower()  # 获取 Biome 列的值并转为小写

        # 检查 Biome 是否包含关键字
        if any(keyword.lower() in biome_value for keyword in critical_keywords):
            df.at[index, 'Critical Zone'] = 'IN'  # 包含关键字，填充 IN
        else:
            df.at[index, 'Critical Zone'] = 'OUT'  # 不包含关键字，填充 OUT

        logger.info(f"Row {index + 1}: Biome = {biome_value}, Critical Zone = {df.at[index, 'Critical Zone']}")

    logger.info("Critical Zone column processing completed.")
    return df


def convert_lat_lon(df):
    """
    将“22.5255 N, 114.026 E”格式的纬度和经度转换为有符号浮点格式。
    自动处理经纬度位置对调、符号错误或数值超出范围的问题。
    """
    count_num = 0
    for index, row in df.iterrows():
        lat_lon_curated = row.get('lat_lon_curated', None)
        if pd.isnull(lat_lon_curated):
            count_num += 1
            continue
        else:
            has_curated = True

        if (not has_curated) and count_num > 200:
            logger.info("lat_lon_curated列不存在")
            break

        lat_lon = str(lat_lon_curated).lower()

        if pd.isnull(lat_lon) or lat_lon == 'nan':
            df.at[index, 'Lat_lon'] = "无"
            continue

        lat_lon = lat_lon.replace("，", "").replace(",", "")
        # Split the string into parts by space
        parts = lat_lon.split(" ")

        if len(parts) != 4:
            df.at[index, 'Lat_lon'] = "格式错误"
            continue

        try:
            # Extract initial values
            lat = float(parts[0])
            lat_dir = parts[1]
            lon = float(parts[2])
            lon_dir = parts[3]

            # Step 1: Check if latitude and longitude are swapped
            if lat_dir.lower() in ["e", "w"] and lon_dir.lower() in ["n", "s"]:
                # Swap latitude and longitude
                lat, lon = lon, lat
                lat_dir, lon_dir = lon_dir, lat_dir

            # Step 2: Apply signs based on directions
            if lat_dir.lower() == "n":
                latitude = lat
            elif lat_dir.lower() == "s":
                latitude = -lat
            else:
                # Invalid latitude direction, skip this row
                df.at[index, 'Lat_lon'] = "方向错误，未能修正"
                continue

            if lon_dir.lower() == "e":
                longitude = lon
            elif lon_dir.lower() == "w":
                longitude = -lon
            else:
                # Invalid longitude direction, skip this row
                df.at[index, 'Lat_lon'] = "方向错误，未能修正"
                continue

            # Step 3: Validate ranges
            if not (-90 <= latitude <= 90 and -180 <= longitude <= 180):
                df.at[index, 'Lat_lon'] = f"{longitude}, {latitude}"
                continue

            # Step 4: Assign the corrected latitude and longitude
            df.at[index, 'Lat_lon'] = f"{latitude}, {longitude}"

        except Exception as e:
            # Catch any unexpected errors and mark as error
            df.at[index, 'Lat_lon'] = f"错误: {str(e)}"

    return df


if __name__ == "__main__":
    main()
