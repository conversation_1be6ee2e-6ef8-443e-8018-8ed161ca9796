import re
from collections import OrderedDict

import numpy as np
import pandas as pd
from pymongo import MongoClient


def extract_lat_lon(lat_lon_str):
    """
    从Lat_lon字符串中提取经纬度
    正确格式例如: "44.337000, 143.380833"
    返回元组 (latitude, longitude)
    """
    if pd.isna(lat_lon_str) or lat_lon_str == "无":
        return None, None

    # 尝试解析格式为"纬度, 经度"的字符串
    try:
        # 移除所有空格，然后按逗号分割
        parts = re.sub(r'\s+', '', str(lat_lon_str)).split(',')
        if len(parts) == 2:
            lat = float(parts[0])
            lon = float(parts[1])
            return lat, lon
    except (ValueError, TypeError):
        pass

    return None, None


def extract_range_values(value_str):
    """
    从字符串中提取数值范围
    支持的格式：
    - 单个数值: "12.5"
    - 范围: "0.1-2", "0.1~2", "0.1-2.5"
    - 大于小于: ">200", "<10"
    返回元组 (start_value, end_value)
    """
    if pd.isna(value_str) or value_str == "无":
        return None, None

    value_str = str(value_str).strip()

    # 尝试直接转换为浮点数
    try:
        value = float(value_str)
        return value, None
    except ValueError:
        pass

    # 尝试解析范围格式 (如 "0.1-2" 或 "0.1~2")
    range_pattern = r'([-+]?\d*\.?\d+)\s*[-~]\s*([-+]?\d*\.?\d+)'
    match = re.match(range_pattern, value_str)
    if match:
        start = float(match.group(1))
        end = float(match.group(2))
        return start, end

    # 尝试解析大于小于格式 (如 ">200" 或 "<10")
    gt_pattern = r'>\s*([-+]?\d*\.?\d+)'
    lt_pattern = r'<\s*([-+]?\d*\.?\d+)'

    gt_match = re.match(gt_pattern, value_str)
    if gt_match:
        start = float(gt_match.group(1))
        return start, None

    lt_match = re.match(lt_pattern, value_str)
    if lt_match:
        end = float(lt_match.group(1))
        return None, end

    return None, None


def main():
    # MongoDB连接信息
    mongo_uri = "**********************************************************************"
    db_name = "mash"
    collection_name = "samples"

    # 连接到MongoDB
    client = MongoClient(mongo_uri)
    db = client[db_name]
    collection = db[collection_name]

    # 清空集合
    collection.delete_many({})

    excel_file = r"D:\Documents\WeChat Files\wxid_txw829zp38x122\FileStorage\File\2025-06\MASH_Merge_250616.xlsx"

    print(f"正在读取Excel文件: {excel_file}")

    # 读取Excel文件
    df = pd.read_excel(excel_file)

    # 定义列名映射字典 - 使用OrderedDict以保持顺序
    column_mapping = OrderedDict([
        ('RunID', 'run_id'),
        ('Run_status', 'run_status'),
        ('BioprojectID', 'bio_project_id'),
        ('BioProject_name', 'bio_project_name'),
        ('BioProject_title', 'bio_project_title'),
        ('BioProject_description', 'bio_project_description'),
        ('BioProject_status', 'bio_project_status'),
        ('BioSampleID', 'bio_sample_id'),
        ('Sample_title', 'sample_title'),
        ('Sample_description', 'sample_description'),
        ('BioSample_status', 'bio_sample_status'),
        ('Data Source', 'data_source'),
        ('Organism', 'organism'),
        ('tOmics_type', 'omics_type'),
        ('Technology_type', 'technology_type'),
        ('ExperiemntID', 'experiment_id'),
        ('Experiemnt_status', 'experiment_status'),
        ('Lat_lon', 'lat_lon'),
        ('Geo_loc_name', 'geo_loc_name'),
        ('Temperature', 'temperature'),
        ('Salinity', 'salinity'),
        ('Depth', 'depth'),
        ('Pressure', 'pressure'),
        ('ph', 'ph'),
        ('Biome', 'biome'),
        ('Biome_tag', 'biome_tag'),
        ('项目_tag', 'project_tag'),
        ('MASH-Lake', 'mash_lake'),
        ('Mash-Ocean-V2', 'mash_ocean_v2'),
        ('Tara-Ocean', 'tara_ocean'),
        ('MASH-ChinaSea', 'mash_china_sea'),
        ('MEER', 'meer'),
        ('Sampling Substrate', 'sampling_substrate'),
        ('Critical Zone', 'critical_zone'),
        ('Country', 'country'),
        ('Hydrosphere Type', 'hydrosphere_type'),
        ('Water Body Type (By Classification)', 'water_body_type_by_classification'),
        ('Water Body Type (By Geographic)', 'water_body_type_by_geographic'),
        ('Water Body Name', 'water_body_name'),
        ('Water Body Type', 'water_body_type')
    ])

    # 重命名列
    df = df.rename(columns=column_mapping)

    # 只保留映射表中的字段
    valid_columns = list(column_mapping.values())
    existing_valid_columns = [col for col in valid_columns if col in df.columns]
    df = df[existing_valid_columns]

    # 打印转换后的列名
    print("转换后的列名:")
    for col in df.columns:
        print(f"  - {col}")

    print(f"总共保留了{len(df.columns)}个字段")

    # 将DataFrame中的NaN值和"无"值替换为None (MongoDB中的null)
    df = df.replace({np.nan: None, '无': None})

    # 需要处理范围的字段列表
    range_fields = ['temperature', 'salinity', 'depth', 'pressure', 'ph']

    # 转换DataFrame为字典列表，确保字段顺序一致
    records = []
    for _, row in df.iterrows():
        record = OrderedDict()
        for field in existing_valid_columns:
            value = row.get(field)
            # 确保NaN值和'无'值被转换为None
            if pd.isna(value) or value == '无':
                value = None
            # 特殊处理 bio_project_id 字段
            if field == 'bio_project_id' and value is not None and value == '-':
                value = None

            # 将所有非None值转换为字符串
            if value is not None:
                value = str(value)

            record[field] = value

            # 特殊处理 lat_lon 字段
            if field == 'lat_lon' and value is not None:
                latitude, longitude = extract_lat_lon(value)
                if latitude is not None and longitude is not None:
                    record['latitude'] = float(latitude)
                    record['longitude'] = float(longitude)

            # 处理范围字段
            if field in range_fields and value is not None:
                start_value, end_value = extract_range_values(value)
                if start_value is not None:
                    record[f'{field}_start'] = start_value
                if end_value is not None:
                    record[f'{field}_end'] = end_value

        # 添加field_count字段，统计不为None的字段数量
        non_none_fields = sum(1 for v in record.values() if v is not None)
        record['field_count'] = non_none_fields

        # 去除所有值为 None 的字段
        record = OrderedDict((k, v) for k, v in record.items() if v is not None)
        records.append(record)

    print(f"共读取{len(records)}条记录，开始排序...")

    # 对记录进行排序：先按data_source排序，再按field_count降序排序，最后按bio_project_id排序
    # 使用None作为默认值处理可能不存在的键
    records = sorted(records, key=lambda x: (
        x.get('data_source', ''),
        -x.get('field_count', 0),  # 负号表示降序排序
        x.get('bio_project_id', '')
    ))

    print(f"排序完成，开始导入MongoDB...")

    # 批量插入到MongoDB
    if records:
        collection.insert_many(records)

    print(f"导入完成! 总共导入{len(records)}条记录到'{collection_name}'集合。")


if __name__ == "__main__":
    main()
