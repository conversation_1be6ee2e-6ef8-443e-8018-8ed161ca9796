<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>MASH Map Demo</title>
    <link rel="stylesheet" href="./js/leaflet.css"/>
    <link rel="stylesheet" href="./js/MarkerCluster.css"/>
    <link rel="stylesheet" href="./js/MarkerCluster.Default.css"/>
    <style>
        #map {
            height: 70vh;
            width: 70vw;
            background-color: #fffff5;
            opacity: 1;
        }
        .map-container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .legend{
            padding: 6px 8px;
            background: rgba(255, 255, 255, .8);
            color: #555;
            position: absolute;
            bottom: 25%;
            z-index: 999;
            left: 300px;
            font-size: 12px;
            border-radius: 2px;
        }
        .legend>div{
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        .circle{
            height: 10px;
            width: 10px;
            border-radius: 50%;
            background: #F2D643;
            margin-right: 6px;
        }

        .leaflet-div-icon {
            background: transparent;
            border: none;
        }
        .leaflet-marker-icon.ocean-label {
            color: #ffffff;
            font-size: 14px;
            width: 200px !important;
            z-index: 200;
        }
        .lake-label {
            color: #2668b4;
            font-family: initial;
        }

        .ocean-item {
            z-index: 888;
        }
    </style>
</head>
<body>
<div class="map-container">
    <div id="map"></div>
<!--    <div class="legend">-->
<!--        <div>-->
<!--            <div class="circle"></div>-->
<!--            <div>Ocean</div>-->
<!--        </div>-->
<!--        <div>-->
<!--            <div class="circle" style=" background: #E87C25!important;"></div>-->
<!--            <div>Inland Water</div>-->
<!--        </div>-->
<!--        <div>-->
<!--            <div class="circle" style="background: #C1232B;"></div>-->
<!--            <div>Critical Zone</div>-->
<!--        </div>-->
<!--    </div>-->
</div>

<script src="./js/leaflet.js" crossorigin=""></script>
<script src="./js/leaflet.markercluster.js"></script>
<script src="./data/mash-samples.js"></script>

<script>

    function filterNodeData(displayBy, omicsType, featureDataset, dataType) {

        const trueArr = allData.filter(item => item.node)
        const falseArr = allData.filter(item => !item.node)
        let filteredData  = falseArr.concat(trueArr)

        // let filteredData = [...allData]; // 复制原数据

        // 根据 displayBy 参数过滤
        if (displayBy === "project") {
            const seenProjects = new Set();
            filteredData = filteredData.filter(item => {
                if (seenProjects.has(item.project_id)) {
                    return false;
                }
                seenProjects.add(item.project_id);
                return true;
            });
        }

        // 根据 omicsType 参数过滤
        if (omicsType !== "All") {
            filteredData = filteredData.filter(item => item.omics_type.toLowerCase() === omicsType.toLowerCase());
        }

        // 根据 featureDataset 参数过滤
        if (featureDataset === "IN") {
            filteredData = filteredData.filter(item => item.analysis === true);
        } else if (featureDataset === "OUT") {
            filteredData = filteredData.filter(item => item.analysis === false);
        }

        // 根据 数据类型 参数过滤
        if (dataType === "NODE") {
            filteredData = filteredData.filter(item => item.node === true);
        } else if (dataType === "AntNest") {
            filteredData = filteredData.filter(item => item.node === false);
        }

        return filteredData;
    }

    const displayBy = "sample"; // "project" 或 "samples"
    const omicsType = "All"; // 具体类型 或 "All"
    const featureDataset = "All"; // "IN", "OUT" 或 "All"
    const dataType = "All"; // "NODE", "AntNest" 或 "All"

    const legendBy = "" // 图例：DataSource 或 HydrosphereType

    var data = filterNodeData(displayBy, omicsType, featureDataset, dataType);

    var latlng = L.latLng(20, 0);

    var map = L.map('map', {
        center: latlng,
        zoom: 2,
        minZoom: 2, // 设置最小缩放级别为 10
        // layers: [tiles],
        zoomControl: false,
        attributionControl: false,
    });

    // 创建新的 panes
    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    // 设置每个 pane 的 z-index
    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 300; // 河流图层
    map.getPane('pointsPane').style.zIndex = 300; // 圆点图层

    fetch('./data/oceans.geojson')
        .then((response) => response.json())
        .then((data) => {
            var geojsonLayer = L.geoJSON(data, {
                onEachFeature: function (feature, layer) {
                    let labelLatLng;
                    // 根据特征名称选择标签位置
                    if (feature.properties.name === 'North Pacific Ocean') {
                        labelLatLng = L.latLng(40, -140);
                    } else if (feature.properties.name === 'South Pacific Ocean') {
                        labelLatLng = L.latLng(-30, -110);
                    } else {
                        // 默认使用中心点
                        labelLatLng = layer.getBounds().getCenter();
                    }

                    // 创建一个标记
                    var label = L.marker(labelLatLng, {
                        icon: L.divIcon({
                            className: 'ocean-label',
                            html: feature.properties.name,
                            iconSize: [100, 20],
                        }),
                    });
                    label.addTo(map); // 将标签添加到地图
                },

                style: function (feature) {
                    return {
                        fillColor: '#1C4F80', // 设置填充颜色为蓝色
                        weight: 1,
                        opacity: 1, // 不透明度设置为 1
                        color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
                        fillOpacity: 1, // 填充不透明度
                        pane: 'oceanPane',
                    };
                },
            }).addTo(map);
        });

    fetch('./data/global_river.geojson')
        .then((response) => response.json())
        .then((data) => {
            riverLayer = L.layerGroup();
            // 将湖泊数据添加到层中
            L.geoJson(data, {
                onEachFeature: function (feature, layer) {
                    // 创建一个标记
                    var label = L.marker(layer.getBounds().getCenter(), {
                        icon: L.divIcon({
                            className: 'ocean-label',
                            html: '',
                            iconSize: [100, 20],
                        }),
                    });
                    label.addTo(riverLayer);
                },

                style: (feature) => {
                    return {
                        color: '#9ABAE7',
                        opacity: 1,
                        weight: 1,
                        fillOpacity: 1, // 填充不透明度
                        pane: 'riverPane',
                    };
                },
            }).addTo(riverLayer);
            // 将层添加到地图中
            map.addLayer(riverLayer);
        });

    var riverLayer = null;
    var currentZoom = null;
    map.on('zoomend', function () {
        currentZoom = map.getZoom();
        if (currentZoom > 2) {
            if (!riverLayer) {
            }
        } else {
            if (riverLayer) {
                map.removeLayer(riverLayer);
                riverLayer = null;
            }
        }
    });


    // // 湖泊./json/ne_10m_lakes_03.geojson
    fetch('./data/sample_lakes.geojson')
        .then((response) => response.json())
        .then((data) => {
            const lakeLayer = L.layerGroup();
            // 将湖泊数据添加到层中
            L.geoJson(data, {
                onEachFeature: function (feature, layer) {
                    // 创建一个标记
                    // layer.bindTooltip(feature.properties.name_abb);
                    var label = L.marker(layer.getBounds().getCenter(), {
                        icon: L.divIcon({
                            iconSize: [100, 20],
                        }),
                    });
                    label.addTo(map); // 将标签添加到地图
                    map.on('zoomend', function () {
                        var zoom = map.getZoom();
                        label.setIcon(
                            L.divIcon({
                                className: 'lake-label',
                                html: zoom > 4 ? feature.properties.Name : '',
                            })
                        );
                    });
                },
                style: (feature) => {
                    return {
                        color: '#9ABAE7',
                        opacity: 1,
                        weight: 1,
                        fillOpacity: 1, // 填充不透明度
                        pane: 'oceanPane',
                    };
                },
            }).addTo(lakeLayer);

            // 将层添加到地图中
            map.addLayer(lakeLayer);
        });

    const canvasRenderer = L.canvas({padding: 0.5});

    // 创建圆点
    var pointsLayer = L.layerGroup().addTo(map);

    for (var i = 0; i < data.length; i++) {
        var color = '';
        // 区分NODE和蚁巢数据的点颜色
        if (legendBy === 'DataSource') {
            if (data[i].node) {
                color = '#E098C7';
            } else {
                color = '#A9CF7C';
            }
        } else {
            // 区分 关键带、海洋、内陆点的颜色
            if (data[i].type === 'Critical Zone') {
                color = '#C1232B';
            } else if (data[i].type === 'Inland') {
                color = '#E87C25';
            } else {
                color = '#F2D643';
            }
        }
        if (data[i].technology_type === 'WGS') {
            if (data[i].node) {
                color = '#FFB980';
            } else {
                color = '#2EC7C9';
            }
        }else{
            color = '#8D98B3';
        }


            // if (data[i].omics_type==='METAGENOMIC') {
            //     color = '#FFB980';
            // } else {
            //     color = '#2EC7C9';
            // }


        var circleMarker = L.circleMarker([data[i].lat, data[i].lon], {
            radius: 2, // 点的大小
            fillColor: color,
            color: color,
            opacity: 1,
            weight: 0.5,
            className: data[i].run_id,
            fillOpacity: 1, // 填充透明度设置为 1
            pane: 'pointsPane',
            renderer: canvasRenderer, // 使用 Canvas 渲染
        }).addTo(pointsLayer); // 添加到图层组

        // 创建工具提示内容
        /*const popupContent = `
              Run ID: ${data[i].run_id}<br>
              Project ID: ${data[i].project_id}<br>
              Water Body Type: ${data[i].type}<br>
              Latitude: ${data[i].lat}<br>
              Longitude: ${data[i].lon}<br>
            `;

        // 绑定工具提示
        circleMarker.bindTooltip(popupContent);*/
    }
</script>
</body>
</html>
