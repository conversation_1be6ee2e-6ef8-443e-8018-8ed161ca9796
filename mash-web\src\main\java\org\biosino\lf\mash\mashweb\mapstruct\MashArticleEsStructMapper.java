package org.biosino.lf.mash.mashweb.mapstruct;

import org.biosino.lf.mash.mashweb.es.entity.MashArticleEs;
import org.biosino.lf.mash.mashweb.vo.ScholarlyArchiveListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Mapper
public interface MashArticleEsStructMapper extends CommonMapper {
    MashArticleEsStructMapper INSTANCE = Mappers.getMapper(MashArticleEsStructMapper.class);

    //void copyToVo(MashArticleEs source, @MappingTarget ScholarlyArchiveListVO target);

    @Mapping(source = "title", target = "title", qualifiedByName = "trimToEmpty")
    @Mapping(source = "volume", target = "volume", qualifiedByName = "trimToEmpty")
    @Mapping(source = "issue", target = "issue", qualifiedByName = "trimToEmpty")
    @Mapping(source = "author", target = "author", qualifiedByName = "trimToEmpty")
    @Mapping(source = "doi", target = "doi", qualifiedByName = "trimToEmpty")
    @Mapping(source = "journalTitle", target = "journalTitle", qualifiedByName = "trimToEmpty")
    @Mapping(source = "articleAbstract", target = "articleAbstract", qualifiedByName = "trimToEmpty")
    void copyToVo(MashArticleEs source, @MappingTarget ScholarlyArchiveListVO target);


}
