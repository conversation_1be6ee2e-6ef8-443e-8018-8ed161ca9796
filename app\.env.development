# .env.development
ENV = 'development'

# 注意：环境变量的值必须以 VITE_ , 否则import.meta.env 或者 loadEnv 获取获取不到

# public path
VITE_APP_PUBLIC_PATH = '/mash'

# base api
VITE_APP_BASE_API = '/dev-api'

# 打包输出的目录
VITE_BUILD_OUT_DIR = 'mash'

# Dify的应用密钥
VITE_DIFY_TOKEN = 'eyJhbGciOiJIUzM4NCJ9.eyJhY3RpdmVfcHJvZmlsZSI6ImRldiIsImFwaV90b2tlbl9rZXkiOiJtYXNoX2FwaV92YWwifQ.eUJ70zpxK_mwDNqHx08uyTCi2qghUFW6H5_2QRAx0T59Sd1DoGeSa8WqGLjmKK2U'

