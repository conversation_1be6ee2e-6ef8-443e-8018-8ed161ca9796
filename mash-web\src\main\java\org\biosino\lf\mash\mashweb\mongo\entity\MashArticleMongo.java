package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 */
@Data
@Document(collection = "supp_tp_articles")
//@CompoundIndexes({
//        @CompoundIndex(name = "uni_article_key", def = "{'project_id': 1,'order_num': 1}", unique = true)
//})
public class MashArticleMongo implements Serializable {
    @Id
    private String id;

    @Indexed(name = "uni_pmid", unique = true)
    private Long pmid;

    private Long pmcId;

    private String doi;

    private String journalTitle;
    private String journalIssnElectronic;
    private String journalIssnPrint;
    private String isoAbbreviation;
    private String jcrAbbreviation;
    private String publisherTitle;
    private String publisherIoc;
    private String title;
    private String abs;
    private String volume;
    private Integer year;

    @Field("jcr_quartile")
    private String jcrQuartile;
//    private Integer jcrCategories;
//    private Integer zkySections;

    private String issue;
    private String page;
    private String keyword;
    private String author;
    private Pdf pdf;
    private String articleType;
    private String journalAbbr;

    private String source;

    private Date createDate;

    /*private String subjectType;
    private String source;
    private String pubStatus;
    private String language;
    private String vernacularTitle;
    private String datePubmed;
    private String dateReceivedAccepted;
    private Integer publishedYear;
    private Integer publishedMonth;
    private Integer publishedDay;
    private Integer printPublishedYear;
    private Integer printPublishedMonth;
    private Integer printPublishedDay;
    private String medlineDate;
    private String ELocationType;
    private String ELocationId;
    private Integer authorIsparsed;
    private String affiliation;
    private Integer affiliationIsparsed;
    private String articleAbstract;
    private String articleAbstractCn;
    private String copyright;
    private String articleOtherAbstract;
    private String otherCopyright;
    private Integer referenceNumber;
    private String referenceIdList;
    private String note;
    private Date createDate;
    private Date updateDate;
    private Integer hitNum;
    private Date updateValidateDate;*/

    @Field("year_ifs")
    private List<ImpactFactor> yearIfs;

    @Field("zky_sections")
    private List<ZkySections> zkySections;

    @Field("pdf_doc_content")
    private PdfContent pdfDocContent;

//    @Field("project_id")
//    private Integer projectId;
//    @Field("order_num")
//    private Integer orderNum;

    @Field("node_ids")
    private List<String> nodeIds;

    @Field("ncbi_sra_ids")
    private List<String> ncbiSraIds;

    @Data
    public static class ImpactFactor {
        private Integer year;
        @Field("impact_factor")
        private Double impactFactor;
    }

    @Data
    public static class PdfContent {
        @Field("abstract")
        private List<String> abstractInfo;
        private List<String> body;
    }

    @Data
    public static class Pdf {
        private String path;
        private Long createTime;
        private String originName;
        private String md5;
    }

    @Data
    public static class ZkySections {
        private String name;
        private Integer section;
        private Integer type;
        @Field("name_cn")
        private String nameCn;
    }

}
