import os
import re
import time
import torch
import torch.multiprocessing as mp
import pandas as pd
from sentence_transformers import SentenceTransformer
from pymilvus import connections, utility, FieldSchema, CollectionSchema, DataType, Collection
from langchain_community.document_loaders import TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
import json
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist


# os.environ["CUDA_VISIBLE_DEVICES"] = "1,2,3,4,5,6,7"
def setup(rank, world_size):
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12352'
    dist.init_process_group("nccl", rank=rank, world_size=world_size)


def cleanup():
    dist.destroy_process_group()


def load_metadata(metadata_path):
    """加载元数据"""
    metadata_df = pd.read_excel(metadata_path)
    metadata_dict = {
        str(row["PMID"]): {
            "title": row.get("Title", ""),
            "doi": str(row.get("DOI", "")) if pd.notna(row.get("DOI")) else "",
            "year": int(year) if pd.notna(year := row.get("published_year")) else 0,
            "journal": str(row.get("journal_title", "")) if pd.notna(row.get("journal_title")) else "",
            "impact_factor": float(value) if pd.notna(value := row.get("impact_factor")) else 0.0,
        }
        for _, row in metadata_df.iterrows()
    }
    return metadata_dict


def process_document(rank, world_size, docs, model_path, collection_name, failed_documents_file, metadata_dict):
    # 设置当前进程使用的GPU
    setup(rank, world_size)
    torch.cuda.set_device(rank)

    # 在每个GPU上加载模型
    model = SentenceTransformer(model_path, trust_remote_code=True, device=f'cuda:{rank}')
    model = DDP(model, device_ids=[rank])

    # 连接Milvus
    connections.connect(host='localhost', port='19530')

    # 创建Collection（只在rank 0上执行）
    if rank == 0:
        if utility.has_collection(collection_name):
            utility.drop_collection(collection_name)

        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1536),
            FieldSchema(name="child_text", dtype=DataType.VARCHAR, max_length=4096),
            FieldSchema(name="parent_text", dtype=DataType.VARCHAR, max_length=4096),
            FieldSchema(name="pmid", dtype=DataType.VARCHAR, max_length=4096),
            FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=4096),
            FieldSchema(name="doi", dtype=DataType.VARCHAR, max_length=4096),
            FieldSchema(name="year", dtype=DataType.INT64),
            FieldSchema(name="journal", dtype=DataType.VARCHAR, max_length=4096),
            FieldSchema(name="impact_factor", dtype=DataType.FLOAT),
            FieldSchema(name="chunk_id", dtype=DataType.INT64)
        ]
        schema = CollectionSchema(fields, description="Document embeddings with metadata")
        collection = Collection(name=collection_name, schema=schema)

    # 等待collection创建完成
    dist.barrier()

    collection = Collection(name=collection_name)

    # 文档分割器
    parent_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=200)
    child_splitter = RecursiveCharacterTextSplitter(chunk_size=400, chunk_overlap=100)

    # 计算每个GPU应处理的文档
    docs_per_gpu = len(docs) // world_size
    start_idx = rank * docs_per_gpu
    end_idx = start_idx + docs_per_gpu if rank != world_size - 1 else len(docs)

    local_failed_documents = []

    for idx in range(start_idx, end_idx):
        doc = docs[idx]
        start_time = time.time()

        # 获取PMID
        pmid = os.path.basename(doc.metadata['source']).replace('.txt', '')

        # 获取元数据
        doc_metadata = metadata_dict.get(pmid, {
            "title": "",
            "doi": "",
            "year": "",
            "journal": "",
            "impact_factor": ""
        })

        # 预处理文档，去除[数字]和[#数字]形式的字符串
        cleaned_content = re.sub(r'\[#?-?\d+\]', '', doc.page_content)
        # 替换英文单引号为中文单引号
        cleaned_content = cleaned_content.replace("'", "’")
        # 替换英文双引号为中文双引号
        # cleaned_content = cleaned_content.replace('"', "“”")
        
        # 处理文档
        parent_docs = parent_splitter.split_text(cleaned_content)
        child_docs_with_parents = []  # 存储 (child_text, parent_text)

        for parent in parent_docs:
            child_docs = child_splitter.split_text(parent)
            for child in child_docs:
                child_docs_with_parents.append((child, parent))  # 记录子块和父块

        # 去重
        seen_texts = set()
        unique_child_docs = []
        for child_text, parent_text in child_docs_with_parents:
            if child_text not in seen_texts:
                unique_child_docs.append((child_text, parent_text))
                seen_texts.add(child_text)

        # 生成embeddings
        embeddings = model.module.encode([child[0] for child in unique_child_docs], normalize_embeddings=True)

        # 准备插入数据
        pmid_int = int(pmid) if pmid.isdigit() else 0

        batch_entities = []
        batch_size = 10

        for i, (embedding, (child_text, parent_text)) in enumerate(zip(embeddings, unique_child_docs)):
            embedding_list = embedding.tolist()

            if len(embedding_list) != 1536:
                print(f"Warning: Embedding size mismatch for PMID {pmid}, expected 1536 but got {len(embedding_list)}")

            entity = {
                "id": pmid_int * 10000 + (i + 1),
                "vector": embedding_list,
                "child_text": child_text,
                "parent_text": parent_text,
                "pmid": pmid,
                "title": doc_metadata.get("title", ""),
                "doi": doc_metadata.get("doi", ""),
                "year": doc_metadata.get("year", 0),
                "journal": doc_metadata.get("journal", ""),
                "impact_factor": doc_metadata.get("impact_factor", 0.0),
                "chunk_id": i + 1
            }
            batch_entities.append(entity)

            # 每 batch_size 条数据插入一次
            if len(batch_entities) >= batch_size:
                try:
                    collection.insert(batch_entities)
                    batch_entities = []  # 清空列表，准备下一批
                except Exception as e:
                    print(f"GPU {rank}: Failed to insert batch for PMID {pmid}: {e}")
                    local_failed_documents.append({
                        "pmid": pmid,
                        "error": str(e),
                        "error_entity": batch_entities
                    })

        # 处理最后不足 batch_size 的数据
        if batch_entities:
            try:
                collection.insert(batch_entities)
            except Exception as e:
                print(f"GPU {rank}: Failed to insert final batch for PMID {pmid}: {e}")
                local_failed_documents.append({
                    "pmid": pmid,
                    "error": str(e),
                    "error_entity": batch_entities
                })

        print(f"GPU {rank}: Successfully processed document {pmid} in {time.time() - start_time:.4f} seconds")

    # 收集所有GPU上的失败文档
    all_failed_documents = [None] * world_size
    dist.all_gather_object(all_failed_documents, local_failed_documents)

    if rank == 0:
        # 合并所有失败的文档并保存
        flat_failed_documents = [item for sublist in all_failed_documents for item in sublist]
        if flat_failed_documents:
            with open(failed_documents_file, "w") as f:
                json.dump(flat_failed_documents, f, indent=4)

    cleanup()


def main():
    # 配置参数
    docs_dir = './mash_txt'
    model_path = '/home/<USER>/script/models/gte-Qwen2-1.5B-instruct'
    metadata_path = './mash_metadata.xlsx'  # 请替换为实际的元数据文件路径
    failed_documents_file = "./mash_doc_milvus_failed.json"
    collection_name = "mash_doc_milvus"
    # 记录程序开始时间
    start_time = time.time()

    # 加载元数据
    metadata_dict = load_metadata(metadata_path)

    # 加载所有文档
    docs = []
    for filename in os.listdir(docs_dir):
        if filename.endswith(".txt"):
            file_path = os.path.join(docs_dir, filename)
            loader = TextLoader(file_path)
            loaded_docs = loader.load()
            for doc in loaded_docs:
                if doc.page_content.strip():
                    docs.append(doc)

    # 获取可用的GPU数量
    world_size = torch.cuda.device_count()

    # 使用多进程启动
    mp.spawn(
        process_document,
        args=(world_size, docs, model_path, collection_name, failed_documents_file, metadata_dict),
        nprocs=world_size,
        join=True
    )

    # 记录程序结束时间
    end_time = time.time()

    # 打印总的处理时间
    print(f"Total time taken: {end_time - start_time:.4f} seconds")


if __name__ == "__main__":
    main()
