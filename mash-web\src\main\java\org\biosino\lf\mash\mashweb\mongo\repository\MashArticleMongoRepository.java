package org.biosino.lf.mash.mashweb.mongo.repository;

import org.biosino.lf.mash.mashweb.mongo.entity.MashArticleMongo;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MashArticleMongoRepository extends MongoRepository<MashArticleMongo, String>, MashArticleMongoCustomRepository {
    MashArticleMongo findByPmid(Long pmid);

    List<MashArticleMongo> findByPmidIn(List<Long> pmids);
}
