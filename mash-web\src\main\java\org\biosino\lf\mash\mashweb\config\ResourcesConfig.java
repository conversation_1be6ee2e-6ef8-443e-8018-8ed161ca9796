package org.biosino.lf.mash.mashweb.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * 通用配置
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class ResourcesConfig implements WebMvcConfigurer {
    private final AppConfig appConfig;
    private final FileProperties fileProperties;
    public static final String BRACKEN_KRONA_HTML_URL_PREFIX = "/kronal";


    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源映射
        registry.addResourceHandler("/most_recent/**")
                .addResourceLocations("classpath:/files/most_recent");

        final String dataHome = appConfig.getDataHome();
        final File dir = new File(dataHome);
        final File imgDir = new File(dir, "imgs");
        if (!imgDir.exists()) {
            imgDir.mkdirs();
        }

        registry.addResourceHandler("/img_preview/**")
                .addResourceLocations("file:" + imgDir.getAbsolutePath());
        registry.addResourceHandler(BRACKEN_KRONA_HTML_URL_PREFIX + "/**").addResourceLocations("file:" + fileProperties.getBaseDataDir() + BRACKEN_KRONA_HTML_URL_PREFIX);

        //.setCacheControl(CacheControl.maxAge(5, TimeUnit.HOURS).cachePublic());
    }


    /**
     * 跨域配置
     */
    /*@Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        config.addAllowedOriginPattern("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 有效期 1800秒
        config.setMaxAge(1800L);
        // 添加映射路径，拦截一切请求
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        // 返回新的CorsFilter
        return new CorsFilter(source);
    }*/

}
