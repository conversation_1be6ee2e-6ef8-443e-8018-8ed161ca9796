import { ref, reactive, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';

export const useCounterStore = defineStore(
  'counter',
  () => {
    const count = ref(0);
    const doubleCount = computed(() => count.value * 2);
    function increment() {
      count.value++;
    }

    const searchQuery = ref('');
    const searchFieldsList = ref([]);
    const baskets = reactive({
      browser: {
        data: [],
        dataset: [],
      },
      projectDetail: {
        data: [],
        dataset: [],
      },
    });

    const handleQuery = str => {
      searchQuery.value = str;
      console.log(searchQuery.value);
    };
    const handleFieldsList = arr => {
      searchFieldsList.value = arr;
      console.log(searchFieldsList.value);
    };

    const setBaskets = (page, type, data) => {
      console.log(data);
      if (!data || data.length === 0) {
        ElMessage.warning({
          message: '未选择数据或数据集！',
        });
        return;
      }
      const arr = data.filter(item => {
        let b = true;
        for (const key in baskets) {
          for (const k in baskets[key]) {
            const arr1 = baskets[key][k];
            if (arr1.map(it => it.antAccession).includes(item.antAccession)) {
              b = false;
            }
          }
        }
        if (b) {
          return item;
        }
      });
      console.log(arr);
      baskets[page][type] = baskets[page][type].concat(arr);
      ElMessage.success({
        message: '添加成功！',
      });
    };
    const delBaskets = arr => {
      const ids = arr.map(item => item.antAccession);
      for (const key in baskets) {
        for (const k in baskets[key]) {
          const arr = baskets[key][k];
          for (let i = arr.length - 1; i >= 0; i--) {
            const item = arr[i];
            if (ids.includes(item.antAccession)) {
              arr.splice(i, 1);
            }
          }
        }
      }
    };
    const delBasketsAll = () => {
      for (const key in baskets) {
        for (const k in baskets[key]) {
          baskets[key][k] = [];
        }
      }
    };

    return {
      count,
      doubleCount,
      increment,
      searchQuery,
      searchFieldsList,
      handleQuery,
      handleFieldsList,
      baskets,
      setBaskets,
      delBaskets,
      delBasketsAll,
    };
  },
  {
    persist: {
      storage: sessionStorage,
      paths: ['baskets'],
    },
  },
);
