package org.biosino.lf.mash.mashweb.controller;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.ExpertQaDTO;
import org.biosino.lf.mash.mashweb.service.ExpertQaService;
import org.biosino.lf.mash.mashweb.vo.ExpertQaVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 专家问答
 *
 * <AUTHOR>
 * @date 2025/4/25
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/expertQa")
public class ExpertQaController {
    private final ExpertQaService expertQaService;

    @PostMapping("/ask")
    public AjaxResult ask(@Validated @RequestBody ExpertQaDTO expertQaDTO, HttpServletRequest request) {
        expertQaService.ask(expertQaDTO, request);
        return AjaxResult.success("success");
    }


    @GetMapping("/initInfo")
    public AjaxResult initInfo() {
        ExpertQaVO vo = expertQaService.initInfo();
        return AjaxResult.success(vo);
    }

}
