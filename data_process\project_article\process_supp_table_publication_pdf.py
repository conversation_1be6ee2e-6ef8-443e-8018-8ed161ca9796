# -*- coding: utf-8 -*-

"""
将1w pdf解析结果入库，并抽取node_ids和ncbi_sra_ids
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import os

from pymongo import MongoClient
from pymongo.synchronous.collection import Collection
from pymongo.synchronous.database import Database

from cust_utils import time_stat
from custom_log_config import init_default_logger
from fill_mash_excel import to_int_num
from fill_more_mash_excel import read_csv_by_pmid
from find_pmid_by_title import mongo_uri, mongo_db_name, mongo_collection_name
from process_supp_table_publication import supp_collection_name

logger = init_default_logger('process_supp_table_publication_pdf.log')

# input_dir = 'data/grobid_xml/1w/xml'
# output_dir = 'data/grobid_xml/1w/csv'
# input_dir = 'data/grobid_xml/1800/xml'
# output_dir = 'data/grobid_xml/1800/csv'

input_dir = 'data/grobid_xml/97/xml'
output_dir = 'data/grobid_xml/97/csv'


def save_pdf_content(collection: Collection):
    logger.info('开始读取csv中的pdf内容')
    csv_files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
    logger.info(f'csv文件数量: {len(csv_files)}')
    i = 0
    for filename in csv_files:
        i += 1
        if i % 100 == 0:
            logger.info(f'正在读取第{i}个csv文件')

        pmid = to_int_num(filename.split('.')[0])
        if pmid is None:
            logger.error(f'pmid为空，文件名：{filename}')
            continue

        query = {
            "pmid": pmid,
            "pdf_doc_content": {"$exists": False},
        }
        projection = {"_id": 1}
        result = collection.find_one(query, projection)
        if not result:
            logger.error(f'pmid为{pmid}的记录不存在')
            continue
        # pdf_doc_content = result.get('pdf_doc_content', None)
        # if pdf_doc_content:
        #     print(f'pmid为{pmid}的pdf内容已存在')
        #     continue

        csv_path = f'{output_dir}/{filename}'
        read_csv_by_pmid(csv_path, pmid, collection)

    logger.info('保存pdf内容完成')


def save_by_articles(db: Database, collection: Collection):
    collection_articles = db[mongo_collection_name]

    query = {"pmid": {"$exists": True}, "pdf_doc_content": {"$exists": True}}
    all_pmids = []
    map_item = {}

    projection = {"pmid": 1, "pdf_doc_content": 1, "_id": 0}
    results = collection_articles.find(query, projection)
    for doc in results:
        pmid = doc.get('pmid', None)
        if pmid not in all_pmids:
            all_pmids.append(pmid)

        pdf_doc_content = doc.get('pdf_doc_content', None)
        map_item[pmid] = pdf_doc_content

    print(len(all_pmids))
    query = {"pmid": {"$in": all_pmids}, "pdf_doc_content": {"$exists": False}}
    results = collection.find(query)
    i = 0
    for doc in results:
        i += 1
        pmid = doc.get('pmid', None)
        logger.info(f'{i}: 补充{pmid}的pdf_doc_content')
        pdf_doc_content = map_item.get(pmid)
        collection.update_one({"pmid": pmid}, {"$set": {"pdf_doc_content": pdf_doc_content}})


@time_stat(logger)
def process_supp_table_publication_pdf():
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[supp_collection_name]
    try:
        # 1.将grobid生成的xml转为csv
        from xml_to_csv import parse_directory
        parse_directory(input_dir, output_dir)

        # 2.读取csv，保存pdf内容到MongoDB
        save_pdf_content(collection)

        # 3.将articles表中的pdf_doc_content字段补充到supp_tp_articles表中
        save_by_articles(db, collection)

        # 4.根据标题、简介和内容，补充node_ids和ncbi_sra_ids
        from find_full_text import find_node_or_ncbi_sra_id
        find_node_or_ncbi_sra_id(collection, True)
    finally:
        client.close()


if __name__ == '__main__':
    process_supp_table_publication_pdf()
