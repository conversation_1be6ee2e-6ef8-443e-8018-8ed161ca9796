# -*- coding: utf-8 -*-

"""
调用grobid 获取pdf全文
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
from grobid_client.grobid_client import GrobidClient

from cust_utils import time_stat
from custom_log_config import init_default_logger

# 初始化日志
logger = init_default_logger('parse_pdf.log')


@time_stat(logger)
def parse_by_grobid():
    client = GrobidClient(config_path="config/grobid_config.json")
    client.process("processFulltextDocument", input_path="data/downloads", output="data/grobid_output", n=2,
                   consolidate_citations=False, force=True,
                   include_raw_affiliations=False)


if __name__ == '__main__':
    parse_by_grobid()
