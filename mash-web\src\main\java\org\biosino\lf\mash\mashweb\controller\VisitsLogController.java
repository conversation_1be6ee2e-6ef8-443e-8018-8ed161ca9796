package org.biosino.lf.mash.mashweb.controller;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.dto.VisitsLogDTO;
import org.biosino.lf.mash.mashweb.service.VisitsLogService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 访问日志控制层
 *
 * <AUTHOR>
 * @date 2025/6/10
 */
@RestController
@RequestMapping("/visitsLog")
@RequiredArgsConstructor
public class VisitsLogController {
    private final VisitsLogService visitsLogService;

    /**
     * 添加访问日志
     */
    @PostMapping("/addVisitsLog")
    public Map<String, Object> addVisitsLog(@Validated @RequestBody final VisitsLogDTO dto, final HttpServletRequest request) {
        return visitsLogService.addVisitsLog(dto, request);
    }

    /**
     * 获取访问日志统计数据
     *
     * @param request HTTP请求对象，用于获取验证令牌
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics(HttpServletRequest request) {
        return visitsLogService.getStatistics(request);
    }

}
