package org.biosino.lf.mash.mashweb.util;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;

/**
 * 缓存工具类
 */
public class CacheManageUtil {

    /**
     * 过期(毫秒)
     */
    public static final long EXPIRE_MILLISECOND = 5L * 60 * 1000;

    private static TimedCache<String, Object> baseCache;


    /**
     * 用于替代redis缓存
     *
     * @return
     */
    public static synchronized TimedCache<String, Object> baseCache() {
        if (baseCache == null) {
            baseCache = CacheUtil.newTimedCache(EXPIRE_MILLISECOND);
        }
        return baseCache;
    }


}
