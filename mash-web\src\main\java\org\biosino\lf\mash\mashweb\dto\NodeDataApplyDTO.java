package org.biosino.lf.mash.mashweb.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
@Data
public class NodeDataApplyDTO extends CaptchaDTO {

    @NotBlank(message = "Username cannot be empty")
    @Size(max = 100, message = "Username cannot exceed 100 characters")
    private String username;

    @NotBlank(message = "Password cannot be empty")
    @Size(max = 100, message = "Password cannot exceed 100 characters")
    private String password;

    private String type = "experiment";

    @NotEmpty(message = "Selected Data cannot be empty")
    private List<String> typeNos;

    @Size(max = 2500, message = "Description cannot exceed 2500 characters")
    private String description;
}
