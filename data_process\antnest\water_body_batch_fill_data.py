# -*- coding: utf-8 -*-

"""
# 将智谱AI批量分析后的结果回填到Excel中
# Author: LRJ
# Date: 2024-12-05
# Version: 1.0.0
"""

import json
import os.path

import pandas as pd

from cust_utils import load_data, time_stat, write_result_data
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item

# 配置输入输出文件

# 输入文件
# 获取当前配置项
curr_process_item = get_curr_process_item()
# 输入文件
filepath = curr_process_item.get('main_process_input_filepath')

# AI处理后的文件
# batch_output_file = "./data/jsonl_result/output_202412311433.jsonl"
# batch_output_file = "./data/jsonl_result/output_mash_flash_zh2.jsonl"
batch_output_file = "./data/jsonl_result/output_202503242014.jsonl"

# 配置日志
log_file_name = 'water_body_batch_fill_data.log'
logger = init_default_logger(log_file_name)


# 项目启动入口
@time_stat(logger)
def main():
    logger.info("开始执行 water_body_batch_fill_data.py")
    if not os.path.exists(batch_output_file) or os.path.isdir(batch_output_file):
        logger.error(f'jsonl文件不存在：{batch_output_file}')

    df = load_data(filepath)

    # 数据治理
    df = process_water_body(df)

    output_file = write_result_data(df, filepath)
    logger.info("Processed data saved to %s", output_file)

    logger.info("water_body_batch_fill_data.py 执行完毕")


def parse_jsonl(file_path):
    """
    读取 JSONL 文件并整理成字典。
    参数:
        file_path (str): JSONL 文件的路径。
    返回:
        dict: 以 RunId 为键，水体类型和水体名称为值的字典。
    """
    result = {}

    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            data = json.loads(line.strip())
            request_id = data['custom_id']
            # 提取水体类型和水体名称
            content = data['response']['body']['choices'][0]['message']['content']

            try:
                water_data = json.loads(content.strip("\n").strip())
                # 保存到结果字典
                result[request_id] = {
                    "水体类型": water_data["水体类型"],
                    "水体名称": water_data["水体名称"],
                    "水体名称中文翻译": water_data["水体名称中文翻译"]
                }
            except Exception as e:
                logger.error(f"解析JSON出错：{request_id}, {content}")

    return result


# 处理 Water_Body_Type 和 Water_Body_Name 字段
def process_water_body(df):
    batch_output_json = parse_jsonl(batch_output_file)
    for index, row in df.iterrows():
        run_id = row.get('RunID', None)

        if pd.notnull(run_id) and run_id in batch_output_json:
            water_body = batch_output_json.get(run_id)
            df.at[index, 'Water Body Type'] = water_body['水体类型']
            df.at[index, 'Water Body Name'] = water_body['水体名称']
            df.at[index, 'Water Body Name 中文翻译'] = water_body['水体名称中文翻译']

    return df


if __name__ == "__main__":
    main()
