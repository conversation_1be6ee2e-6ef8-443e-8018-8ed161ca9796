package org.biosino.lf.mash.mashweb.core;

import org.biosino.lf.mash.mashweb.util.TimeoutUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static org.biosino.lf.mash.mashweb.util.CacheManageUtil.baseCache;

/**
 * 缓存工具类，底层使用的hutool缓存
 *
 * <AUTHOR>
 **/
@Component
public class CustomCache {
    /*@Autowired
    public RedisTemplate redisTemplate;*/

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        baseCache().put(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        baseCache().put(key, value, TimeoutUtils.toMillis(timeout, timeUnit));
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
   /* public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }*/

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    /*public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return baseCache().expire(key, timeout, unit);
    }*/

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    /*public long getExpire(final String key) {
        return baseCache().timeout();
    }*/

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return baseCache().containsKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public Object getCacheObject(final String key) {
        return baseCache().get(key, false);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public void deleteObject(final String key) {
        baseCache().remove(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    /*public boolean deleteObject(final Collection collection) {
        return redisTemplate.delete(collection) > 0;
    }*/

}
