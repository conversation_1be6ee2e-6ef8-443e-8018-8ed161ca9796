import json

from fastapi import <PERSON><PERSON><PERSON>, HTTP<PERSON>x<PERSON>, Header, status
from pydantic import BaseModel
from pymilvus import MilvusClient, MilvusException
from sentence_transformers import SentenceTransformer
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

"""
    供Dify的连接外部知识库API功能使用

    网站参数填写说明：
    外部知识库 ID：Milvus的集合名称
    API Endpoint：http://127.0.0.1:8000/retrieval
    API Key：下文API_KEYS
"""

# 初始化模型和客户端
MODEL = SentenceTransformer(r'D:\Temp\mash_data\sentence-transformers\all-MiniLM-L6-v2')
MILVUS_CLIENT = MilvusClient(uri="http://**************:19530")
API_KEYS = {"lrj_test_token_2025"}  # 存储有效的API密钥


class RetrievalSetting(BaseModel):
    top_k: int
    offset: int = 0
    score_threshold: float


class RequestBody(BaseModel):
    knowledge_id: str
    query: str
    retrieval_setting: RetrievalSetting


class RecordMetadata(BaseModel):
    path: str
    description: str


class Record(BaseModel):
    content: str
    score: float
    title: str
    metadata: RecordMetadata


class RecordsResponse(BaseModel):
    records: list[Record]


class ErrorResponse(BaseModel):
    error_code: int
    error_msg: str


def normalize_score(distance: float) -> float:
    """将Milvus的距离转换为相似度分数（0-1）"""
    # Milvus返回的是距离（L2），距离越小越相似
    return 1.0 / (1.0 + distance)


@app.post("/zsk/retrieval", response_model=RecordsResponse,
          responses={
              403: {"model": ErrorResponse},
              404: {"model": ErrorResponse},
              500: {"model": ErrorResponse}
          })
async def retrieval(
        body: RequestBody,
        authorization: str = Header(...)
):
    # 身份验证
    await authentication(authorization, body)

    try:
        # 向量化查询
        query_vector = MODEL.encode(body.query).tolist()

        # 执行搜索
        search_params = {
            "metric_type": "COSINE",
            "offset": body.retrieval_setting.offset
        }

        search_result = MILVUS_CLIENT.search(
            collection_name=body.knowledge_id,
            data=[query_vector],
            limit=body.retrieval_setting.top_k,
            search_params=search_params,
            output_fields=["pmid", "title", "parent_text", "child_text"]
        )

        # 构建响应
        records = []
        for hits in search_result:
            for hit in hits:
                entity = hit['entity']
                distance = hit['distance']
                if normalize_score(distance) < body.retrieval_setting.score_threshold:
                    continue
                records.append({
                    "content": entity.get("child_text", ""),
                    "score": distance,
                    "title": entity.get("pmid", ""),
                    "metadata": {
                        "path": entity.get("pmid", ""),
                        "description": entity.get("parent_text", "")
                    }
                })
        return {"records": records}

    except MilvusException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error_code": 500, "error_msg": str(e)}
        )


class DocumentMetadata(BaseModel):
    pmid: str
    title: str
    parent_text: str
    child_text: str
    doi: str
    year: int
    journal: str
    impact_factor: float


class MetadataRequest(BaseModel):
    pmid: str
    knowledge_id: str
    child_text: Optional[str] = None


@app.post("/zsk/metadata", response_model=DocumentMetadata,
          responses={
              403: {"model": ErrorResponse},
              404: {"model": ErrorResponse},
              500: {"model": ErrorResponse}
          })
async def get_metadata(
    body: MetadataRequest,
    authorization: str = Header(...)
):
    """
    根据 PMID 和可选的 childText 查询对应文档的所有 metadata 数据。
    参数：
      - request: 包含 pmid 和可选的 childText 的请求体
      - knowledge_id: 知识库ID（Milvus 集合名称）
    返回值：文档的所有 metadata 数据
    """
    await authentication(authorization, body)

    try:
        # 构建查询表达式
        if body.child_text:
            # query_expr = f"pmid == '{body.pmid}' and child_text == '{body.child_text}'"
            query_expr = f"pmid == {json.dumps(body.pmid)} and child_text == {json.dumps(body.child_text)}"
        else:
            query_expr = f"pmid == {json.dumps(body.pmid)}"

        # 使用 Milvus 的 query 方法查询满足条件的记录
        query_result = MILVUS_CLIENT.query(
            collection_name=body.knowledge_id,
            limit=1,
            filter=query_expr,
            output_fields=["pmid", "title", "parent_text", "child_text", "doi", "year", "journal", "impact_factor"]
        )

        if not query_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"error_code": 3001, "error_msg": "未找到对应的文档"}
            )
        # 返回第一条记录
        document = query_result[0]
        return DocumentMetadata(**document)
    except MilvusException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error_code": 500, "error_msg": str(e)}
        )

# 身份验证
async def authentication(authorization, body):
    try:
        scheme, _, api_key = authorization.partition(' ')
        if scheme.lower() != 'bearer' or api_key not in API_KEYS:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={"error_code": 1002, "error_msg": "授权失败"}
            )
    except:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error_code": 1001, "error_msg": "无效的Authorization头格式"}
        )
    # 检查知识库是否存在
    if not MILVUS_CLIENT.has_collection(body.knowledge_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error_code": 2001, "error_msg": "知识库不存在"}
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
