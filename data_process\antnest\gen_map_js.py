# -*- coding: utf-8 -*-

"""
 生成地图js
 Author: lrj
 Date: 2025-01-06
 Version: 1.0.0

"""

import json

import pandas as pd

from cust_utils import time_stat
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item, is_ant_nest, CURR_ACTIVE_PROFILE

# 获取当前配置项
curr_process_item = get_curr_process_item()
# 输入文件
# input_filename = 'mash_12580.xlsx'
# 标志位是node还是AntNest
ant_nest_flag = is_ant_nest()

# 读取Excel文件
# file_path = 'data/MASH_AntNest_Samples_250102.xlsx'
file_path = curr_process_item.get('all_file_path')

# 创建一个自定义的日志记录器
log_file_name = 'gen_map_js.log'
logger = init_default_logger(log_file_name)


@time_stat(logger)
def gen_javascript():
    logger.info(f"开始生成{CURR_ACTIVE_PROFILE} js")
    df = pd.read_excel(file_path)

    # 初始化存储结果的列表
    result = []

    # 遍历每一行
    for _, row in df.iterrows():
        if row['Water Body Type'] == 'unspecified':
            continue

        # 解析Lat_lon列
        lat_lon = row['Lat_lon']
        if isinstance(lat_lon, str) and ',' in lat_lon:
            lat, lon = lat_lon.split(',')

            # 获取type字段的值
            if row['Critical Zone'] == 'IN':
                body_type = 'Critical Zone'
            elif row['Water Body Type'] == 'Oceans':
                body_type = 'Oceans'
            else:
                body_type = 'Inland'

            # 获取analysis字段的值
            analysis_fields = ['MASH-Ocean', 'MASH-Lake', 'MASH-Chinasea', 'MASH-China', 'MEER']
            analysis = any(row[field] == 'IN' for field in analysis_fields)

            # 生成JS数组对象
            # 'technology_type': row['Technology_type'],
            entry = {
                'lat': lat.strip(),
                'lon': lon.strip(),
                'type': body_type,
                'node': not ant_nest_flag,
                'analysis': analysis,
                'omics_type': str(row['tOmics_type']).upper(),
                'run_id': row['RunID'],
                'project_id': row['BioprojectID']
            }

            # 将结果添加到列表
            result.append(entry)

    # 转换为 JSON 格式并格式化为 JavaScript 数组
    # 保存为 JavaScript 文件
    if ant_nest_flag:
        js_array = f"const data = {json.dumps(result, indent=4)};"
        output_file = "antnest-samples.js"
    else:
        js_array = f"const nodeData = {json.dumps(result, indent=4)};"
        output_file = "node-samples.js"

    with open(output_file, "w", encoding="utf-8") as f:
        f.write(js_array)

    logger.info(f"{CURR_ACTIVE_PROFILE} JS生成完成")


if __name__ == '__main__':
    gen_javascript()
