# -*- coding: utf-8 -*-
# 将蚁巢数据按智谱AI规范切割后批量分析
# Author: LRJ
# Date: 2024-12-05
# Version: 1.0.0

import json
import os.path

import pandas as pd

from cust_utils import time_stat, load_data
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item, is_ant_nest, CURR_ACTIVE_PROFILE

# 配置输入输出文件

# 蚁巢数据 OR NOD数据
AntNest = is_ant_nest()

# 获取当前配置项
curr_process_item = get_curr_process_item()
# 输入文件
filepath = curr_process_item.get('main_process_input_filepath')

# 配置日志
log_file_name = 'water_body_batch_split_data.log'
logger = init_default_logger(log_file_name)


# 项目启动入口
@time_stat(logger)
def main():
    logger.info("开始执行 water_body_batch_split_data.py")

    df = load_data(filepath)

    # 数据治理
    process_water_body(df)

    # 程序结束
    logger.info("water_body_batch_split_data.py 执行完毕")


# 处理 Water_Body_Type 和 Water_Body_Name 字段
def process_water_body(df):
    # 初始化记录计数和文件编号
    max_records = 50000
    record_count = 0
    file_count = 1
    output_dir = "./data/jsonl_split"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    output_file = f"{output_dir}/mash_split_data_{CURR_ACTIVE_PROFILE}_{file_count}.jsonl"

    if AntNest:
        # AntNest的数据列
        context_fields = {
            'Geo_loc_name': "Geographical location name:",
            'Sample_title': "Sample title:",
            'Sample_description': "Sample description:",
            'BioProject_description': "Project description:",
            'BioProject_name': "Project name:",
            'BioProject_title': "Project title:",
            'Biome': "Biome:",
            'Lat_lon': "Latitude and longitude:"
        }
    else:
        # NODE 的数据列
        context_fields = {
            'Geo_loc_name': "Geographical location name:",
            'Sample_title': "Sample title:",
            'sample_name': "Sample Name:",
            'Sample_description': "Sample description:",
            'experiment_name': "Experiment Name:",
            'experiment_description': "Experiment description:",
            'BioProject_description': "Project description:",
            'BioProject_name': "Project name:",
            'Env_biome_curated': "Env Biome:",
            'Env_feature_curated': "Env Feature:",
            'Env_material_curated': "Env Material:",
            'Biome': "Biome:",
            'Lat_lon': "Latitude and longitude:"
        }
    # 打开第一个 JSONL 文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for index, row in df.iterrows():

            # 合并上下文：前面添加字段名称，只取非空值字段并排除"无"
            context = "; ".join([
                f"{context_fields[field]} {str(row[field])}"
                for field in context_fields if
                pd.notnull(row[field]) and row[field] != "" and row[field] != "无" and row[field] != "-"
            ])

            # 遍历每一行数据
            search_content = "## 任务: - 从描述文本中准确识别出水体类型和水体名称，提供水体名称中文翻译，只输出结果。 ## 要求:- 必须识别以下类别的实体：水体类型、水体名称。- 水体类型必须属于以下类别之一：Oceans, Ice Caps & Glaciers, Groundwater, Freshwater lakes, Wetlands, Soil moisture, Rivers, Atmosphere, Biota, Saline lakes, Inland Seas。- 温泉的水体类型是Groundwater。- 如果描述性文本中出现Hypersaline和Saline关键词，则水体类型是Saline lakes。- 水体名称提供具体地点的官方英文名称，不能是Mangrove swamp、Coral Reef、Sea、Gulf等非具体地点数据，可以是自然保护区名称。- 水体名称不要出现多级信息，只保留最详细的信息即可，如Atlantic Ocean: Gulf of Cadiz应该是Gulf of Cadiz。- 如果无法识别水体名称可根据经纬度地理信息推测。- 水体名称提供中文翻译。- 输出内容只提供最匹配的结果。- 如果无法识别，回答“unspecified”。## 输出格式： '''{\"水体类型\": \"\", \"水体名称\": \"\", \"水体名称中文翻译\": \"\" }''' ## 描述文本：" + context
            # 获取 id 和 name
            record = {"custom_id": row['RunID'], "method": "POST", "url": "/v4/chat/completions",
                      "body": {"model": "glm-4-flash",
                               "response_format": {
                                   'type': 'json_object'
                               },
                               "temperature": 0.1,
                               "messages": [{"role": "system", "content": "水体类型与水体名称识别专家"},
                                            {"role": "user",
                                             "content": search_content}]}}

            # 将记录写入 JSONL 文件
            f.write(json.dumps(record, ensure_ascii=False) + '\n')

            record_count += 1

            # 每max_records条记录生成一个新的文件
            if record_count >= max_records:
                file_count += 1
                output_file = f"{output_dir}/mash_split_data_{CURR_ACTIVE_PROFILE}_{file_count}.jsonl"
                f = open(output_file, 'w', encoding='utf-8')
                record_count = 0


if __name__ == "__main__":
    main()
