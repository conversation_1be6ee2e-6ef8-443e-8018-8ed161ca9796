package org.biosino.lf.mash.mashweb;

import cn.hutool.core.collection.CollUtil;
import co.elastic.clients.elasticsearch._types.Script;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.util.ObjectBuilder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.biosino.lf.mash.mashweb.es.entity.MashArticleEs;
import org.biosino.lf.mash.mashweb.es.repository.MashArticleEsRepository;
import org.biosino.lf.mash.mashweb.service.EsService;
import org.junit.jupiter.api.Test;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/3/12
 */
@SpringBootTest
public class EsTest {
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    //@Autowired
    //private ElasticsearchOperations elasticsearchOperations;
    @Autowired
    private ObjectMapper objectMapper;

//    @Autowired
//    private VectorStore vectorStore;

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private MashArticleEsRepository mashArticleEsRepository;

    @Autowired
    private EsService esService;

    @Test
    public void checkNormalize() {
        float[] vector = embeddingModel.embed("测试文本");
        check(vector);
//        checkList(normalize(vector));
        float[] floats = EsService.normalizeVector(embeddingModel, "测试文本");
        check(floats);
    }

    /**
     * 检查向量是否归一花
     */
    private void check(float[] vector) {
        double norm = 0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);
        // 如果范数接近1.0，则已归一化；否则需要手动归一化
        System.out.println("向量范数: " + norm);
    }

    private void checkList(List<Float> vector) {
        double norm = 0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);
        System.out.println("向量范数: " + norm);
    }


    @Test
    public void test2() {
//        MatchQuery.Builder builder = QueryBuilders.match()
//                .field("title")
//                .query("Concept discrimination learning by preschool");

        TermQuery.Builder builder = QueryBuilders.term()
                .field("pmid").value(6854789);

        /*Query query = new NativeQueryBuilder()
                .withQuery(QueryBuilders.term().field("category").value("electronics"))
                .build();*/

        final NativeQuery searchQuery = new NativeQueryBuilder().withQuery(builder.build()._toQuery()).build();
        long count = elasticsearchTemplate.count(searchQuery, MashArticleEs.class);
        System.out.println(count);
        SearchHits<MashArticleEs> search = elasticsearchTemplate.search(searchQuery, MashArticleEs.class);
        // SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        int i = 0;
        for (SearchHit<MashArticleEs> searchHit : search) {
            System.out.println(i++);
            MashArticleEs content = searchHit.getContent();
            System.out.println(content.getTitle());
            System.out.println(content.getEsUpdateDate());
        }
    }

    @Test
    public void testVector() {
        //vectorStore.similaritySearch(SearchRequest.query("Spring").withTopK(5));
        //List<Document> results =vectorStore.similaritySearch(SearchRequest.builder().query("Spring").topK(5).build());

        List<Document> documents = List.of(
                new Document("Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!! Spring AI rocks!!", Map.of("meta1", "meta1")),
                new Document("The World is Big and Salvation Lurks Around the Corner"),
                new Document("You walk forward facing the past and you turn back toward the future.", Map.of("meta2", "meta2")));

        // Add the documents to Elasticsearch
//        vectorStore.add(documents);

        // Retrieve documents similar to a query
//        List<Document> results = this.vectorStore.similaritySearch(SearchRequest.builder().query("Spring").topK(5).build());
//        System.out.println(results.size());

    }


    @Test
    public void test3() {
//        String text = "Radical retropubic prostatectomy.";
        String text = "title INDEX 0";
        float[] queryVector = embeddingModel.embed(text);

//        Script script = new Script(ScriptType.INLINE, "painless",
//                "cosineSimilarity(params.query_vector, 'vector_field') + 1.0",
//                Map.of("query_vector", queryVector));
        // 构建params
        /*List<Double> vectorList = IntStream.range(0, queryVector.length)
                .mapToObj(i -> (double) queryVector[i])
                .collect(Collectors.toList());*/
        Map<String, JsonData> params = Map.of("query_vector", JsonData.of(queryVector));

        Script script = Script.of(fn -> fn.source("cosineSimilarity(params.query_vector, 'title_vector') + 1.0")
                .lang("painless").params(params));

        NativeQuery searchQuery = new NativeQueryBuilder()
                .withQuery(QueryBuilders.scriptScore(fn -> fn
                        .script(script)
                        .query(QueryBuilders.matchAll().build()._toQuery())  // 可替换为过滤条件[[1]]
                ))
                .build();

        SearchHits<MashArticleEs> search = elasticsearchTemplate.search(searchQuery, MashArticleEs.class);
        int i = 0;
        for (SearchHit<MashArticleEs> searchHit : search) {
            MashArticleEs content = searchHit.getContent();
            System.out.println(content.getPmid());
            System.out.println(content.getTitle());
            System.out.println(content.getEsUpdateDate());
        }
    }

    @Test
    public void testSave() throws JsonProcessingException {
        List<MashArticleEs> list = new ArrayList<>();
        ArrayList<String> list1 = CollUtil.toList("cat", "dog", "cattle", "milk", "goat");
        for (int i = 0; i < 5; i++) {
            MashArticleEs item = new MashArticleEs();
            item.setPmid(i + 1L);
            item.setTitle("title INDEX " + i);
            item.setTitleVector(embeddingModel.embed(list1.get(i)));
//            item.setLanguage("zh" + i);
//            item.setUpdateDate(LocalDateTime.now());
            item.setEsUpdateDate(new Date());
//            item.setUpdateDate(ZonedDateTime.now());
            list.add(item);
            System.out.println(objectMapper.writeValueAsString(item));
        }

//        mashArticleEsRepository.saveAll(list, RefreshPolicy.IMMEDIATE);
//        mashArticleEsRepository.saveAll(list);
    }


    @Test
    public void initEs() {
        esService.initEs();
    }

    @Test
    public void testSearch() {
        long count = elasticsearchTemplate.count(new NativeQueryBuilder().build(), MashArticleEs.class);
        System.out.println("总数：" + count);

        final ObjectBuilder<RangeQuery> builder = QueryBuilders.range().date(fn -> fn.field("update_date")
                .gte("2025-01-16 16:38:11").lte("2025-04-16 16:39:11")
                .timeZone("Asia/Shanghai").format("yyyy-MM-dd HH:mm:ss"));
        // final String title = "Genomic basis of environmental adaptation in the widespread poly-extremophilic Exiguobacterium group.";
        final String title = "Cultivation strategies for organisms";

        float[] queryVector = embeddingModel.embed(title);
        final Map<String, JsonData> params = Map.of("query_vector", JsonData.of(queryVector));
        final Script script = Script.of(fn -> fn.source("cosineSimilarity(params.query_vector, 'title_vector') + 1.0")
                .lang("painless").params(params));

//        QueryBuilders.bool().must(QueryBuilders.scriptScore());

        final NativeQuery searchQuery = new NativeQueryBuilder()
                .withQuery(QueryBuilders.scriptScore(fn -> fn
                        .script(script)
                        .query(builder.build()._toQuery())  // 替换为过滤条件
                )).withPageable(PageRequest.of(0, 10))
                .build();

        // 打印DSL
        System.out.println("DSL: " + searchQuery.getQuery());
        final SearchHits<MashArticleEs> search = elasticsearchTemplate.search(searchQuery, MashArticleEs.class);

        System.out.println(title);
        for (final SearchHit<MashArticleEs> searchHit : search) {
            final MashArticleEs item = searchHit.getContent();
            float score = searchHit.getScore();
            System.out.println(item.getTitle());
            System.out.println(score - 1);
        }
    }

}
