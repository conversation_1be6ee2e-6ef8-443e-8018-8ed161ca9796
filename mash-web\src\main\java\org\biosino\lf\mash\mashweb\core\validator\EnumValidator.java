package org.biosino.lf.mash.mashweb.core.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 枚举name取值范围校验器实现类
 *
 * <AUTHOR>
 */
public class EnumValidator implements ConstraintValidator<ValidEnum, String> {
    private Class<? extends Enum<?>> enumClass;
    private boolean ignoreCase;
    private boolean allowNull;

    @Override
    public void initialize(ValidEnum annotation) {
        ConstraintValidator.super.initialize(annotation);
        this.enumClass = annotation.enumClass();
        this.ignoreCase = annotation.ignoreCase();
        this.allowNull = annotation.allowNull();
    }

    @Override
    public boolean isValid(final String value, ConstraintValidatorContext context) {
        if (value == null) {
            // 是否允许null值
            return allowNull;
        }
        try {
            Enum<?>[] enums = enumClass.getEnumConstants();
            for (Enum<?> e : enums) {
                if (ignoreCase) {
                    if (e.name().equalsIgnoreCase(value)) {
                        return true;
                    }
                } else {
                    if (e.name().equals(value)) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            // 如果enumClass不是一个枚举类或者为空，则返回false
            return false;
        }
    }
}
