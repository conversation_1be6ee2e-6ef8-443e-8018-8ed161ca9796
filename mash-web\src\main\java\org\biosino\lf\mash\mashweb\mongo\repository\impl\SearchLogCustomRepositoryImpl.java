package org.biosino.lf.mash.mashweb.mongo.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.mongo.entity.SearchLog;
import org.biosino.lf.mash.mashweb.mongo.repository.SearchLogCustomRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@RequiredArgsConstructor
public class SearchLogCustomRepositoryImpl implements SearchLogCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public List<SearchLog> findTop5() {
        PageRequest page = PageRequest.of(0, 5, Sort.by(Sort.Direction.DESC, "num"));
        Query query = new Query();
        query.with(page);
        return mongoTemplate.find(query, SearchLog.class);
    }

}
