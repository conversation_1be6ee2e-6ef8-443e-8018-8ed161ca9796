import pandas as pd

# 读取 Excel 文件
file_path = "Updated_MASH_Samples_Antnest_250224.xlsx"

# 加载数据
df = pd.read_excel(file_path)

# 确保列名正确
water_body_type_col = "Water Body Type"
water_body_name_col = "Water Body Name"

# 排除 Water Body Name 等于 'unspecified'
filtered_df = df[df[water_body_name_col] != "unspecified"]

# 去重并统计每种类型的水体名称数量
result = (
    filtered_df.drop_duplicates(subset=[water_body_type_col, water_body_name_col])
    .groupby(water_body_type_col)[water_body_name_col]
    .nunique()
)

# 将结果转换为 DataFrame
result_df = result.reset_index()
result_df.columns = [water_body_type_col, "Water Body Name种类数"]

# 打印结果
print(result_df)

# 可选：将结果导出到 Excel
output_path = "unique_water_body_counts.xlsx"  # 替换为你的输出文件路径
result_df.to_excel(output_path, index=False)
