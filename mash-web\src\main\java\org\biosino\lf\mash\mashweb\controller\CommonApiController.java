package org.biosino.lf.mash.mashweb.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.ApiParamDTO;
import org.biosino.lf.mash.mashweb.service.EsService;
import org.biosino.lf.mash.mashweb.util.ThreadPoolUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * api接口
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class CommonApiController {
    private final EsService esService;
    private static final Map<String, Object> taskRunningMap = new ConcurrentHashMap<>();

    private static final String INIT_ES_KEY = "initEs";

    /**
     * token校验
     */
    private void checkToken(final String token) {
        if (!"3c8661430cdd56d329af2914b93bdc07tzrG6kLxvMOV".equals(token)) {
            throw new ServiceException("权限不足");
        }
    }


    @PostMapping("/initEs")
    public String initEs(@RequestBody final ApiParamDTO dto) {
        executeTask(dto.getToken(), INIT_ES_KEY, () -> esService.initEs());
        return "Es 正在初始化中";
    }

    /**
     * 校验token并执行任务（如果任务未运行）
     */
    private void executeTask(final String token, final String taskKey, final Runnable task) {
        // 校验token
        checkToken(token);

        // 检查任务是否正在运行
        if (taskRunningMap.containsKey(taskKey)) {
            throw new ServiceException(taskKey + ": 任务正在运行中");
        }

        // 执行任务
        ThreadPoolUtil.getExecutor().execute(() -> {
            try {
                taskRunningMap.put(taskKey, true);
                task.run();
            } finally {
                taskRunningMap.remove(taskKey);
            }
        });
    }


//    private final EmbeddingModel embeddingModel; // 自动注入 ONNX 模型
//    private final ElasticsearchTemplate elasticsearchTemplate;
//    private final VectorStore vectorStore;

    /*private float[] generateEmbedding(String text) {
        float[] embed = embeddingModel.embed(text);
        System.out.println("Embedding size: " + embed.length); // 应为 384 维
        return embed;
    }*/

    /*@GetMapping("/ai")
    public float[] getResult(String text) {
        long count = elasticsearchTemplate.count(Query.findAll(), MashArticleEs.class);
        System.out.println("count: " + count);
        return generateEmbedding(text);
    }*/


}
