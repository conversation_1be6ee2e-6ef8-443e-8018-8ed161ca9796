package org.biosino.lf.mash.mashweb.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Data
public class ScholarlyArchiveListVO implements Serializable {

    private String id;

    private Long pmid;

    private String title;

    private Integer publishedYear;

    private Integer year;

    private String volume;

    private String issue;

    private String author;

    private String doi;

    private String journalTitle;

    private String page;

    private Date createDate;

    private String articleAbstract;

    private List<String> nodeIds;
    private List<String> ncbiSraIds;
    private int bioProjectsCount;

    private boolean checked = false;

    private boolean disabled = false;

    private String img;
}
