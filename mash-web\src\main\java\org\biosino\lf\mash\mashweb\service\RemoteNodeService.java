package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.config.NodeProperties;
import org.biosino.lf.mash.mashweb.core.CustomCache;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.NodeDataApplyDTO;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RemoteNodeService {

    private final NodeProperties nodeProperties;

    private final CustomCache customCache;

    public List<JSONObject> findByExpNos(List<String> projectNos) {
        Map<String, Object> body = new HashMap<>();
        body.put("nos", projectNos);
        String url = HttpUtil.urlWithForm(nodeProperties.getGetExperimentMetadataUrl(), body, StandardCharsets.UTF_8, true);
        HttpRequest httpRequest = HttpUtil.createGet(url)
                .header("token", nodeProperties.getApiToken())
                .timeout(10 * 1000);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String responseBody = httpResponse.body();
            log.info(responseBody);
            if (httpResponse.getStatus() != 200) {
                throw new ServerException("NODE 接口异常");
            }
            JSONObject jsonResult = JSONUtil.parseObj(responseBody);
            Integer code = jsonResult.get("code", Integer.class);
            if (code != 200) {
                throw new ServiceException(jsonResult.getStr("msg"));
            }
            return JSONUtil.toList(jsonResult.getStr("data"), JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public List<JSONObject> findMetadataByNos(String type, List<String> nos) {
        Map<String, Object> body = new HashMap<>();
        body.put("nos", nos);
        String url = HttpUtil.urlWithForm(StrUtil.format(nodeProperties.getGetMetadataUrl(), type), body, StandardCharsets.UTF_8, true);
        HttpRequest httpRequest = HttpUtil.createGet(url)
                .header("token", nodeProperties.getApiToken())
                .timeout(10 * 1000);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String responseBody = httpResponse.body();
            log.info(responseBody);
            if (httpResponse.getStatus() != 200) {
                throw new ServerException("NODE 接口异常");
            }
            JSONObject jsonResult = JSONUtil.parseObj(responseBody);
            Integer code = jsonResult.get("code", Integer.class);
            if (code != 200) {
                throw new ServiceException(jsonResult.getStr("msg"));
            }
            return JSONUtil.toList(jsonResult.getStr("data"), JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void applyNodeData(NodeDataApplyDTO dto) {
        HttpRequest httpRequest = HttpUtil.createPost(nodeProperties.getSaveRequestDataUrl())
                .header("token", nodeProperties.getApiToken())
                .body(JSONUtil.toJsonStr(dto))
                .timeout(10 * 1000);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            log.info(httpResponse.body());
            String responseBody = httpResponse.body();
            if (StrUtil.containsIgnoreCase(responseBody, "username or password error")) {
                if (customCache.hasKey(dto.getUsername())) {
                    Integer count = (Integer) customCache.getCacheObject(dto.getUsername());
                    if (count >= 5) {
                        throw new ServiceException("The number of failed password verification failed, please try again after 5 minutes");
                    }
                    customCache.setCacheObject(dto.getUsername(), count + 1);
                } else {
                    customCache.setCacheObject(dto.getUsername(), 1);
                }
            }
            if (httpResponse.getStatus() != 200) {
                throw new ServerException("NODE 接口异常");
            }
            JSONObject jsonResult = JSONUtil.parseObj(responseBody);
            Integer code = jsonResult.get("code", Integer.class);
            if (code != 200) {
                throw new ServiceException(jsonResult.getStr("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }
}
