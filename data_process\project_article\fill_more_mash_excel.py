# -*- coding: utf-8 -*-

"""
Excel数据回填
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import json
import os
from time import sleep

import pandas as pd
import requests
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import time_stat, str_val
from custom_log_config import init_default_logger
from fill_mash_excel import to_int_num
from find_pmid_by_title import mongo_uri, mongo_db_name, mongo_collection_name, base_api_url, api_token, HEADERS

# 初始化日志
logger = init_default_logger('fill_more_mash_excel.log')

final_300_filepath = 'data/水圈项目&论文_补充plosp数据-0313.xlsx'
filepath = final_300_filepath
new_filepath = final_300_filepath

pmid_api_url = base_api_url + '/article/findByPmid.do'
doi_api_url = base_api_url + '/article/findByDois.do'


def find_by_pmid_in_plosp(pmid: int, _session: requests.Session):
    params = {
        'token': api_token,
        'pmid': pmid,
    }
    sleep(1.2)
    response = _session.request("GET", pmid_api_url, params=params, timeout=50)
    response.raise_for_status()  # 检查请求是否成功
    return response.json()


def process_more_excel_files(collection: Collection, _session: requests.Session):
    # 查询 collection 中 pmid 字段的去重值
    all_pmids = collection.distinct("pmid")

    # 检查文件是否存在
    if not os.path.exists(filepath):
        logger.error(f'文件不存在: {filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {filepath}')
        return

    # 打印文件路径和列内容
    logger.info(f'处理文件：{filepath}')

    for index, row in df.iterrows():
        # title = trim_to_none(row['名称'])
        pmid = str_val(row, 'pmid')
        if pmid:
            pmid = to_int_num(pmid)
            if pmid is None or pmid in all_pmids:
                continue

            print(f'正在处理行{index + 1}: {pmid}')
            json_data = find_by_pmid_in_plosp(pmid, _session)
            status = json_data.get('status', '')
            msg = json_data.get('msg', '')
            one_item = json_data.get('article', None)

            if status == 'success':
                if one_item:
                    all_pmids.append(pmid)
                    # 插入数据
                    collection.insert_one(one_item)

                    add_col(df, index, one_item)
                else:
                    logger.info(f'未找到pmid: {pmid}')
            else:
                logger.error(f'api查询出错: {pmid}，错误信息: {msg}')

    # 保存到新的Excel文件
    df.to_excel(new_filepath, index=False, engine='openpyxl')
    logger.info(f'更新后的文件已保存到: {new_filepath}')


def process_more_by_doi(collection: Collection, _session: requests.Session):
    # 查询 collection 中 pmid 字段的去重值
    all_pmids = collection.distinct("pmid")

    # 检查文件是否存在
    if not os.path.exists(filepath):
        logger.error(f'文件不存在: {filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {filepath}')
        return

    # 打印文件路径和列内容
    logger.info(f'处理文件：{filepath}')

    for index, row in df.iterrows():
        # title = trim_to_none(row['名称'])
        pmid = str_val(row, 'pmid')
        doi = str_val(row, 'doi')
        if doi:
            pmid = to_int_num(pmid)
            if pmid in all_pmids:
                continue

            print(f'正在处理行{index + 1}: {doi}')
            params = {
                'token': api_token,
                'doi': doi,
            }
            response = _session.request("GET", doi_api_url, params=params, timeout=50)
            response.raise_for_status()  # 检查请求是否成功
            sleep(1.2)
            json_data = response.json()
            status = json_data.get('status', '')
            msg = json_data.get('msg', '')
            articles = json_data.get('articles', None)

            if status == 'success':
                if articles and len(articles) > 0:
                    for one_item in articles:
                        pmid = one_item.get('pmid')
                        if pmid in all_pmids:
                            continue

                        all_pmids.append(pmid)
                        # 插入数据
                        collection.insert_one(one_item)

                        add_col(df, index, one_item)
                else:
                    logger.info(f'未找到doi: {doi}')
            else:
                logger.error(f'api查询出错: {doi}，错误信息: {msg}')

    # 保存到新的Excel文件
    df.to_excel(new_filepath, index=False, engine='openpyxl')
    logger.info(f'更新后的文件已保存到: {new_filepath}')


def add_col(df: pd.DataFrame, index, one_item):
    pmid = one_item.get('pmid')
    # 更新'名称'列和添加新列
    # df.at[index, '名称'] = one_item.get('title')
    df.at[index, 'pmid'] = str(pmid)

    pdf_info = one_item.get('pdf', None)
    has_pdf = pdf_info and pdf_info.get('path', None)
    df.at[index, '是否存在PDF'] = 'YES' if has_pdf else 'NO'

    df.at[index, 'pmcId'] = str_val(one_item, 'pmcId')
    df.at[index, 'doi'] = str_val(one_item, 'doi')
    df.at[index, 'plosp title'] = str_val(one_item, 'title')
    df.at[index, '期刊名称'] = str_val(one_item, 'journalTitle')
    df.at[index, '卷volume'] = str_val(one_item, 'volume')
    df.at[index, '期号issue'] = str_val(one_item, 'issue')
    df.at[index, '期刊Issn Print'] = str_val(one_item, 'journalIssnPrint')
    df.at[index, '期刊 Issn Electronic'] = str_val(one_item, 'journalIssnElectronic')
    df.at[index, '页码'] = str_val(one_item, 'page')
    df.at[index, '作者'] = str_val(one_item, 'author')
    df.at[index, '关键词'] = str_val(one_item, 'keyword')
    df.at[index, '摘要'] = str_val(one_item, 'abs')
    df.at[index, 'year'] = str_val(one_item, 'year')
    df.at[index, '出版社'] = str_val(one_item, 'publisherTitle')
    df.at[index, 'publisherIoc'] = str_val(one_item, 'publisherIoc')
    year_ifs = one_item.get('year_ifs', None)
    year_ifs_str = ''
    if year_ifs:
        year_ifs_str = json.dumps(year_ifs)
    df.at[index, '影响因子'] = year_ifs_str

    zky_sections = one_item.get('zky_sections', None)
    zky_sections_str = ''
    if zky_sections:
        name_cn_list = [item["name_cn"] for item in zky_sections]
        zky_sections_str = ", ".join(name_cn_list)
    df.at[index, '中科院分区'] = zky_sections_str


def update_plosp_title(collection: Collection):
    # 检查文件是否存在
    if not os.path.exists(new_filepath):
        logger.error(f'文件不存在: {new_filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(new_filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {new_filepath}')
        return

    # 打印文件路径和列内容
    logger.info(f'处理文件：{new_filepath}')
    for index, row in df.iterrows():
        pmid = str_val(row, 'pmid')
        if pmid:
            pmid = to_int_num(pmid)
            if pmid is None:
                continue

            one_item = collection.find_one({'pmid': pmid})
            if one_item:
                add_col(df, index, one_item)
                df.at[index, 'plosp title'] = str_val(one_item, 'title')

    # 保存到新的Excel文件
    df.to_excel(new_filepath, index=False, engine='openpyxl')
    logger.info(f'更新plosp title后的文件已保存到: {new_filepath}')


def clean_and_filter_content(content):
    if pd.isnull(content):
        return None
    # 去除首尾空白字符
    stripped_content = str(content).strip()
    # 如果内容为空，则返回 None
    return stripped_content if stripped_content else None


def parse_csv(csv_path: str):
    df = pd.read_csv(csv_path)

    # 清理 content 列并过滤掉空值
    df['content'] = df['content'].apply(clean_and_filter_content)
    df = df.dropna(subset=['content'])  # 删除 content 为空的行
    # 按照 'type' 列进行分组，并将 'content' 列的值转换为字符串数组
    return df.groupby('type')['content'].apply(list).to_dict()


def read_csv_by_pmid(csv_path: str, pmid: int, collection: Collection):
    pdf_doc_content = parse_csv(csv_path)
    collection.update_one({'pmid': pmid}, {'$set': {'pdf_doc_content': pdf_doc_content}})


def insert_cust_data(row, csv_path: str, collection: Collection):
    author = str_val(row, '作者')
    title = str_val(row, '名称')
    journalTitle = str_val(row, '期刊')
    year = to_int_num(str_val(row, '日期'))

    project_id = to_int_num(str_val(row, '项目编号'))
    order_num = to_int_num(str_val(row, '序号'))

    one_item = collection.find_one({'project_id': project_id, 'order_num': order_num})
    if one_item:
        logger.info(f'项目编号: {project_id}，序号: {order_num}，已存在')
        return
    else:
        pmid = to_int_num(str_val(row, 'pmid'))
        logger.info(f'项目编号: {project_id}，序号: {order_num}，不存在, pmid: {pmid}')
        if pmid:
            plosp_title = to_int_num(str_val(row, 'plosp title'))
            if plosp_title:
                raise Exception(f'项目编号: {project_id}，序号: {order_num}，plosp title存在：{plosp_title}')

    pdf_doc_content = None
    if os.path.exists(csv_path):
        logger.info(f'csv文件存在: {csv_path}')
        pdf_doc_content = parse_csv(csv_path)

    one_item = {
        'project_id': project_id,
        'order_num': order_num,
        'author': author,
        'title': title,
        'journalTitle': journalTitle,
        'year': year,
    }

    if pdf_doc_content:
        one_item['pdf_doc_content'] = pdf_doc_content
    collection.insert_one(one_item)
    logger.info(f'插入数据成功,项目编号: {project_id}，序号: {order_num}')


def insert_project_full_doc_info(collection: Collection):
    # 检查文件是否存在
    if not os.path.exists(final_300_filepath):
        logger.error(f'文件不存在: {final_300_filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(final_300_filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {final_300_filepath}')
        return

    # 打印文件路径和列内容
    logger.info(f'处理文件：{final_300_filepath}')
    csv_dir = 'data/grobid_xml/output_csv'

    for index, row in df.iterrows():
        pmid = str_val(row, 'pmid')

        project_id = to_int_num(str_val(row, '项目编号'))
        order_num = to_int_num(str_val(row, '序号'))
        if project_id is None or order_num is None:
            raise ValueError('项目编号或序号为空')

        csv_path = f'{csv_dir}/{project_id}.{str(order_num).zfill(2)}.grobid.tei.csv'

        if pmid:
            pmid = to_int_num(pmid)
            if pmid is None:
                continue

            one_item = collection.find_one({'pmid': pmid})
            if one_item:

                collection.update_one({'pmid': pmid}, {'$set': {'project_id': project_id, 'order_num': order_num}})

                if os.path.exists(csv_path):
                    read_csv_by_pmid(csv_path, pmid, collection)
                else:
                    has_pdf_fill = str_val(row, '是否存在PDF')
                    if '同' in has_pdf_fill:
                        file_name = has_pdf_fill[1:]
                        csv_path = f'{csv_dir}/{file_name}.grobid.tei.csv'
                        read_csv_by_pmid(csv_path, pmid, collection)
                        continue

                    pdf_info = one_item.get('pdf', None)
                    has_pdf = pdf_info and pdf_info.get('path', None)
                    has_pdf = True if has_pdf else False
                    has_pdf_fill = str_val(row, '是否存在PDF')
                    logger.error(f'文件不存在: {csv_path}, 是否存在PDF: {has_pdf_fill}  {has_pdf}')
            else:
                logger.error(f'pmid对应数据不存在: {pmid}')
                insert_cust_data(row, csv_path, collection)
        else:
            # 处理没有pmid的情况
            logger.error(f'处理没有pmid的情况, 行数: {index + 1}')
            insert_cust_data(row, csv_path, collection)

    logger.info(f'更新项目和全文信息后的文件已保存到')


def insert_all(collection: Collection):
    # 检查文件是否存在
    if not os.path.exists(final_300_filepath):
        logger.error(f'文件不存在: {final_300_filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(final_300_filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {final_300_filepath}')
        return

    # 打印文件路径和列内容
    logger.info(f'处理文件：{final_300_filepath}')
    csv_dir = 'data/grobid_xml/output_csv'

    for index, row in df.iterrows():
        project_id = to_int_num(str_val(row, '项目编号'))
        order_num = to_int_num(str_val(row, '序号'))
        if project_id is None or order_num is None:
            raise ValueError('项目编号或序号为空')

        csv_path = f'{csv_dir}/{project_id}.{str(order_num).zfill(2)}.grobid.tei.csv'
        insert_cust_data(row, csv_path, collection)


@time_stat(logger)
def fill_more_excel():
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[mongo_collection_name]
    _session = None
    try:
        _session = requests.session()
        _session.headers = HEADERS
        # process_more_excel_files(collection, _session)
        # process_more_by_doi(collection, _session)
        # insert_project_full_doc_info(collection)
        insert_all(collection)
    finally:
        client.close()
        if _session:
            _session.close()


if __name__ == '__main__':
    fill_more_excel()
