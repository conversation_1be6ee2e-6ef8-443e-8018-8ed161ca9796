package org.biosino.lf.mash.mashweb.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.page.TableDataInfo;
import org.biosino.lf.mash.mashweb.dto.ScholarlyArchiveQueryDTO;
import org.biosino.lf.mash.mashweb.enums.ScArcQueryItemEnum;
import org.biosino.lf.mash.mashweb.service.ScholarlyArchiveService;
import org.biosino.lf.mash.mashweb.vo.HotWordAndMostRecentVO;
import org.biosino.lf.mash.mashweb.vo.ScholarlyArchiveListVO;
import org.biosino.lf.mash.mashweb.vo.SelectVO;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * MASH文献检索
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/scholarly_archive")
public class ScholarlyArchiveController {
    private final ScholarlyArchiveService scholarlyArchiveService;


    /**
     * 文献列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@Validated @RequestBody ScholarlyArchiveQueryDTO queryDTO) {
        Page<ScholarlyArchiveListVO> page = scholarlyArchiveService.list(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }


    /**
     * 筛选项下拉框
     */
    @GetMapping("/selectItem")
    public List<SelectVO> selectItem() {
        return ScArcQueryItemEnum.getSelectVOList();
    }


    /**
     * 热点检索词
     */
    @GetMapping("/findHotWordAndMostRecent")
    public HotWordAndMostRecentVO findHotWordAndMostRecent() {
        return scholarlyArchiveService.findHotWordAndMostRecent();
    }

}
