import request from '@/utils/request';
import { nanoid } from 'nanoid';
import { router_info_export } from '@/router';

// 发送请求：新增访问日志
function addVisitsLog(data) {
  return request({
    url: `/visitsLog/addVisitsLog`,
    method: 'post',
    data: data,
  });
}

// 获取访问日志统计数据
export function getVisitStatistics(token) {
  return request({
    url: '/visitsLog/statistics',
    method: 'get',
    headers: {
      'X-Stats-Token': token,
    },
  });
}

// 获取或创建sessionId
function getSessionId() {
  let sessionId = sessionStorage.getItem('mash_visit_ses_id_v1');
  if (!sessionId) {
    sessionId = nanoid();
    sessionStorage.setItem('mash_visit_ses_id_v1', sessionId);
  }
  return sessionId;
}

// 获取浏览器信息
function getBrowserInfo() {
  return navigator.userAgent;
}

// 新增访问日志
export function doAddVisitsLog(route, functionModule, data) {
  /*if (
    route.path === '/' ||
    route.path === '' ||
    route.path.startsWith('/visits-log') ||
    route.path.startsWith('/esIndex')
  ) {
    return;
  }*/
  const router_infos = router_info_export;
  let path = route.path.toLowerCase();
  path = path.endsWith('/') ? path.substring(0, path.lastIndexOf('/')) : path;
  let is_log_path = false;
  for (let i = 0; i < router_infos.length; i++) {
    let item = router_infos[i];
    if (path === item.path || item.path.startsWith(`${path}/`)) {
      is_log_path = !!item.headTitle;
      break;
    }
  }
  if (!is_log_path) {
    return;
  }

  const logData = {
    sessionId: getSessionId(),
    url: path,
    functionModule: functionModule,
    browser: getBrowserInfo(),
    queryObj: Object.keys(route.query).length ? route.query : null,
    paramsObj: Object.keys(route.params).length ? route.params : null,
    dataObj: data ? data : null,
  };

  // 发送访问日志
  addVisitsLog(logData).catch(err => {
    console.error('Failed to record visit log:', err);
  });
}
