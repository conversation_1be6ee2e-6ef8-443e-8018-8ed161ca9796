<template>
  <div class="mash-container">
    <div class="app-container swiper-no-swiping">
      <swiper
        ref="mashSwiper"
        :no-swiping="true"
        direction="vertical"
        :modules="modules"
        :space-between="0"
        :mousewheel="{ mousewheel: true }"
        :pagination="{ clickable: true }"
        :effect="'fade'"
        :fade-effect="{ crossFade: true }"
        class="main-swiper"
        @swiper="onSwiper"
        @slide-change="onMashSlideChange"
        @transition-end="onTransitionEnd"
      >
        <swiper-slide>
          <div class="main-content">
            <div class="home-content d-flex align-items-center">
              <div class="container-fluid">
                <!-- 顶部区域 -->
                <div class="hero-section">
                  <h1 class="main-title">
                    Microbiome Atlas for Science of Hydrosphere (MASH)
                  </h1>
                  <h2 class="sub-title">
                    <span class="prefix-text">{{ prefixText }}</span
                    >{{ suffixText }}<span class="cursor">|</span>
                  </h2>

                  <!-- 搜索框 -->
                  <div class="search-box d-flex pos-relative">
                    <el-select
                      v-model="selectValue"
                      :teleported="false"
                      placeholder="Omics data"
                      size="large"
                      style="width: 280px"
                    >
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <el-input
                      v-model="searchQuery"
                      placeholder="Natural conditions or sample/project/experiment id"
                      class="search-input"
                      @keyup.enter="searchData"
                    >
                    </el-input>
                    <el-button class="search-btn" circle @click="searchData">
                      <el-icon size="22">
                        <Search />
                      </el-icon>
                    </el-button>
                  </div>

                  <!-- 标签组 -->
                  <div class="d-flex justify-center">
                    <div class="tag-group mt-2">
                      <el-tag
                        @click="searchByExample('bio_project_id', 'PRJDB3905')"
                        >PRJDB3905
                      </el-tag>
                      <el-tag
                        @click="searchByExample('experiment_id', 'DRX032701')"
                        >DRX032701
                      </el-tag>
                      <el-tag @click="searchByExample('country', 'China')"
                        >China
                      </el-tag>
                      <el-tag
                        @click="searchByExample('hydrosphere_type', 'Marine')"
                        >Marine
                      </el-tag>
                      <el-tag
                        @click="
                          searchByExample(
                            'water_body_type_by_classification',
                            'Groundwater',
                          )
                        "
                        >Groundwater
                      </el-tag>
                      <el-tag
                        @click="
                          searchByExample(
                            'water_body_type_by_geographic',
                            'North Pacific',
                          )
                        "
                        >North Pacific
                      </el-tag>
                      <el-tag
                        @click="
                          searchByExample('water_body_name', 'Yangtze River')
                        "
                        >Yangtze River
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </swiper-slide>
        <swiper-slide>
          <div class="map-statistic pos-relative" style="padding-top: 120px">
            <div class="container-fluid">
              <el-row>
                <el-col :span="24">
                  <div class="world-map">
                    <el-form
                      :inline="true"
                      :model="form"
                      class="demo-form-inline"
                      label-position="top"
                    >
                      <el-form-item label="Display By">
                        <el-select
                          v-model="form.displayBy"
                          :teleported="false"
                          placeholder=""
                          @change="loadMapPoints"
                        >
                          <el-option
                            value="Project"
                            label="Project"
                          ></el-option>
                          <el-option value="Run" label="Run"></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="Select By Hydrosphere Type">
                        <el-select
                          v-model="form.hydrosphereType"
                          :teleported="false"
                          placeholder=""
                          @change="loadMapPoints"
                        >
                          <el-option value="All" label="All"></el-option>
                          <el-option
                            value="Inland water"
                            label="Inland water"
                          ></el-option>
                          <el-option value="Marine" label="Marine"></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="Select By Water Body Type">
                        <el-cascader
                          v-model="form.waterBodyType"
                          :options="waterBodyTypeOptions"
                          @change="loadMapPoints"
                        />
                      </el-form-item>
                      <el-form-item label="Legend By">
                        <el-select
                          v-model="form.legend"
                          :teleported="false"
                          placeholder=""
                          @change="loadMapPoints"
                        >
                          <el-option
                            value="Hydrosphere Type"
                            label="Hydrosphere Type"
                          ></el-option>
                          <el-option
                            value="Data Source"
                            label="Data Source"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-form>
                    <div
                      id="homeMap"
                      v-loading="mapLoading || geoDataLoading"
                      class="map-container"
                    ></div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div v-if="form.legend === 'Hydrosphere Type'" class="legend">
              <div>
                <div
                  class="circle"
                  style="background: #f2d643 !important"
                ></div>
                <div>Marine</div>
              </div>
              <div>
                <div
                  class="circle"
                  style="background: #e69100 !important"
                ></div>
                <div>Inland Water</div>
              </div>
              <div>
                <div
                  style="background: #c1232b !important"
                  class="circle"
                ></div>
                <div>Critical Zone</div>
              </div>
            </div>
            <div v-else-if="form.legend === 'Data Source'" class="legend">
              <div>
                <div
                  class="circle"
                  style="background: #2ec5c8 !important"
                ></div>
                <div>AntNest</div>
              </div>
              <div>
                <div
                  style="background: #feaecb !important"
                  class="circle"
                ></div>
                <div>NODE</div>
              </div>
            </div>
            <el-button
              round
              class="explore-data mt-2"
              size="large"
              style="position: absolute; right: 30px; bottom: 20px"
            >
              <span class="btn-label" @click="router.push({ path: '/browse' })">
                Explore Data
              </span>
            </el-button>
          </div>
        </swiper-slide>
        <swiper-slide>
          <div class="anal-result pos-relative">
            <div class="container-fluid">
              <!-- Biota of Analysis 部分 -->
              <div class="biota-section">
                <div class="container">
                  <div class="biota-content">
                    <div class="text-content">
                      <h2>Biota of Analysis</h2>
                      <p class="text-justify">
                        We curated high-quality, processed metagenomic data,
                        with an emphasis on broad coverage across various sample
                        types and regions. Based on the above data, users can
                        conduct bioinformatics analyses, including but not
                        limited to diversity analysis, functional analysis, and
                        biogeographical analysis.
                      </p>
                      <a href="javascript:void(0);">
                        <el-button
                          class="explore-btn"
                          title="Coming soon"
                          @click="proxy.$modal.msgWarning('It will come soon')"
                        >
                          Explore More
                          <el-icon color="#FFAC12" size="18" class="ml-05">
                            <Right />
                          </el-icon>
                        </el-button>
                      </a>
                    </div>
                    <div class="image-content">
                      <img
                        src="@/assets/images/home-biota.png"
                        alt="Biota Analysis"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </swiper-slide>
        <swiper-slide>
          <div class="anal-result scholarly">
            <div class="container-fluid">
              <!-- Scholarly Archive 部分 -->
              <div class="biota-section">
                <div class="biota-content">
                  <div class="text-content">
                    <h2 class="text-white">Scholarly Archive</h2>
                    <p class="text-intro text-justify">
                      Based on AI models, users can start with research papers
                      to find papers of interest and related data, enabling
                      integrated linkage between papers, data, and analysis
                      results.
                    </p>
                    <router-link to="/scholarly">
                      <el-button class="explore-btn scholarly-btn">
                        Explore More
                        <el-icon color="#FFAC12" size="18" class="ml-05">
                          <Right />
                        </el-icon>
                      </el-button>
                    </router-link>
                  </div>
                  <div class="image-content">
                    <img
                      src="@/assets/images/scholarly.png"
                      alt="Biota Analysis"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </swiper-slide>
        <swiper-slide>
          <div id="slide3" class="datasets">
            <div
              class="container-fluid"
              style="width: 1640px !important; max-width: 1640px !important"
            >
              <h3 class="home-title">Feature Datasets</h3>
              <swiper
                :modules="modules"
                :slides-per-view="4"
                :space-between="10"
                :pagination="{ clickable: true }"
                :scrollbar="{ draggable: true }"
                :loop="true"
                :autoplay="{ delay: 4000 }"
                @swiper="onTeamSwiper"
              >
                <swiper-slide key="1">
                  <div class="link">
                    <div class="content">
                      <h4>
                        Microbiome Altas Sino Hydrosphere Ocean (MASH Ocean)
                      </h4>
                      <div class="dataset-content text-justify">
                        <span class="font-600">MASH Ocean </span>
                        focuses on an oceanic database, providing data,
                        services, and support for related research. With the
                        increasing severity of water pollution and global
                        climate change, understanding the diversity, functions,
                        and distribution of aquatic microorganisms has become
                        increasingly important.
                      </div>
                      <div class="cooperators mb-1 mt-1">
                        <div class="font-600">cooperators</div>
                        Shanghai Jiao Tong University
                        <div class="mt-1">
                          <a
                            href="javascript:void(0);"
                            class="text-link"
                            @click="toBrowseSearchDataset('mash_ocean_v2')"
                            >Explore 1358 related projects in MASH Data Browsing
                          </a>
                        </div>
                      </div>

                      <div class="mt-1">
                        <el-button
                          class="explore-btn"
                          @click="
                            openExternalLink(
                              'https://www.biosino.org/mash-ocean/',
                            )
                          "
                        >
                          Explore More
                          <el-icon color="#FFAC12" size="18" class="ml-05">
                            <Right />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide key="2">
                  <div class="link">
                    <div class="content">
                      <h4>
                        Mariana Trench Environment and Ecology Research (MEER)
                      </h4>
                      <div class="dataset-content text-justify">
                        <span class="font-600">MEER</span>
                        focuses on fundamental scientific inquiries in hadal
                        trenches, investigating key areas including the
                        formation and evolution of Earth's hadal trench systems,
                        the origin of life and environmental adaptation,
                        biodiversity, and climate change. The project was
                        initiated during the TS-21 cruise aboard the research
                        vessel Tan-Suo-Yi-Hao.
                      </div>
                      <div class="cooperators mb-1 mt-1">
                        <div class="font-600">cooperators</div>
                        Shanghai Jiao Tong University
                        <div class="mt-1">
                          <a
                            href="javascript:void(0);"
                            class="text-link"
                            @click="toBrowseSearchDataset('meer')"
                            >Explore 2 related projects in MASH Data Browsing
                          </a>
                        </div>
                      </div>

                      <div class="mt-1">
                        <el-button
                          class="explore-btn"
                          @click="
                            openExternalLink(
                              'https://www.biosino.org/mash/meer',
                            )
                          "
                        >
                          Explore More
                          <el-icon color="#FFAC12" size="18" class="ml-05">
                            <Right />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide key="3">
                  <div class="link">
                    <div class="content">
                      <h4>The Global Crysphere Viruses Database (GCVD)</h4>
                      <div class="dataset-content text-justify">
                        <span class="font-600">GCVD</span>
                        includes three sub-datasets: glaciers (published),
                        permafrost, and lakes. Users can browse the global
                        cryosphere samples (through the map below) as well as
                        virus genome sequence analysis and results (online)
                      </div>
                      <div class="cooperators mb-1 mt-1">
                        <div class="font-600">cooperators</div>
                        Center for the Pan-third Pole
                        <br />
                        Environment, Lanzhou University
                      </div>
                      <div class="mt-1">
                        <el-button
                          class="explore-btn"
                          @click="
                            openExternalLink(
                              'https://dev.biosino.org/keep-gcvd',
                            )
                          "
                        >
                          Explore More
                          <el-icon color="#FFAC12" size="18" class="ml-05">
                            <Right />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide key="4">
                  <div class="link">
                    <div class="content">
                      <h4>A global viral oceanography database (gVOD)</h4>
                      <div class="dataset-content text-justify">
                        <span class="font-600">GVOD</span>
                        is a user-friendly and comprehensive database of marine
                        viral ecology designed to provide an integrated platform
                        for experimentalists, field investigations, and modelers
                        to obtain data on marine virus abundance, virus
                        activity, and their host and ambient environment.
                      </div>
                      <div class="cooperators mb-1 mt-1">
                        <div class="font-600">cooperators</div>
                        Institute for Advanced Study
                        <br />
                        Shenzhen University
                      </div>
                      <div class="mt-1">
                        <el-button
                          class="explore-btn"
                          @click="
                            openExternalLink('https://www.biosino.org/gvod')
                          "
                        >
                          Explore More
                          <el-icon color="#FFAC12" size="18" class="ml-05">
                            <Right />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide key="5">
                  <div class="link">
                    <div class="content">
                      <h4>
                        Microbiome Altas Sino Hydrosphere Chinasea （MASH
                        Chinasea）
                      </h4>
                      <div class="dataset-content text-justify">
                        <span class="font-600">MASH Chinasea</span>
                        focuses on integrating metagenomic data from China's
                        seas. Through this platform, users can easily explore
                        the distribution of specific microbial genes across
                        different regions and depths of China's marine areas, as
                        well as perform functional comparisons and analyses
                        across datasets.
                      </div>
                      <div class="cooperators mb-1 mt-1">
                        <div class="font-600">cooperators</div>
                        Xiamen University
                        <div class="mt-1">
                          <a
                            href="javascript:void(0);"
                            class="text-link"
                            @click="toBrowseSearchDataset('mash_china_sea')"
                            >Explore 104 related projects in MASH Data Browsing
                          </a>
                        </div>
                      </div>
                      <div class="mt-1">
                        <el-button
                          class="explore-btn"
                          disabled
                          title="Coming soon"
                        >
                          Explore More
                          <el-icon color="#FFAC12" size="18" class="ml-05">
                            <Right />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </swiper-slide>
              </swiper>
            </div>
          </div>
        </swiper-slide>
        <swiper-slide>
          <!-- Submit to MASH 部分 -->
          <div class="anal-result submit-section">
            <div class="container-fluid">
              <h2 class="text-white">Submit to MASH</h2>
              <p class="submit-description">
                MASH aims to provide a centralized platform for the collection
                and sharing of global aquatic microbiome data. We encourage
                users to contribute their data to help us continuously explore
                and enrich our database.
              </p>
              <p class="submit-description mt-1-5">
                Omics data can be submitted through the
                <a
                  class="submit-link"
                  href="https://www.biosino.org/node"
                  target="_blank"
                  >NODE</a
                >
                , while sequence data can be submitted via the
                <a
                  class="submit-link"
                  href="https://www.biosino.org/elmsg"
                  target="_blank"
                  >eLMSG</a
                >
                platform. All submitted data will be integrated into our
                database, driving collaborative research and discovery.
              </p>
              <div class="submit-options">
                <div class="submit-card">
                  <h3>Submit omics data by NODE</h3>
                  <p>
                    The National Omics Data Encyclopedia (NODE) is an
                    integrated, multi-omics platform that aggregates, manages
                    and compares diverse omics data across different sample
                    types.
                  </p>
                  <p>
                    NODE enables the seamless integration and standardised
                    storage of heterogeneous omics data while maintaining
                    compatibility with global repositories such as SRA and
                    ProteomeXchange. By facilitating cross-disciplinary research
                    collaboration, the platform enhances the credibility of
                    research, accelerates the sharing of data among scientists,
                    and increases the reuse of high-quality omics datasets.
                  </p>
                  <a href="https://www.biosino.org/node" target="_blank">
                    <img
                      src="@/assets/images/node.png"
                      alt="MASH Logo"
                      class="logo"
                    />
                  </a>
                </div>
                <div class="submit-card">
                  <h3>Submit sequence data by eLMSG</h3>
                  <p>
                    An eLibrary of Microbial Systematics and Genomics (eLMSG) is
                    a database platform that provides foundational data for the
                    polyphasic taxonomy and identification of microbial type
                    species, genome sequences with high-quality annotations, as
                    well as core and pan-genome information of populations.
                  </p>
                  <p>
                    The platform also supports the submission of genome data for
                    both isolated strains and metagenome-assembled strains
                  </p>
                  <a href="https://www.biosino.org/elmsg" target="_blank">
                    <img src="@/assets/images/elmsg.png" class="logo" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>
<script setup>
  import { Search } from '@element-plus/icons-vue';
  import 'swiper/css';
  import 'swiper/css/pagination'; // 轮播图底面的小圆点
  import 'swiper/css/navigation';
  import 'swiper/css/scrollbar';
  import { useRouter } from 'vue-router';
  // 这些已经在main.js里面引入过了
  // import 'leaflet/dist/leaflet.css';
  // import L from 'leaflet';
  // import '@/assets/js/geojson-vt';
  // import '@/assets/js/leaflet-geojson-vt.js';
  import {
    getCurrentInstance,
    nextTick,
    onBeforeUnmount,
    onMounted,
    reactive,
    ref,
    watch,
  } from 'vue';

  import {
    A11y,
    Autoplay,
    Mousewheel,
    Navigation,
    Pagination,
    Scrollbar,
  } from 'swiper/modules';

  // Import Swiper Vue.js components
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { trimStr } from '@/utils';
  import axios from 'axios';

  import {
    getHomeMapLocation,
    getWaterBodyTypeByHydrosphere,
  } from '@/api/samples';

  const router = useRouter();

  const { proxy } = getCurrentInstance();
  let slide = proxy.$route.query.slide;
  // 定义Canvas渲染器，用于地图点位渲染
  const canvasRenderer = L.canvas({ padding: 0.5 });

  // 添加地理数据的ref
  const oceanData = ref(null);
  const lakesData = ref(null);
  const riversData = ref(null);
  const geoDataLoading = ref(true);

  // 滚动到指定slide
  const scrollToSlide = index => {
    if (!index) return;

    const slideIndex = parseInt(index);
    if (isNaN(slideIndex) || slideIndex < 0 || slideIndex >= 7) return;

    mashSwiper.value.slideTo(slideIndex);

    // 更新路由参数而不是清空它
    if (proxy.$route.query.slide !== index.toString()) {
      router.replace({
        path: proxy.$route.path,
        query: { ...proxy.$route.query, slide: index },
      });
    }
  };

  // 异步获取地理数据的函数
  const fetchGeoData = async () => {
    try {
      geoDataLoading.value = true;
      // 获取基础路径
      const basePath = import.meta.env.VITE_APP_PUBLIC_PATH || '/';
      const [oceanResponse, lakesResponse, riversResponse] = await Promise.all([
        axios.get(`${basePath}/geojson/ocean.json`),
        axios.get(`${basePath}/geojson/sample_lakes.json`),
        axios.get(`${basePath}/geojson/sample_rivers.json`),
      ]);

      oceanData.value = oceanResponse.data;
      lakesData.value = lakesResponse.data;
      riversData.value = riversResponse.data;
      return true;
    } catch (error) {
      console.error('加载地理数据失败:', error);
      return false;
    } finally {
      geoDataLoading.value = false;
    }
  };

  const modules = [
    Mousewheel,
    Pagination,
    Navigation,
    Scrollbar,
    A11y,
    Autoplay,
  ];
  const selectValue = ref('Omics data');
  const searchQuery = ref('');

  const options = [
    {
      value: 'Omics data',
      label: 'Omics data',
    },
    {
      value: 'Expert Q&A',
      label: 'Expert Q&A',
    },
    {
      value: 'Hydrosphere publications',
      label: 'Hydrosphere publications',
    },
  ];
  const form = reactive({
    displayBy: 'Project',
    hydrosphereType: 'All',
    waterBodyType: ['water_body_type_by_geographic', 'All'],
    legend: 'Hydrosphere Type',
  });

  let waterBodyTypeOptions = ref([
    {
      value: 'water_body_type_by_geographic',
      label: 'Geolocation',
      children: [
        {
          label: 'All',
          value: 'All',
        },
      ],
    },
    {
      value: 'water_body_type_by_classification',
      label: 'Water Body Type',
      children: [
        {
          label: 'All',
          value: 'All',
        },
      ],
    },
  ]);
  const map = ref(null);

  // swiper实例
  const homeSwiper = ref(null);
  const teamSwiper = ref(null);
  const mashSwiper = ref(null);

  const onSwiper = swiper => {
    mashSwiper.value = swiper;
  };
  const onTeamSwiper = swiper => {
    teamSwiper.value = swiper;
  };
  // swiper切换时触发
  const onMashSlideChange = swiper => {
    mashSwiper.value = swiper;

    // 当滑动改变时，更新路由参数而不是清空它
    const activeIndex = swiper.activeIndex;
    if (activeIndex !== parseInt(proxy.$route.query.slide)) {
      router.replace({
        path: proxy.$route.path,
        query: { ...proxy.$route.query, slide: activeIndex },
      });
    }
  };

  // swiper过渡结束时触发
  const onTransitionEnd = swiper => {
    mashSwiper.value = swiper;
  };

  // 添加mapPoints属性用于存储地图点位数据
  const mapPoints = ref([]);

  // 添加地图点颜色映射，根据不同的legend值映射不同的颜色
  const colorMapping = {
    // 第一组颜色 - 使用#E69100
    'Marine': '#f2d643',
    // 第二组颜色 - 使用#F2D643
    'Inland water': '#e69100',
    'Critical Zone': '#c1232b',
    'AntNest': '#2EC5C8',
    'NODE': '#FEAECB',
  };

  // 监听路由查询参数变化，控制滑动到指定的屏幕
  watch(
    () => proxy.$route.query,
    newQuery => {
      if (newQuery.slide) {
        slide = newQuery.slide;
        // 确保mashSwiper已经初始化
        if (mashSwiper.value) {
          scrollToSlide(slide);
        }
      }
    },
  );

  function initMap() {
    // 检查所有地理数据是否已加载
    if (!oceanData.value || !lakesData.value || !riversData.value) {
      console.warn('地理数据尚未加载完成，正在重新获取...');
      fetchGeoData().then(success => {
        if (success) {
          initMap();
        } else {
          proxy.$modal.msgError('加载地图数据失败，请刷新页面重试');
        }
      });
      return;
    }

    var latlng = L.latLng(30, 0);

    // 获取地图容器宽度
    const mapContainer = document.getElementById('homeMap');
    const containerWidth = mapContainer.offsetWidth;

    // 根据容器宽度设置初始缩放级别
    const initialZoom = containerWidth > 1200 ? 3 : 2;

    map.value = L.map('homeMap', {
      center: latlng,
      zoom: initialZoom,
      minZoom: 2,
      maxZoom: 18,
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    // 设置地图背景色为白色（陆地颜色）
    document.querySelector('.leaflet-container').style.backgroundColor =
      '#ffffff';

    map.value.createPane('oceanPane');
    map.value.createPane('riverPane');
    map.value.createPane('pointsPane');

    map.value.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.value.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.value.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    // 修改海洋渲染方式，使用矢量瓦片方式渲染
    const oceanLayer = L.geoJson
      .vt(oceanData.value, {
        maxZoom: 18,
        tolerance: 3,
        style: {
          fillColor: '#1C4F80',
          weight: 1,
          opacity: 1,
          color: 'rgba(0, 0, 0, 0)',
          fillOpacity: 1,
          pane: 'oceanPane',
          renderer: canvasRenderer,
        },
      })
      .addTo(map.value);

    // 遍历海洋的features，创建标签
    oceanData.value.features.forEach(feature => {
      let labelLatLng;
      // 根据特征名称选择标签位置
      if (feature.properties.name === 'North Pacific Ocean') {
        labelLatLng = L.latLng(30, -150);
      } else if (feature.properties.name === 'South Pacific Ocean') {
        labelLatLng = L.latLng(-30, -140);
      } else if (feature.properties.name === 'Baltic Sea') {
        labelLatLng = L.latLng(59, 21);
      } else if (feature.properties.name === 'Southern Ocean') {
        labelLatLng = L.latLng(-64, 20);
      } else {
        // 默认使用中心点
        const bounds = L.geoJSON(feature).getBounds();
        labelLatLng = bounds.getCenter();
      }

      // 创建一个标记
      var label = L.marker(labelLatLng, {
        icon: L.divIcon({
          className: 'ocean-label',
          html: feature.properties.name,
          iconSize: [100, 20],
        }),
      });

      label.addTo(map.value); // 将标签添加到地图
    });

    // 添加 lake 图层（矢量瓦片方式）
    const lakeLayer = L.geoJson
      .vt(lakesData.value, {
        maxZoom: 14,
        tolerance: 3,
        style: {
          fillColor: '#9ABAE7',
          weight: 1,
          opacity: 1,
          color: 'rgba(0, 0, 0, 0)',
          fillOpacity: 1,
          pane: 'oceanPane',
          renderer: canvasRenderer,
        },
      })
      .addTo(map.value);

    // 遍历 lakes 的 features，创建标签
    lakesData.value.features.forEach(feature => {
      const bounds = L.geoJSON(feature).getBounds();
      const center = bounds.getCenter();

      const label = L.marker(center, {
        icon: L.divIcon({
          iconSize: [100, 20],
          className: 'lake-label',
          html: map.value.getZoom() > 4 ? feature.properties.Name : '',
        }),
      });

      label.addTo(map.value);

      // 控制缩放时是否显示 label
      map.value.on('zoomend', () => {
        const zoom = map.value.getZoom();
        label.setIcon(
          L.divIcon({
            className: 'lake-label',
            html: zoom > 4 ? feature.properties.Name : '',
          }),
        );
      });
    });

    // 使用 geoJson.vt 渲染瓦片图层
    const riverLayer = L.geoJson
      .vt(riversData.value, {
        maxZoom: 14,
        tolerance: 3,
        style: {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          renderer: canvasRenderer,
          pane: 'riverPane',
        },
      })
      .addTo(map.value);

    // 遍历原始 GeoJSON feature，添加标签
    riversData.value.features.forEach(feature => {
      const layerBounds = L.geoJSON(feature).getBounds();

      const label = L.marker(layerBounds.getCenter(), {
        icon: L.divIcon({
          iconSize: [100, 20],
          className: 'lake-label',
          html: map.value.getZoom() > 4 ? feature.properties.name : '',
        }),
      });

      label.addTo(map.value);

      map.value.on('zoomend', () => {
        const riverZoom = map.value.getZoom();
        label.setIcon(
          L.divIcon({
            className: 'lake-label',
            html: riverZoom > 4 ? feature.properties.name : '',
          }),
        );
      });
    });

    // 地图初始化完成后，加载点位数据
    nextTick(() => {
      loadMapPoints();
    });
  }

  onMounted(async () => {
    if (slide) {
      mashSwiper.value.slideTo(slide);
    }

    // 加载水体类型选项
    loadWaterBodyTypeOptions();

    // 首先加载地理数据，然后初始化地图
    await fetchGeoData();
    nextTick(() => {
      initMap();
    });
  });

  // 组件卸载前清理资源
  onBeforeUnmount(() => {
    // 如果地图已初始化，则移除
    if (map.value) {
      map.value.remove();
      map.value = null;
    }
  });

  function toBrowseSearchDataset(field) {
    router.push({
      path: '/browse',
      query: {
        dataset: field,
      },
    });
  }

  const prefixText = ref('');
  const suffixText = ref('');

  const prefixContent = 'Explore the ';
  const textOptions = ['Omics data', 'Expert Q&A', 'Hydrosphere publications'];

  let currentTextIndex = 0;
  let isDeleting = false;
  let isPrefixComplete = false;

  const typingSpeed = 150;
  const deletingSpeed = 150;
  const pauseTime = 1500;

  const autoTypeEffect = () => {
    if (!isPrefixComplete) {
      prefixText.value = prefixContent.slice(0, prefixText.value.length + 1);
      if (prefixText.value.length < prefixContent.length) {
        return setTimeout(autoTypeEffect, typingSpeed);
      }
      isPrefixComplete = true;
      return setTimeout(autoTypeEffect, typingSpeed);
    }

    const currentText = textOptions[currentTextIndex];

    if (isDeleting) {
      suffixText.value = currentText.slice(0, suffixText.value.length - 1);
      if (suffixText.value.length > 0) {
        return setTimeout(autoTypeEffect, deletingSpeed);
      }
      isDeleting = false;
      currentTextIndex = (currentTextIndex + 1) % textOptions.length;
      return setTimeout(autoTypeEffect, 500);
    }

    suffixText.value = currentText.slice(0, suffixText.value.length + 1);
    if (suffixText.value.length < currentText.length) {
      return setTimeout(autoTypeEffect, typingSpeed);
    }

    setTimeout(() => {
      isDeleting = true;
      autoTypeEffect();
    }, pauseTime);
  };

  onMounted(() => {
    // 启动自动打字效果
    setTimeout(autoTypeEffect, 500);

    if (slide) {
      mashSwiper.value.slideTo(slide);
    }

    // 加载水体类型选项
    loadWaterBodyTypeOptions();
  });

  // 新增搜索跳转函数
  const searchData = () => {
    const val = trimStr(searchQuery.value);
    if (!val) {
      return;
    }

    // console.log(selectValue.value);
    if (selectValue.value === 'Expert Q&A') {
      // 跳转到专家问答页面并带上搜索参数
      router.push({
        path: '/expert-qa',
        query: {
          keyword: val,
        },
      });
    } else if (selectValue.value === 'Hydrosphere publications') {
      // 跳转到专家问答页面并带上搜索参数
      router.push({
        path: '/scholarly',
        query: {
          keyword: val,
        },
      });
    } else {
      // 跳转到browse页面并带上搜索参数
      router.push({
        path: '/browse',
        query: {
          keyword: val,
        },
      });
    }
  };

  // 点击示例标签时的跳转函数
  function searchByExample(field, value) {
    searchQuery.value = value;
    router.push({
      path: '/browse',
      query: {
        field: field,
        keyword: searchQuery.value.trim(),
      },
    });
  }

  function openExternalLink(url) {
    window.open(url, '_blank');
  }

  // 监听hydrosphereType变化，更新waterBodyTypeOptions
  watch(
    () => form.hydrosphereType,
    newHydrosphereType => {
      // 当水文圈类型变化时，更新水体类型选项
      updateWaterBodyTypeOptions(newHydrosphereType);
    },
  );

  // 加载水体类型级联选择器数据
  const loadWaterBodyTypeOptions = () => {
    // 使用当前选择的hydrosphereType加载水体类型选项
    updateWaterBodyTypeOptions(form.hydrosphereType);
  };

  // 根据水文圈类型更新水体类型选项
  const updateWaterBodyTypeOptions = hydrosphereType => {
    // 设置loading状态
    mapLoading.value = true;

    // 更新Geographic选项
    getWaterBodyTypeByHydrosphere({
      hydrosphereType: hydrosphereType,
      category: 'water_body_type_by_geographic',
    })
      .then(response => {
        if (response.data && response.data.length) {
          const options = response.data.map(item => ({
            label: item,
            value: item,
          }));

          // 确保包含'All'选项
          if (!options.find(item => item.value === 'All')) {
            options.unshift({ label: 'All', value: 'All' });
          }

          // 更新选项
          waterBodyTypeOptions.value[0].children = options;
        }
      })
      .catch(error => {
        console.error(
          'Failed to load Geographic water body type options:',
          error,
        );
      });

    // 更新Classification选项
    getWaterBodyTypeByHydrosphere({
      hydrosphereType: hydrosphereType,
      category: 'water_body_type_by_classification',
    })
      .then(response => {
        if (response.data && response.data.length) {
          const options = response.data.map(item => ({
            label: item,
            value: item,
          }));

          // 确保包含'All'选项
          if (!options.find(item => item.value === 'All')) {
            options.unshift({ label: 'All', value: 'All' });
          }

          // 更新选项
          waterBodyTypeOptions.value[1].children = options;
        }
      })
      .catch(error => {
        console.error(
          'Failed to load Classification water body type options:',
          error,
        );
      })
      .finally(() => {
        mapLoading.value = false;
      });
  };

  let mapLoading = ref(false);

  // 加载地图点位数据
  function loadMapPoints() {
    // 构建查询参数，从form中获取
    const params = {
      displayBy: form.displayBy,
      hydrosphereType: form.hydrosphereType || 'All',
      waterBodyType:
        form.waterBodyType && form.waterBodyType.length > 1
          ? form.waterBodyType[1]
          : 'All',
      waterBodyTypeCategory:
        form.waterBodyType && form.waterBodyType.length > 0
          ? form.waterBodyType[0]
          : '',
      legend: form.legend || '',
    };
    geoDataLoading.value = true;
    // 调用接口获取数据
    getHomeMapLocation(params)
      .then(response => {
        if (response && response.data) {
          mapPoints.value = response.data;

          // 更新地图点位
          renderMapPoints();
        } else {
          console.warn('API返回成功但没有数据');
        }
      })
      .catch(error => {
        console.error('加载地图点位失败:', error);
      })
      .finally(() => {
        geoDataLoading.value = false;
      });
  }

  // 渲染地图点位
  const renderMapPoints = () => {
    // 如果地图尚未初始化，则直接返回
    if (!map.value) {
      return;
    }

    // 先清除所有现有的点位图层
    map.value.eachLayer(layer => {
      // 只移除点位图层，保留基础地图图层
      if (layer instanceof L.LayerGroup && !(layer instanceof L.GeoJSON)) {
        map.value.removeLayer(layer);
      }
    });

    // 创建新的点位图层组
    const pointsLayer = L.layerGroup();
    let markers = []; // 用于保存所有marker

    // 按legendValue分组
    const groupedPoints = {};

    if (!mapPoints.value || mapPoints.value.length === 0) {
      return;
    }

    mapPoints.value.forEach(point => {
      const legendValue = point.legendValue || 'Default';
      if (!groupedPoints[legendValue]) {
        groupedPoints[legendValue] = [];
      }
      groupedPoints[legendValue].push(point);
    });

    // 为每个分组创建marker
    Object.keys(groupedPoints).forEach(legendValue => {
      const points = groupedPoints[legendValue];

      // 确定颜色
      const color = colorMapping[legendValue];
      // 创建每个点的marker
      points.forEach(point => {
        const circleMarker = L.circleMarker([point.latitude, point.longitude], {
          radius: 3,
          fillColor: color,
          color: color,
          weight: 0.5,
          opacity: 1,
          fillOpacity: 0.8,
          pane: 'pointsPane',
          renderer: canvasRenderer,
        });

        // 添加点击事件，打印点位信息
        circleMarker.on('click', function (e) {
          router.push({
            path: '/browse',
            query: {
              longitude: point.longitude,
              latitude: point.latitude,
            },
          });
        });

        markers.push(circleMarker);
      });
    });

    // 一次性添加所有marker到图层
    if (markers.length > 0) {
      pointsLayer.addLayer(L.featureGroup(markers));
      // 将点位图层添加到地图
      pointsLayer.addTo(map.value);
    }
  };
</script>
<style lang="scss" scoped>
  .app-container {
    width: 100%;
    height: 100vh;
  }

  .mash-container {
    background-color: #ffffff;
  }

  .swiper {
    width: 100%;
    height: 100%;
  }

  .home-content {
    min-height: 100vh;
    background-image: url('../../assets/images/mash-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    position: relative;

    .wrap {
      padding: 120px 0;
    }

    &::before {
      background: rgb(#02113c, 0.2);
      opacity: 0.7;
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      pointer-events: none;
      z-index: 1;
    }

    .container-fluid {
      z-index: 3;
    }

    .hero-section {
      .main-title {
        color: #fff;
        font-size: 46px;
        font-weight: 600;
        text-align: center;
        margin-bottom: 20px;
      }

      .sub-title {
        font-size: 36px;
        color: #ffac12;
      }

      .tag-group {
        width: 50%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 30px;

        .el-tag {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.8);
          font-size: 18px;
          cursor: pointer;
          height: 50px;
          border-radius: 40px;
          padding: 15px 30px;
          transition: all 0.3s;
          border: 1px solid rgba(255, 255, 255, 0.6);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    .sub-title {
      color: #fff;
      font-size: 25px;
      font-weight: normal;
      text-align: center;
      margin-bottom: 40px;
    }

    .search-box {
      max-width: 58%;
      margin: 0 auto 30px;
      display: flex;
      gap: 0;

      :deep(.el-select__wrapper) {
        width: 280px !important;
        box-shadow: none;
        border: none;
        background-color: #ffffff;
        height: 60px;
        color: #333333;
        border-radius: 40px 0 0 40px;
      }

      :deep(.el-select__selected-item.el-select__placeholder),
      :deep(.el-icon.el-select__caret.el-select__icon) {
        font-size: 18px;
        color: #333 !important;
      }

      .search-input {
        :deep(.el-input__wrapper) {
          flex: 1;
          box-shadow: none;
          background: rgba(255, 255, 255, 1);
          border-radius: 0 40px 40px 0;
          border: none;
          padding-right: 70px;
        }

        :deep(.el-input__inner) {
          font-size: 18px;
        }
      }

      .search-btn {
        background: #ffac12;
        color: #182b49;
        font-weight: 600;
        border-radius: 40px;
        border: none;
        width: 50px;
        height: 50px;
        padding: 0 20px;
        position: absolute;
        right: 8px;
        top: 5px;
        z-index: 999;
      }
    }
  }

  :deep(.swiper-scrollbar) {
    display: none;
  }

  .explore-data {
    border: none;
    background-color: #d3aa4e;

    .btn-label {
      font-weight: 600 !important;
      color: #182b49 !important;
      font-size: 16px;
    }
  }

  .map-statistic {
    height: 100vh;
    //background-image: url('../../assets/images/mash-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #1c4f80;

    padding: 20px 0 40px 0;
    //background-image: url('../../assets/images/mash-statistic.png');
  }

  .world-map {
    padding: 20px 0 15px 0;

    img {
      width: 100%;
      //height: 720px;
    }
  }

  .el-form-item {
    width: 23% !important;

    &:last-child {
      margin-right: 0;
    }
  }

  .search-btn {
    background-color: #1e7cb2;
    height: 36px;
    border-radius: 0;
  }

  h3 {
    font-size: 24px !important;
  }

  .home-title {
    line-height: 1.43333;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    color: #ffffff;
  }

  :deep(.el-select__wrapper) {
    border-radius: 0;
    background-color: transparent;
    border: 1px solid #ffffff;
  }

  :deep(.el-select__placeholder),
  :deep(.el-select__icon),
  :deep(.el-select__placeholder.is-transparent) {
    color: #ffffff !important;
  }

  .world-map {
    :deep(.el-input__wrapper) {
      border-radius: 0;
      color: white !important;
      background-color: transparent;
      border: 1px solid #ffffff;
    }

    :deep(.el-input__inner) {
      color: #ffffff !important;
    }

    // 添加级联选择器样式，使其宽度与其他选择器一致
    :deep(.el-cascader) {
      width: 100%;

      .el-input,
      .el-input__wrapper {
        width: 100%;
      }
    }
  }

  .anal-result {
    min-height: 100vh;
    background-color: #edf2fa;

    background-repeat: no-repeat;
    background-size: cover;
    padding: 120px 0 40px 0;
    display: flex;
    align-items: center;
    //background-color: #f8f8f8;
    .anal-content {
      font-size: 24px;
      color: rgb(255, 255, 255, 0.8);
      text-align: justify;
    }

    .anal-left {
      height: 100vh;
      background-color: #a1b4c7;
      display: flex;
      align-items: center;
    }

    img {
      //height: 450px;
    }

    :deep(.swiper-pagination-bullet) {
      background-color: #a1b4c7;
      opacity: 1 !important;
      width: 12px !important;
      height: 12px !important;
    }

    :deep(.swiper-pagination-bullet-active) {
      background-color: #1e7cb2;
      opacity: 1;
    }

    .swiper-slide {
      display: flex;
      align-items: center;
    }
  }

  .datasets {
    background-color: #edf2fa;
    min-height: 100vh;
    padding: 100px 0 40px 0;
    display: flex;
    align-items: center;

    .home-title {
      font-size: 42px !important;
      color: #182b49 !important;
    }

    :deep(.swiper-pagination) {
      position: relative;
      top: 6px;
    }

    .link {
      transition: transform 0.3s; /* 添加过渡效果 */
      position: relative;
      z-index: 800;
      padding: 10px;
    }

    .dataset {
      background-color: #fff !important;
      display: flex;
      align-items: center;
      column-gap: 30px;
      border: 1px solid #e4e4e4;
      padding: 15px;
    }

    img {
      mix-blend-mode: multiply;
      width: 100%;
      height: auto;
    }

    .content {
      box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.12);
      background-color: #ffffff;
      padding: 20px;
      min-height: 493px;
      border-radius: 25px;

      h4 {
        text-align: justify;
        font-size: 18px;
        position: relative;
      }

      .text-justify {
        height: 175px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 7;
      }

      .text-link {
        color: #ffac12;
        text-decoration: underline;
      }
    }

    .cooperators {
      min-height: 119px;
    }
  }

  :deep(.el-form-item__label) {
    color: rgb(255, 255, 255, 0.9);
  }

  .legend {
    padding: 6px 15px;
    background: rgba(255, 255, 255, 0.8);
    color: #333;
    position: absolute;
    bottom: 10px;
    z-index: 999;
    left: 30px;
    font-size: 16px;
    border-radius: 2px;
  }

  .legend > div {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  }

  .circle {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    background: #f2d643;
    margin-right: 6px;
  }

  .search-box {
    max-width: 800px;
    margin: 0 auto 30px;
    display: flex;
    gap: 15px;

    .search-input {
      :deep(.el-input__wrapper) {
        background: rgba(255, 255, 255, 0.9);
      }
    }

    .explore-btn {
      background: #d3aa4e;
      border: none;
      color: #182b49;
      font-weight: 600;
      padding: 0 20px;
    }
  }

  .map-section {
    background: #1c4f80;
    padding: 40px 0;

    .map-filters {
      margin-bottom: 20px;

      :deep(.el-form-item__label) {
        color: #fff;
      }

      :deep(.el-select) {
        .el-input__wrapper {
          background: transparent;
          border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .el-input__inner {
          color: #fff;
        }
      }
    }
  }

  .map-container {
    width: 100%;
    height: calc(100vh - 255px);
    min-height: 400px;
    max-height: 80vh;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }

  .map-legend {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    border-radius: 4px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 50%;

      &.ocean {
        background: #f2d643;
      }

      &.inland {
        background: #e87c25;
      }

      &.critical {
        background: #c1232b;
      }
    }
  }

  .explore-data-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: #d3aa4e;
    border: none;
    color: #182b49;
    font-weight: 600;
  }

  //biota
  .biota-section {
    .biota-content {
      display: flex;
      align-items: center;
      //gap: 60px;

      .text-content {
        width: 40%;
        //flex: 1;

        h2 {
          font-size: 42px;
          color: #182b49;
          margin-bottom: 30px;
          position: relative;
          padding-top: 20px;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 170px;
            height: 4px;
            background: linear-gradient(to right, #ffac12, #000); /* 渐变色 */
          }
        }

        p {
          font-size: 20px;
          color: #333;
          margin-bottom: 30px;
          line-height: 1.6;
        }
      }

      .image-content {
        position: absolute;
        perspective: 1000px;
        width: 1000px !important;
        transform: rotate(-5deg);
        right: -190px !important;

        img {
          width: 100%;
          height: auto;
          transform: skew(22deg) rotateY(1deg) rotateX(14deg);
          /* transform: skew(17deg) rotateY(1deg); */
          transform-origin: center;
          transition: transform 0.5s ease;
          box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  .datasets-section {
    padding: 80px 0;
    background: #f8f9fa;

    h2 {
      font-size: 42px;
      color: #182b49;
      text-align: center;
      margin-bottom: 50px;
    }

    .datasets-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 30px;
    }

    .dataset-card {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      transition: transform 0.3s;

      &:hover {
        transform: translateY(-5px);
      }

      img {
        width: 100%;
        height: 200px;
        object-fit: cover;
      }

      .dataset-content {
        padding: 20px;

        h3 {
          font-size: 18px;
          color: #182b49;
          margin-bottom: 15px;
        }

        p {
          font-size: 14px;
          color: #666;
          margin-bottom: 20px;
          line-height: 1.6;
        }
      }
    }
  }

  .explore-btn {
    height: 50px;
    width: 170px;
    background: #1b4f80;
    border: none;
    color: #ffffff;
    font-size: 16px;
    border-radius: 0 18px 0 0;
  }

  //submit
  .submit-section {
    min-height: 100vh;
    background-image: url('../../assets/images/home-submit.png');
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    position: relative;

    h2 {
      font-size: 42px;
      color: #182b49;
      text-align: center;
      margin-bottom: 20px;
    }

    .submit-description {
      text-align: justify;
      color: #ebedf1 !important;
      font-size: 20px;
      line-height: 1.6;
    }

    .submit-options {
      display: flex;
      gap: 40px;
      justify-content: center;
    }

    .submit-card {
      margin-top: 1.5rem;
      flex: 1;
      text-align: justify;
      padding: 10px 25px;
      background: #f8f9fa;
      border-radius: 25px;
      position: relative;
      min-height: 350px;

      h3 {
        font-size: 24px;
        color: #182b49;
        margin-bottom: 10px;
      }

      img {
        width: 200px;
        height: auto;
        position: absolute;
        bottom: 20px;
        left: 25px;
      }

      p {
        color: #333;
        margin-bottom: 15px;
        line-height: 1.6;
        font-size: 17px;
      }

      .submit-btn {
        background: #d3aa4e;
        border: none;
        color: #182b49;
        padding: 12px 30px;
        font-weight: 600;
      }
    }
  }

  //scholarly
  .scholarly {
    background-color: #1b4f80;

    .biota-content {
      .text-content {
        color: #ffffff !important;
      }

      .text-intro {
        color: #ebedf1 !important;
      }
    }

    .image-content {
      perspective: 1000px;
      width: 48% !important;
      right: -170px !important;

      img {
        transform: skew(22deg) rotateY(1deg) rotateX(14deg);
        /* transform: skew(17deg) rotateY(1deg); */
        transform-origin: center;
        transition: transform 0.5s ease;
        box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .scholarly-btn {
    background: #ffffff;
    border: none;
    color: #333;
  }

  .submit-link {
    text-decoration: underline;
  }

  .sub-title {
    white-space: nowrap;
  }

  .cursor {
    display: inline-block;
    animation: blink 1s step-end infinite;
  }

  @keyframes blink {
    from,
    to {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }

  .ocean-label,
  .lake-label {
    font-size: 14px;
    font-weight: 500;
    color: #424141;
    text-shadow: 0px 0px 2px #fff;
    background: transparent !important;
    border: none !important;
    text-align: center;
  }

  //.clickable-label {
  //  cursor: pointer;
  //
  //  &:hover {
  //    text-decoration: underline;
  //  }
  //}

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  //:deep(.clickable-label) {
  //  cursor: pointer;
  //  transition: all 0.2s ease;
  //
  //  &:hover {
  //    transform: scale(1.05);
  //    text-decoration: underline;
  //    font-weight: bold;
  //  }
  //}

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  .prefix-text {
    color: #ffffff; /* 确保前缀文本为白色 */
  }

  .sub-title {
    color: #ffac12; /* 变化的文本保持原来的颜色 */
  }
</style>
<style>
  .datasets .swiper-pagination-bullet {
    background-color: #c5cacc;
    opacity: 1 !important;
  }

  .datasets .swiper-pagination-bullet-active {
    background-color: #2196f3;
  }

  .swiper-pagination-bullet {
    background-color: #ffffff;
    opacity: 1 !important;
    width: 12px !important;
    height: 12px !important;
  }

  #homeMap {
    background-color: #fff;
  }

  .swiper-pagination-bullet-active {
    background-color: #2196f3;
  }

  .swiper-wrapper {
    transition-timing-function: linear !important;
  }

  .leaflet-marker-icon.ocean-label {
    color: #424141;
    font-size: 16px;
    width: 200px !important;
    font-family: arial;
  }
</style>
