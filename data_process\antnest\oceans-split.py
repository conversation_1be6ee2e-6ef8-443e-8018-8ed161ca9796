import pandas as pd

# 读取 Excel 文件
file_path = "data/MASH_Merge_250324.xlsx"
df = pd.read_excel(file_path)

# 检查是否包含所需的列
required_columns = ["Water Body Type", "Water Body Name 经纬度定位生成", "Water Body Name"]
if not all(col in df.columns for col in required_columns):
    raise ValueError("Excel 文件缺少必要的列")

# 定义可替换的水体名称
water_bodies = {
    "North Pacific Ocean": "North Pacific",
    "South Pacific Ocean": "South Pacific",
    "Arctic Ocean": "Arctic Ocean",
    "Baltic Sea": "Baltic Sea",
    "Indian Ocean": "Indian Ocean",
    "Mediterranean Sea": "Mediterranean Sea",
    "North Atlantic Ocean": "North Atlantic",
    "South Atlantic Ocean": "South Atlantic"
}

# 处理数据
def update_water_body_type(row):
    if pd.notna(row["Water Body Type"]):
        return row["Water Body Type"]

    if row["Water Body Type old"] == "Oceans":
        if pd.notna(row["Water Body Name 经纬度定位生成"]):
            return water_bodies.get(row["Water Body Name 经纬度定位生成"], row["Water Body Name 经纬度定位生成"])
        elif pd.notna(row["Water Body Name"]):
            return water_bodies.get(row["Water Body Name"], "unspecified")
        else:
            return "unspecified"
    return water_bodies.get(row["Water Body Type old"], row["Water Body Type old"])

df["Water Body Type"] = df.apply(update_water_body_type, axis=1)

# 更新 Water Body Name 列
# def update_water_body_name(row):
#     if row["Water Body Type"] == "unspecified":
#         return "unspecified"
#     return row["Water Body Name"]
#
# df["Water Body Name"] = df.apply(update_water_body_name, axis=1)

# 保存回 Excel
output_file = "Updated_MASH_Merge_250324.xlsx"
df.to_excel(output_file, index=False)

print(f"处理完成，已保存为 {output_file}")
