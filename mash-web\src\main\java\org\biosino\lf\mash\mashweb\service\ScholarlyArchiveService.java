package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.ChildScoreMode;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import lombok.RequiredArgsConstructor;
import org.apache.lucene.queryparser.flexible.standard.QueryParserUtil;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.MostRecentItemDTO;
import org.biosino.lf.mash.mashweb.dto.ScholarlyArchiveQueryDTO;
import org.biosino.lf.mash.mashweb.enums.ScArcQueryItemEnum;
import org.biosino.lf.mash.mashweb.es.entity.MashArticleEs;
import org.biosino.lf.mash.mashweb.mapstruct.MashArticleEsStructMapper;
import org.biosino.lf.mash.mashweb.mapstruct.MashArticleMongoStructMapper;
import org.biosino.lf.mash.mashweb.mongo.entity.MashArticleMongo;
import org.biosino.lf.mash.mashweb.mongo.entity.SearchLog;
import org.biosino.lf.mash.mashweb.mongo.repository.MashArticleMongoRepository;
import org.biosino.lf.mash.mashweb.mongo.repository.SearchLogRepository;
import org.biosino.lf.mash.mashweb.vo.HotWordAndMostRecentVO;
import org.biosino.lf.mash.mashweb.vo.ScholarlyArchiveListVO;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * MASH文献检索
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Service
@RequiredArgsConstructor
public class ScholarlyArchiveService {
    private final EmbeddingModel embeddingModel;
    private final ElasticsearchTemplate elasticsearchTemplate;
    private final SearchLogRepository searchLogRepository;
    private final AppConfig appConfig;

    private final MashArticleMongoRepository mashArticleMongoRepository;

    /**
     * 文献列表
     * 在Elasticsearch中，QueryBuilders.knn方法用于执行向量相似度搜索，各参数作用如下：
     * 参数作用
     * k：
     * 表示要返回的最相似文档数量
     * 在ScholarlyArchiveService.java中设置为100，意味着查询会返回最相似的前100个文档
     * 这是KNN算法中的"K"值，代表要检索的最近邻居数量
     * numCandidates：
     * 内部搜索阶段考虑的候选文档数量
     * 应该大于k值（代码中设置为500，是k值的5倍）
     * 增加此值可提高召回率但会降低性能
     * 这是算法的近似参数，用于平衡精确度和性能
     * similarity：
     * 相似度分数阈值（0-1之间）
     * 在代码中设置为0.3f，表示相似度分数必须大于0.3才会被返回
     * 值越小，返回的结果越多；值越大，结果越精确但可能越少
     * <p>
     * 相似度设置建议：
     * 对于Elasticsearch中的知识库相似度检索：
     * 一般推荐相似度值：
     * 文本语义搜索：0.3-0.7之间
     * 您的代码中使用0.3f是比较合理的起点值
     * 最佳实践：
     * 根据业务需求调整similarity值
     * 对高精确度要求：提高到0.5-0.7
     * 对高召回率要求：降低到0.2-0.4
     * 建议通过实验找到最佳平衡点
     * k和numCandidates关系：
     * 通常numCandidates应为k的3-10倍
     * 代码中500:100的比例（5:1）是合理的
     * 您可以通过监控查询返回的结果质量，逐步调整这些参数以优化效果。
     */
    public Page<ScholarlyArchiveListVO> list(final ScholarlyArchiveQueryDTO queryDTO) {

        final Pageable pageable = queryDTO.getPageable();
        final String searchField = queryDTO.getSearchField();
        final String searchKeyword = queryDTO.getSearchKeyword();

        boolean isAiSearch = false;
        Query query = null;
        final Query allQuery = QueryBuilders.matchAll().build()._toQuery();
        if (StrUtil.isNotBlank(searchKeyword)) {
            final ScArcQueryItemEnum queryItemEnum = ScArcQueryItemEnum.findByName(searchField).orElseThrow(() -> new ServiceException("Unknown search term"));
            switch (queryItemEnum) {
                case ai -> {
                    final List<Float> queryVector = EsService.normalizeVectorList(embeddingModel, searchKeyword);

                    // 使用KNN查询替代脚本评分
                    Query knnTitleQuery = QueryBuilders.knn(fn -> fn
                            .field("title_vector")
                            .queryVector(queryVector)
                            .k(100)
                            .numCandidates(500)
                            .similarity(0.3f));

                    // 嵌套字段的处理方式
                    Query nestedAbstractQuery = QueryBuilders.nested(fn -> fn
                            .path("abstract_vectors")
                            .query(QueryBuilders.knn(knn -> knn
                                    .field("abstract_vectors.vector")
                                    .queryVector(queryVector)
                                    .k(100)
                                    .numCandidates(500)
                                    .similarity(0.3f)))
                            .scoreMode(ChildScoreMode.Max));

                    Query nestedContentQuery = QueryBuilders.nested(fn -> fn
                            .path("content_vectors")
                            .query(QueryBuilders.knn(knn -> knn
                                    .field("content_vectors.vector")
                                    .queryVector(queryVector)
                                    .k(100)
                                    .numCandidates(500)
                                    .similarity(0.3f)))
                            .scoreMode(ChildScoreMode.Max));

                    query = QueryBuilders.bool(fn -> fn
                            .should(knnTitleQuery, nestedAbstractQuery, nestedContentQuery));

                    isAiSearch = true;
                }
                case title -> {
//                    final String escape = ReUtil.escape(searchKeyword);
//                    query = QueryBuilders.wildcard(fn -> fn.field("title.keyword").value(StrUtil.format("*{}*", escape)));
//                    QueryBuilders.matchAll(fn->fn.queryName("").boost(2F));
//                    query = QueryBuilders.match(fn -> fn.field("title").analyzer(searchKeyword));
                    query = QueryBuilders.match(fn -> fn.field("title").query(searchKeyword));
                }
                case pmid -> {
                    long pmidVal;
                    try {
                        pmidVal = Long.parseLong(searchKeyword);
                    } catch (Exception e) {
                        throw new ServiceException("PMID must be a number");
                    }
                    query = QueryBuilders.term(fn -> fn.field("pmid").value(pmidVal));
                }
                case doi -> {
                    query = QueryBuilders.matchPhrase(fn -> fn.field("doi").query(searchKeyword));
                }
                case journal -> {
                    query = QueryBuilders.match(fn -> fn.field("journal_title").query(searchKeyword));
                }
                case author -> {
                    query = QueryBuilders.match(fn -> fn.field("author").query(searchKeyword));
                }
                case bioId -> {
//                    final String escape = ReUtil.escape(searchKeyword);
                    final String wildcardCondition = StrUtil.format("*{}*", QueryParserUtil.escape(searchKeyword));
                    query = QueryBuilders.bool(fn1 -> fn1
                            .should(QueryBuilders.wildcard(fn -> fn.field("node_ids.lower").value(wildcardCondition)),
                                    QueryBuilders.wildcard(fn -> fn.field("ncbi_sra_ids.lower").value(wildcardCondition)))
                    );
                }
            }
        }

        if (query == null) {
            query = allQuery;
        }

        final String sortKey = queryDTO.getSortKey();
        Sort sort;
        final Sort.Order scoreOrder = Sort.Order.desc("_score");
        if ("latest".equalsIgnoreCase(sortKey)) {
            sort = Sort.by(Sort.Order.desc("year"), scoreOrder);
        } else if ("data_first".equalsIgnoreCase(sortKey)) {
            sort = Sort.by(Sort.Order.desc("bio_projects_count"), scoreOrder);
        } else {
            // 默认按照相关性排序，Relevance
            sort = Sort.by(scoreOrder);
        }

        final NativeQuery searchQuery = new NativeQueryBuilder()
                .withQuery(query)
                .withPageable(pageable)
                .withSort(sort)
                .build();

//        System.out.println("DSL: " + searchQuery.getQuery());
//        System.out.println("getPageable: " + searchQuery.getPageable());
//        System.out.println("getSort: " + searchQuery.getSort());
        final SearchHits<MashArticleEs> search = elasticsearchTemplate.search(searchQuery, MashArticleEs.class);

        final List<Long> pmids = new ArrayList<>();
        for (SearchHit<MashArticleEs> searchHit : search) {
            pmids.add(searchHit.getContent().getPmid());
        }

        final List<ScholarlyArchiveListVO> list = new ArrayList<>();
        final Map<Long, MashArticleMongo> map = findByPmids(pmids);
        for (SearchHit<MashArticleEs> searchHit : search) {
            final ScholarlyArchiveListVO vo = new ScholarlyArchiveListVO();
            MashArticleEsStructMapper.INSTANCE.copyToVo(searchHit.getContent(), vo);
            vo.setDisabled(vo.getBioProjectsCount() == 0);
            final MashArticleMongo articleMongo = map.get(vo.getPmid());
            if (articleMongo != null) {
                vo.setPage(articleMongo.getPage());
            }
            list.add(vo);
        }

        // if (CollUtil.size(list) > 0 && isAiSearch) {

        /*final String searchBtnFlag = queryDTO.getSearchBtnFlag();
        if ("1".equals(searchBtnFlag) && isAiSearch) {
            saveSearchLog(searchKeyword);
        }*/
        return new PageImpl<>(list, pageable, search.getTotalHits());
    }

    private Map<Long, MashArticleMongo> findByPmids(final List<Long> pmids) {
        if (CollUtil.isEmpty(pmids)) {
            return new HashMap<>();
        }
        List<MashArticleMongo> list = mashArticleMongoRepository.findByPmidIn(pmids);
        Map<Long, MashArticleMongo> map = new HashMap<>();
        for (MashArticleMongo item : list) {
            map.put(item.getPmid(), item);
        }
        return map;
    }

    public void saveSearchLog(String searchKeyword, Integer num) {
        searchKeyword = StrUtil.trimToNull(searchKeyword);
        if (searchKeyword == null) {
            return;
        }

        // 转为小写，小写值唯一，避免重复保存
        final String textLowercase = searchKeyword.toLowerCase();
        SearchLog searchLog = searchLogRepository.findByTextLowercase(textLowercase);
        final Date now = new Date();
        if (searchLog == null) {
            searchLog = new SearchLog();
            searchLog.setCreateDate(now);
            searchLog.setText(searchKeyword);
            searchLog.setNum(num == null ? 1L : num);
        } else {
            searchLog.setNum(num == null ? (searchLog.getNum() + 1L) : num);
        }
        searchLog.setTextLowercase(textLowercase);
        searchLog.setUpdateDate(now);
        searchLogRepository.save(searchLog);
    }

    public HotWordAndMostRecentVO findHotWordAndMostRecent() {
        final List<SearchLog> top5 = searchLogRepository.findTop5();
        final List<ScholarlyArchiveListVO> hotWordList = new ArrayList<>();
        if (CollUtil.isNotEmpty(top5)) {
            for (SearchLog searchLog : top5) {
                final ScholarlyArchiveListVO vo = new ScholarlyArchiveListVO();
                vo.setTitle(searchLog.getText());
                hotWordList.add(vo);
            }
        }

        final List<MostRecentItemDTO> mostRecent = appConfig.getMostRecent();
        final List<ScholarlyArchiveListVO> mostRecentList = new ArrayList<>();
        if (CollUtil.isNotEmpty(mostRecent)) {
            for (MostRecentItemDTO dto : mostRecent) {
                final Long pmid = dto.getPmid();
                final ScholarlyArchiveListVO vo = new ScholarlyArchiveListVO();
                if (pmid != null) {
                    final MashArticleMongo item = mashArticleMongoRepository.findByPmid(pmid);
                    MashArticleMongoStructMapper.INSTANCE.copyToVo(item, vo);
                    /*final Query query = QueryBuilders.term(fn -> fn.field("pmid").value(pmid));
                    final SearchHits<MashArticleEs> searchHits = elasticsearchTemplate.search(new NativeQueryBuilder().withQuery(query)
                            .build(), MashArticleEs.class);
                    for (SearchHit<MashArticleEs> searchHit : searchHits) {
                        MashArticleEsStructMapper.INSTANCE.copyToVo(searchHit.getContent(), vo);
                        break;
                    }*/

                }
                vo.setImg(dto.getImg());
                mostRecentList.add(vo);
            }
        }

        final HotWordAndMostRecentVO hotWordAndMostRecentVO = new HotWordAndMostRecentVO();
        hotWordAndMostRecentVO.setHotWordList(hotWordList);
        hotWordAndMostRecentVO.setMostRecentList(mostRecentList);
        return hotWordAndMostRecentVO;
    }

}
