<template>
  <div class="submit-page">
    <div class="container-fluid">
      <div class="developing">
        <div class="text-center">
          <h1 class="my-5">API</h1>
          <div class="mb-4">
            <span style="margin-right: 6px">Token</span>
            <el-input
              v-model.trim="inputData"
              style="width: 560px"
              clearable
              placeholder="Input token"
            />
            <div style="margin-top: 8px">
              <el-button type="primary" @click="executeApi('MASH')">
                初始化 ES
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue';
  import { trimStr } from '@/utils';
  import { createIndexApi } from '@/api/common_api';

  const inputData = ref('');

  const { proxy } = getCurrentInstance();

  function executeApi(type) {
    let token = trimStr(inputData.value);
    if (!token) {
      proxy.$modal.msgError('请输入token');
      return false;
    }
    proxy.$modal
      .confirm(`确定生成 ${type} ES索引吗？`)
      .then(() => {
        proxy.$modal.loading('Loading');
        createIndexApi(token, type)
          .then(response => {
            proxy.$modal.msgSuccess(`api请求成功： ${response}`);
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {});
  }
</script>

<style lang="scss" scoped>
  .developing {
    border-radius: 0.25rem !important;
    padding: 0.5rem !important;
    border: 1px solid #eff0f2 !important;
    margin-top: 2rem !important;
    margin-bottom: 0.25rem !important;

    .text-center {
      text-align: center;

      .my-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important;
        font-size: 2.34375rem;
        font-family:
          'Myriad Pro Semibold', Helvetica, Arial, '\5FAE\8F6F\96C5\9ED1',
          sans-serif;
        font-weight: 600;
      }

      .mb-4 {
        margin-bottom: 1.5rem !important;

        img {
          max-width: 100%;
          vertical-align: middle;
        }
      }
    }
  }
</style>
