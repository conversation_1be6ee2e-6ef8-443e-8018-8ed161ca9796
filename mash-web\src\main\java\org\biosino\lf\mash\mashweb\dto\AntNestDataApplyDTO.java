package org.biosino.lf.mash.mashweb.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * Ant Nest数据申请的DTO
 */
@Data
public class AntNestDataApplyDTO extends CaptchaDTO {

    /**
     * 研究标题
     */
    @NotBlank(message = "Title cannot be empty")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    private String title;

    /**
     * 申请人姓名
     */
    @NotBlank(message = "Name cannot be empty")
    @Size(max = 100, message = "Name cannot exceed 100 characters")
    private String name;

    /**
     * 申请人单位/机构
     */
    @NotBlank(message = "Institution cannot be empty")
    @Size(max = 300, message = "Institution cannot exceed 300 characters")
    private String institution;

    /**
     * 申请人邮箱
     */
    @NotBlank(message = "Email cannot be empty")
    @Email(message = "Email format incorrect")
    @Size(max = 100, message = "Email cannot exceed 100 characters")
    private String email;

    /**
     * 申请说明/请求文本
     */
    @Size(max = 2500, message = "Request Text cannot exceed 2500 characters")
    private String requestText;

    /**
     * 申请的项目ID列表
     */
    @NotEmpty(message = "Selected Data cannot be empty")
    private List<String> runIds;
}
