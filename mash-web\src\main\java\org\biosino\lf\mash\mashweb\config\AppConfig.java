package org.biosino.lf.mash.mashweb.config;

import lombok.Data;
import org.biosino.lf.mash.mashweb.dto.MostRecentItemDTO;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {
    /**
     * Scholarly Archive页面中显示的最近文章（Most recent）
     */
    private List<MostRecentItemDTO> mostRecent;

    /**
     * 验证码类型
     */
    private String captchaType;

    /**
     * 验证码开关
     */
    private boolean captchaEnabled;

    /**
     * 数据文件目录
     */
    private String dataHome;

    /**
     * 统计页面验证token
     */
    private String statToken;

    /**
     * Dify api请求地址
     */
    private String difyUrl;

    /**
     * Dify api密钥
     */
    private String difyToken;

    /**
     * milvus知识库请求地址
     */
    private String knowledgeUrl;

    /**
     * milvus知识库名称
     */
    private String knowledgeId;

    /**
     * milvus知识库密钥
     */
    private String knowledgeToken;

    /**
     * 专家问答收件人管理员邮箱
     */
    private String adminEmail;

    /**
     * 专家问答邮件抄送管理员邮箱
     */
    private List<String> ccEmails;

}
