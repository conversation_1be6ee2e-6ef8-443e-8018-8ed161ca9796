variables:
  GIT_DEPTH: "3"

stages:
  - build
  - deploy

# 打包前端 APP
nodejs_build_job_app:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/node:20.19-alpine3.21
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - rm -rf /node-cloud/node-ui/html/keep-mash
    - mkdir -p /node-cloud/node-ui/html/keep-mash
    - npm config set registry https://registry.npmmirror.com
    - npm install
    - npm run build:stage
    - cp -r ./keep-mash/* /node-cloud/node-ui/html/keep-mash/

# 部署
deploy_k8s_job:
  image: bitnami/kubectl:1.20.15
  stage: deploy
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    # 重启
    - kubectl -n xuqw rollout restart deployment node-nginx
