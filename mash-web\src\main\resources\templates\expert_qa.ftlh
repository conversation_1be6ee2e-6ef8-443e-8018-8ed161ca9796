<#ftl output_format="HTML">
<div>
    <style>
        .exp-em-content {
            padding: 8px;
        }

        .em-title-p {
            padding: 5px;
            text-indent: 1em;
        }

        .em-main-p {
            padding: 5px;
            text-indent: 2em;
        }

        .qa-em-content {
            border: 1px solid #211f1f;
            border-radius: 8px;
            padding: 8px;
            width: 78%;
            margin: 6px auto;
        }

        .qa-user-title {
            padding: 5px 0;
            text-indent: 1em;
        }

        .qa-question-content {
            padding: 5px 0;
            text-indent: 1em;
        }
    </style>
    <div class="exp-em-content">
        <#--
            此处使用{}占位符
            之后用cn.hutool.core.util.StrUtil.format添加专家名称
            注：发给管理员，不添加专家名
        -->
        <#--<p class="em-title-p">尊敬的 【{}】教授/专家：</p>-->
        <p class="em-title-p">您好！</p>
        <p class="em-main-p">
            感谢参与MASH项目-Expert Q&amp;A专家知识问答模块，正因为有您和其他专家的共同努力，平台得以持续提升用户体验和专业服务质量。
        </p>
        <p class="em-main-p">目前，我们在平台上收到一位用户提出的问题，具体问题及用户信息如下：</p>
        <div class="qa-em-content">
            <p class="qa-user-title"><strong>Email:</strong> ${email!""}</p>
            <p class="qa-user-title"><strong>Name:</strong> ${name!""}</p>
            <p class="qa-user-title"><strong>Title:</strong> ${title!""}</p>
            <p class="qa-user-title"><strong>Organization:</strong> ${organization!""}</p>
            <p class="qa-user-title"><strong>Submission Time:</strong>
                ${(createDate?string("yyyy-MM-dd HH:mm:ss"))!""}
            </p>
            <p class="qa-user-title"><strong>Question Type:</strong> ${selectedTypeText!""}</p>
            <hr>
            <p class="qa-user-title">
                <strong>Question:</strong>
            </p>
            <div>
                <#list questionParagraphs![] as paragraph>
                    <p class="qa-question-content">${paragraph}</p>
                </#list>
            </div>
        </div>
        <p class="em-main-p">
            鉴于您在该方向具有丰富的研究积累和实践经验，恳请您在方便时抽空给予简要解答或指导意见，非常感激！
        </p>
        <br>
        <br>
        <p class="em-title-p">祝好，</p>
        <p class="em-title-p">MASH团队</p>
    </div>
</div>