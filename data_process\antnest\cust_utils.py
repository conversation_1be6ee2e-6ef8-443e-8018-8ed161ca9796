# -*- coding: utf-8 -*-

"""
 自定义工具类
 Author: 尚尉
 Date: 2025-01-06
 Version: 1.0.0

"""

import functools
import os
import time
from datetime import timedelta
from pathlib import Path

import pandas as pd


# 时间统计装饰器生成函数。该函数用于创建一个装饰器，用以统计被装饰函数的执行时间，并通过提供的logger记录执行时间信息。
def time_stat(logger):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()  # 记录开始时间
            result = func(*args, **kwargs)  # 执行被装饰的函数
            end_time = time.time()  # 记录结束时间
            execution_time = format_seconds(end_time - start_time)  # 计算执行时间
            logger.info(f"函数 {func.__name__} 执行时间: {execution_time}")
            return result

        return wrapper

    return decorator


def format_seconds(seconds: float) -> str:
    """
    格式化秒数为人类可读的字符串形式。

    参数:
    seconds (float): 需要格式化的秒数，可以是浮点数。

    返回:
    str: 格式化后的时间字符串，形式为"小时:分钟:秒"，只返回非零时间部分。
    """
    # 检查输入值是否为None或NaN，如果是，则返回空字符串
    if seconds is None or pd.isnull(seconds):
        return ''

    try:
        # 将秒数转换为timedelta对象，用于后续的时间格式化
        # 这里使用round函数处理浮点数精度问题
        td = timedelta(seconds=round(seconds, 6))
    except (ValueError, OverflowError) as e:
        # 捕获异常，如果转换失败，则打印错误信息并返回空字符串
        print(f"无法格式化时间: {seconds}, 错误信息: {e}")
        return ''

    # 将timedelta对象转换为小时、分钟和秒
    hours, remainder = divmod(td.total_seconds(), 3600)
    minutes, seconds = divmod(remainder, 60)

    # 将小时、分钟和秒转换为整数
    hours_int, minutes_int, seconds_int = map(int, [hours, minutes, seconds])

    # 根据小时、分钟和秒的值，返回相应的时间字符串
    if hours_int > 0:
        return f"{hours_int}小时{minutes_int}分钟{seconds_int}秒"
    elif minutes_int > 0:
        return f"{minutes_int}分钟{seconds_int}秒"
    else:
        return f"{seconds_int}秒"


def trim_to_none(val: str):
    """
    去除值的前后空白字符。

    返回值:
    str 或 None。
    """
    if pd.notnull(val) and isinstance(val, str):
        return val.strip()
    return None


def trim_to_empty(val: str):
    """
    去除值的前后空白字符。

    返回值:
    str 或 空字符串。
    """
    if pd.notnull(val) and isinstance(val, str):
        return val.strip()
    return ''


def is_empty(val: str):
    return val is None or val == ''


def load_data(filename: str, plus_name: str = None, data_dir='data') -> pd.DataFrame:
    """
        加载数据文件并返回一个DataFrame对象。

        该函数支持从Excel (.xlsx) 和CSV (.csv) 文件中加载数据。

        参数:
        - filename (str): 文件名，可以是绝对路径或相对于data_dir的路径。
        - data_dir (str): 数据目录的路径，默认为'data'。

        返回:
        - pd.DataFrame: 加载的DataFrame对象。
    """
    if filename is None or filename.strip() == '':
        raise ValueError("文件名不能为空")

    plus_name = trim_to_none(plus_name)
    if not is_empty(plus_name):
        # 获取文件名和扩展名
        input_path = Path(filename)
        name_without_ext = input_path.stem
        ext = trim_to_empty(input_path.suffix).lower()
        filename = f'{name_without_ext}_{plus_name}{ext}'

    # 规范化路径并防止路径注入
    base_path = Path(data_dir).resolve()
    if not base_path.is_dir():
        raise NotADirectoryError(f"数据目录不存在或不是一个有效的目录: {data_dir}")

    file_path = Path(filename)
    if (not file_path.is_absolute()) and ('/' not in filename) and ('\\' not in filename):
        file_path = base_path / file_path

    normalized_path = file_path.resolve()

    # 检查路径是否在预期的目录范围内
    try:
        normalized_path.relative_to(base_path)
    except ValueError:
        raise ValueError("文件路径超出允许范围")

    # 检查文件是否存在且可读
    if not normalized_path.exists():
        raise FileNotFoundError(f"文件不存在: {normalized_path}")
    if not os.access(normalized_path, os.R_OK):
        raise PermissionError(f"文件不可读: {normalized_path}")

    # 严格检查文件扩展名
    ext = file_path.suffix.lower()
    if ext == '.xlsx':
        return pd.read_excel(normalized_path)
    elif ext == '.csv':
        return pd.read_csv(normalized_path)
    else:
        raise ValueError(f"不支持的文件格式: {ext}")


def write_result_data(df: pd.DataFrame, filename: str, plus_name: str = None, columns=None) -> str:
    """
    将数据框写入指定文件。

    参数:
    df: pd.DataFrame - 需要写入的数据框。
    filename: str - 文件路径和名称。
    plus_name: str - 添加到文件名的字符串，通常用于标识文件内容的差异。
    columns: 可选[list] - 需要写入的列名列表，如果未提供，则写入所有列。

    返回:
    str - 输出文件的路径。
    """
    # 验证文件路径和可读性
    input_path = Path(filename)
    if input_path.exists() and (not input_path.is_file() or not os.access(input_path, os.R_OK)):
        raise ValueError(f"提供的文件路径无效或文件不可读: {filename}")

    # 获取文件名和扩展名
    name_without_ext = input_path.stem
    ext = trim_to_empty(input_path.suffix).lower()

    # 构建输出文件路径
    output_dir = input_path.parent

    plus_name = trim_to_none(plus_name)
    if is_empty(plus_name):
        output_file = output_dir / f'{name_without_ext}{ext}'
    else:
        output_file = output_dir / f'{name_without_ext}_{plus_name}{ext}'

    # 处理列选择
    if columns:
        if not columns or not all(col in df.columns for col in columns):
            raise ValueError("提供的列名无效或为空")
        df = df[columns]

    try:
        # 写入文件
        if ext == '.xlsx':
            df.to_excel(output_file, index=False)
        elif ext in ['.csv', '.txt']:
            # 某些应用程序（如 Microsoft Excel）在读取没有 BOM 的 UTF-8 编码文件时，可能会错误地将文件识别为 ANSI 编码，导致乱码问题。使用 utf-8-sig 可以确保这些应用程序正确识别文件的编码格式，避免乱码。
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
        else:
            raise ValueError(f"不支持的文件扩展名: {ext}")
    except IOError as e:
        raise IOError(f"写入文件时发生IO错误: {e}")

    return str(output_file)


def str_val(row, field: str = None, lowercase=False):
    """
    从对象中取出指定字段值，为空返回None
    """
    if (row is None) or (field is None):
        return None

    field = str(field).strip()
    val = row.get(field, None)
    if pd.isnull(val):
        return None

    val_strip = str(val).strip()
    if val_strip == "":
        return None
    else:
        return val_strip.lower() if lowercase else val_strip


def trim_val(row, field):
    """
    从对象中取出指定字段值，为空返回空字符串
    """
    if (row is None) or (field is None):
        return ''

    val = row.get(field, None)
    if pd.isnull(val):
        return ''

    val_strip = str(val).strip()
    return val_strip if (val_strip != "" and val_strip != "无") else ''


def mk_dir(path: str):
    """
    创建目录并返回其绝对路径。

    参数:
        path (str): 目录路径

    返回:
        Path: 创建的目录的绝对路径，如果输入路径无效则返回 None
    """
    try:
        path = trim_to_none(path)
        if path is None:
            return None

        os.makedirs(path, exist_ok=True)

        return os.path.abspath(path)
    except Exception as e:
        print(f"创建目录时发生错误: {e}")
        raise e
