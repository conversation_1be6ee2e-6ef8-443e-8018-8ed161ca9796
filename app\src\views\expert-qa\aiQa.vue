<template>
  <div class="ai-mode" :class="{ 'has-conversation': chatHistory.length > 0 }">
    <!-- 无对话时中央显示的输入框区域 -->
    <div v-if="chatHistory.length === 0" class="center-input-container">
      <!--<div class="welcome-message">
        <h2>Aquatic Microbiome Atlas 智能助手</h2>
        <p>您可以询问关于水生微生物组的任何问题</p>
      </div>-->
      <div class="input-area">
        <el-input
          ref="textareaRef"
          v-model="question"
          type="textarea"
          :show-word-limit="true"
          :maxlength="1500"
          :rows="24"
          placeholder="Please enter your question (press Shift + Enter to change lines)"
          @keyup.enter="handleKeydown"
        />
        <div class="button-container">
          <el-button
            type="primary"
            class="submit-btn"
            :disabled="isLoading || !!currentController || !trimStr(question)"
            @click="handleAiSubmit"
          >
            <el-icon v-if="isLoading || !!currentController" size="18">
              <ElIconLoading />
            </el-icon>
            <span class="font-16">Submit</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 有对话时的聊天界面 -->
    <div v-else class="chat-container">
      <!-- 对话控制栏 -->
      <div class="conversation-header">
        <div class="conversation-info">
          <!--<el-tag size="small" effect="plain" type="info">
            <el-icon style="cursor: default"><ChatLineRound /></el-icon>
            <span style="margin-left: 5px">Current Conversations</span>
          </el-tag>-->
          <div>&nbsp;</div>
          <el-button
            type="primary"
            size="small"
            text
            @click="startNewConversation"
          >
            <el-icon>
              <Plus />
            </el-icon>
            <span style="font-size: 15px">Start a new conversation</span>
          </el-button>
        </div>
      </div>

      <!-- 对话历史区域 -->
      <div ref="messagesContainer" class="messages-container">
        <div
          v-for="(msg, index) in chatHistory"
          :key="index"
          class="message-wrapper"
        >
          <div
            :class="[
              'chat-message',
              msg.role === 'user' ? 'user-message' : 'ai-message',
            ]"
          >
            <div v-if="msg.role !== 'user'" class="message-header">
              <strong>AI</strong>
            </div>
            <div
              class="message-content"
              v-html="
                msg.role === 'assistant'
                  ? processThinkingContent(markdownToHtml(msg.content))
                  : msg.content
              "
            ></div>
            <div class="message-footer">
              <button
                class="copy-text-btn"
                @click="copyTextFromMsg(msg.content)"
              >
                <el-icon>
                  <DocumentCopy />
                </el-icon>
                <span>Copy</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 流式回答正在生成中 - 使用v-show和计算属性避免闪烁 -->
        <div v-show="isLoading && streamingAnswer" class="message-wrapper">
          <div class="chat-message ai-message streaming-message">
            <div class="message-header">
              <strong>AI</strong>
            </div>
            <div class="message-content" v-html="currentAiResponse"></div>
            <div class="message-footer">
              <!-- 流式回答时不显示复制按钮 -->
            </div>
            <div class="loading-indicator">
              <div class="loading-dot"></div>
              <div class="loading-dot"></div>
              <div class="loading-dot"></div>
            </div>
          </div>
        </div>

        <!-- 加载中状态 -->
        <div v-show="isLoading && !streamingAnswer" class="message-wrapper">
          <div class="loading-message">
            <div class="message-header">
              <strong>AI</strong>
            </div>
            <div class="message-footer">
              <!-- 加载中状态不显示复制按钮 -->
            </div>
            <div class="loading-indicator">
              <div class="loading-dot"></div>
              <div class="loading-dot"></div>
              <div class="loading-dot"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部输入区域 -->
      <div class="bottom-input-area">
        <div class="input-container">
          <el-input
            ref="textareaRef"
            v-model="question"
            type="textarea"
            :show-word-limit="true"
            :maxlength="1500"
            :rows="2"
            placeholder="Please enter your question (press Shift + Enter to change lines)"
            @keyup.enter="handleKeydown"
          />
          <!-- 合并发送和停止按钮 -->
          <el-button
            v-if="!isWorkflowStarted"
            type="primary"
            class="bottom-submit-btn"
            :disabled="isLoading || !!currentController || !trimStr(question)"
            @click="handleAiSubmit"
          >
            <el-icon v-if="isLoading" size="18">
              <ElIconLoading />
            </el-icon>
            <span v-else>Submit</span>
          </el-button>
          <el-button
            v-else
            type="danger"
            class="bottom-submit-btn"
            @click="stopResponse"
          >
            <span>Stop</span>
          </el-button>
        </div>
      </div>
    </div>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="Literature Information"
    width="800px"
  >
    <div v-if="documentData">
      <p class="dialog-title">
        <strong>{{ documentData.title }}</strong>
      </p>

      <p v-if="documentData.journal" class="dialog-subtitle">
        Journal：{{ documentData.journal }}
      </p>

      <p v-if="documentData.impact_factor" class="dialog-subtitle">
        Impact Factor：{{ documentData.impact_factor.toFixed(2) }}
      </p>

      <p v-if="documentData.pmid" class="dialog-subtitle">
        PMID：<a
          class="pmid-link"
          :href="initPubmedUrl(documentData.pmid)"
          target="_blank"
          >{{ documentData.pmid }}</a
        >
      </p>

      <p v-if="documentData.doi" class="dialog-subtitle">
        DOI：<a
          class="pmid-link"
          :href="initDoiUrl(documentData.doi)"
          target="_blank"
          >{{ documentData.doi }}</a
        >
      </p>

      <p
        v-if="documentData.highlightText"
        v-html="documentData.highlightText"
      ></p>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">Close</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onBeforeUnmount,
    onMounted,
    ref,
    watch,
  } from 'vue';
  import {
    DocumentCopy,
    Loading as ElIconLoading,
    Plus,
  } from '@element-plus/icons-vue';
  import {
    ensureEndsWithPeriod,
    initDoiUrl,
    initPubmedUrl,
    trimStr,
  } from '@/utils';
  import qa_api from '@/api/qa_api';
  import { gsap } from 'gsap';
  import DOMPurify from 'dompurify';
  import { marked } from 'marked';
  import { htmlToText } from 'html-to-text';
  import { doAddVisitsLog } from '@/api/visits_log';
  import { VISITS_LOG_FUNC } from '@/constants';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const { proxy } = getCurrentInstance();

  // 定义响应式变量
  const question = ref('');
  const isLoading = ref(false);
  const answer = ref('');
  const displayedAnswer = ref('');
  const answerText = ref(null);
  const messagesContainer = ref(null); // 添加消息容器的引用
  const textareaRef = ref(null);
  const currentController = ref(null);
  var currentTaskId = null;

  // 添加工作流状态变量
  const isWorkflowStarted = ref(false);

  // 多轮对话相关变量
  const conversationId = ref(null);
  const chatHistory = ref([]);

  const pmidMap = ref(null);
  const childTextList = ref([]);
  const literatureList = ref(null);

  // 弹窗相关响应式数据
  const dialogVisible = ref(false);
  const documentData = ref(null);

  // 添加一个变量记录当前的流式回答
  const streamingAnswer = ref('');

  // 添加用户滚动状态追踪变量
  const userHasScrolled = ref(false);
  const scrollThreshold = 10; // 滚动阈值（像素）

  // 暴露函数到全局window对象
  window.handlePmidClick = function (element) {
    const pmid = element.dataset.pmid;
    const valueElement = decodeURIComponent(element.dataset.childText);
    window.vueApp?.showDocument(pmid, valueElement);
  };

  // 根据捕获的数字查找 pmidMap 中对应的 PMID 及其出现位置（1为第1个位置）
  function findPMIDAndOccurrence(num) {
    if (!pmidMap.value || Object.keys(pmidMap.value).length === 0) {
      return null;
    }
    const target = Number(num);
    let i = 0;
    for (const key in pmidMap.value) {
      i += 1;
      const positions = pmidMap.value[key];
      const index = positions.indexOf(target);
      if (index !== -1) {
        // 返回 PMID 和该数字在数组中的序号（从1开始）
        return { pmid: key, occurrence: i };
      }
    }
    return null;
  }

  // 处理输入文本，将 [#数字] 替换为蓝色数字图标，并绑定点击事件
  function processText(text) {
    return text.replace(/\[#(\d+)\]/g, (match, p1) => {
      const result = findPMIDAndOccurrence(p1);
      if (result) {
        const target = Number(p1);
        const valueOriginal = childTextList.value[target - 1];
        // 构建替换后的 HTML，使用 span 元素，内联样式设置为蓝色，并绑定 onclick 事件
        return `<span class="pmid-icon" data-pmid="${result.pmid}"
        data-child-text="${encodeURIComponent(valueOriginal)}"
        onclick="window.handlePmidClick(this)"
        title="${valueOriginal}"
        > ${result.occurrence} </span>`;
      } else {
        // 如果没有找到对应的 PMID，则删除序号（一般是超过10的序号，例如[#10]）
        return '';
      }
    });
  }

  // 处理思考内容，替换为加载动画
  const processThinkingContent = content => {
    if (!content) return '';

    // 匹配思考内容区块
    const thinkingPattern =
      /<details[\s\S]*?<summary>\s*Thinking\.\.\.[\s\S]*?<\/details>/gi;

    // 替换为加载动画
    return content.replace(thinkingPattern, initThinkingDiv(true));
  };

  function initThinkingDiv(loadingFlag = false) {
    return loadingFlag
      ? `<div class="ai-thinking">
         <span>Thinking</span>
         <div class="thinking-animation">
           <div></div>
           <div></div>
           <div></div>
         </div>
        </div>`
      : ``;
  }

  // 将markdown内容转换为HTML，并处理引用
  const markdownToHtml = content => {
    if (!content) return '';

    // 先将内容转换为HTML
    const htmlContent = DOMPurify.sanitize(marked.parse(content));

    // 然后处理链接，添加target="_blank"
    const processedHtml = htmlContent.replace(
      /<a\s+href="([^"]+)"([^>]*)>/g,
      (match, href, rest) => `<a href="${href}" target="_blank"${rest}>`,
    );

    return processText(processedHtml);
  };

  // 将 showDocument 方法挂载到全局对象，以便内联 onclick 调用
  window.vueApp = {
    dialogVisible,
    documentData,
    async showDocument(pmid, childText) {
      try {
        documentData.value = await qa_api.queryMetadata(pmid, childText);

        const parentText = documentData.value.parent_text;
        documentData.value['highlightText'] = parentText.replace(
          childText,
          '<span class="highlight">' + childText + '</span>',
        );

        dialogVisible.value = true;
      } catch (error) {
        console.error('查询文献信息失败', error);
      }
    },
  };

  const handleKeydown = e => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // 阻止默认换行行为
      handleAiSubmit(); // 调用原提交方法
    }
    // Shift+Enter会保持默认换行行为
  };

  // 添加一条对话记录到历史
  const addChatRecord = (role, content) => {
    chatHistory.value.push({
      role,
      content,
      timestamp: new Date().getTime(),
    });

    // 添加记录后滚动到底部
    nextTick(() => {
      // 当添加新消息时重置滚动状态，确保新消息出现时会滚动到底部
      resetScrollState();
      scrollToBottom();

      // 如果有对话历史，也滚动页面到底部
      if (chatHistory.value.length > 1) {
        scrollPageToBottom();
      }
    });
  };

  // 更新流式回答并实时显示
  const updateStreamingAnswer = content => {
    streamingAnswer.value = content;

    // 更新后滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  };

  // 清除对话历史并开始新对话
  const startNewConversation = () => {
    chatHistory.value = [];
    conversationId.value = null;
    question.value = '';
    answer.value = '';
    displayedAnswer.value = '';

    // 重置关联数据
    literatureList.value = null;
    documentData.value = null;
    pmidMap.value = null;
    childTextList.value = [];

    // 重置滚动状态
    resetScrollState();
  };

  // 定义滚动到底部函数
  const scrollToBottom = () => {
    nextTick(() => {
      if (messagesContainer.value && !userHasScrolled.value) {
        messagesContainer.value.scrollTop =
          messagesContainer.value.scrollHeight;
      }
    });
  };

  // 监听用户滚动
  const handleScroll = () => {
    if (messagesContainer.value) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainer.value;
      // 如果距离底部超过阈值，标记为用户已手动滚动
      userHasScrolled.value =
        scrollHeight - scrollTop - clientHeight > scrollThreshold;
    }
  };

  // 添加滚动事件监听
  const setupScrollListener = () => {
    if (messagesContainer.value) {
      messagesContainer.value.addEventListener('scroll', handleScroll);
    }
  };

  // 移除滚动事件监听
  const removeScrollListener = () => {
    if (messagesContainer.value) {
      messagesContainer.value.removeEventListener('scroll', handleScroll);
    }
  };

  // 监听消息容器引用变化，确保在DOM元素可用时添加监听器
  watch(messagesContainer, newVal => {
    if (newVal) {
      removeScrollListener(); // 先移除可能存在的旧监听器
      setupScrollListener(); // 添加新的监听器
    }
  });

  // 重置滚动状态
  const resetScrollState = () => {
    userHasScrolled.value = false;
  };

  // 滚动浏览器视图到底部
  const scrollPageToBottom = () => {
    nextTick(() => {
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth',
      });
    });
  };

  // AI提交
  const handleAiSubmit = async () => {
    if (isLoading.value || currentController.value) {
      return;
    }
    const val = trimStr(question.value);
    if (!val) {
      proxy.$modal.msgWarning('Please enter your question');
      return;
    }
    // 添加日志
    doAddVisitsLog(route, VISITS_LOG_FUNC.askAi, { question: val });

    try {
      // 保存当前问题到历史记录
      addChatRecord('user', val);

      // 立即清空问题输入框
      const userQuestion = val;
      question.value = '';

      // 重置状态 - 仅清除当前回答相关状态，不清除对话历史
      isLoading.value = true;
      answer.value = '';
      displayedAnswer.value = '';
      streamingAnswer.value = ''; // 重置流式回答
      literatureList.value = null;
      documentData.value = null;
      pmidMap.value = null;
      childTextList.value = [];

      // 滚动页面到底部
      scrollPageToBottom();

      // 调用API - 传入conversationId进行多轮对话
      const { response, controller } = await qa_api.askQuestion(
        userQuestion,
        conversationId.value,
      );
      currentController.value = controller;
      // 检查响应状态
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '请求失败');
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      let accumulatedAnswer = '';
      let buffer = ''; // 用于存储不完整的JSON字符串

      // 读取流式响应
      // eslint-disable-next-line no-constant-condition
      const dataFlag = 'data:';
      const dataFlagLength = dataFlag.length;

      let dataIndex = 0;
      const allEvents = new Map();

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        // 解码二进制数据
        const chunk = decoder.decode(value, { stream: true });
        // '{"msg":"Question cannot exceed 200 characters","code":500}'
        if (chunk.startsWith('{"msg":')) {
          const errMsg = JSON.parse(chunk);
          proxy.$modal.alertError(errMsg.msg);
          isWorkflowStarted.value = false;
          break;
        }
        buffer += chunk;

        // 处理缓冲区中的完整事件
        const events = buffer.split('\n\n');
        buffer = events.pop() || ''; // 保留最后一个可能不完整的事件

        for (const event of events) {
          if (event.startsWith(dataFlag)) {
            try {
              const jsonStr = trimStr(event.substring(dataFlagLength));
              const data = JSON.parse(jsonStr);

              const currEvent = data.event;
              // 记录所有事件出现次数，调试用
              /*dataIndex++;
        if (currEvent) {
          if (allEvents.has(currEvent)) {
            allEvents.set(currEvent, allEvents.get(currEvent) + 1);
          } else {
            allEvents.set(currEvent, 1);
          }
        }
        if (
          dataIndex % 30 === 0 ||
          currEvent === 'error' ||
          currEvent === 'message_end'
        ) {
          console.log(
            '已处理事件:',
            JSON.stringify(Array.from(allEvents)),
          );
        }*/

              if (currEvent !== 'error') {
                currentTaskId = data.task_id;
              }

              // 处理不同类型的事件
              if (currEvent === 'workflow_started') {
                // 工作流开始，切换按钮状态
                isWorkflowStarted.value = true;
              } else if (currEvent === 'message') {
                const newContent = data.answer || '';
                accumulatedAnswer += newContent;

                accumulatedAnswer = accumulatedAnswer
                  .replace(
                    /<think>/g,
                    '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open> <summary> Thinking... </summary>',
                  )
                  .replace(/<\/think>/g, '</details>');

                displayedAnswer.value = accumulatedAnswer;

                // 立即更新流式回答显示
                updateStreamingAnswer(accumulatedAnswer);
              } else if (currEvent === 'message_end') {
                // 流式回答结束
                isWorkflowStarted.value = false;

                // 完成加载状态并提取会话ID
                const refData = data?.metadata?.retriever_resources;

                // 提取会话ID
                if (data.conversation_id && !conversationId.value) {
                  conversationId.value = data.conversation_id;
                  // console.log('多轮对话已开始，会话ID:', conversationId.value);
                }

                // 构建 PMID 和句子位置的映射
                if (refData) {
                  pmidMap.value = refData.reduce((map, item, index) => {
                    childTextList.value.push(item.content);
                    const pmid = item.document_name;
                    if (!map[pmid]) {
                      map[pmid] = [];
                    }
                    map[pmid].push(index + 1);
                    return map;
                  }, {});
                }

                // 构建包含参考文献的完整回答
                let finalAnswer = accumulatedAnswer;
                if (
                  !accumulatedAnswer.includes('您可能还关注：') &&
                  !accumulatedAnswer.includes('您可能还关注:')
                ) {
                  // 创建一个数组来存储元数据结果
                  let metadataArray = null;

                  // 遍历 pmidMap 的每个键值对
                  if (pmidMap.value) {
                    metadataArray = [];
                    for (const [pmid] of Object.entries(pmidMap.value)) {
                      // 调用 queryMetadata 获取元数据，并将结果存储到数组中
                      const metadata = await qa_api.queryMetadata(pmid);
                      metadataArray.push(metadata);
                    }
                  }

                  literatureList.value = metadataArray;

                  if (literatureList.value && literatureList.value.length > 0) {
                    finalAnswer += '\n\n### Reference\n';
                    literatureList.value.forEach((ref, index) => {
                      finalAnswer += `${index + 1}. [${ensureEndsWithPeriod(ref.title)}](${initPubmedUrl(ref.pmid)})\n`;
                    });
                  }
                }

                // 首先保存完整回答到历史记录，再清除状态
                addChatRecord('assistant', finalAnswer);

                // 等待历史记录更新后再清除流式状态
                await nextTick(() => {
                  streamingAnswer.value = '';
                  isLoading.value = false;
                });

                // 消息结束事件
                currentTaskId = null;
              } else if (currEvent === 'error' || !currEvent) {
                currentTaskId = null;
                isWorkflowStarted.value = false;
                throw new Error(data.message || 'Error generating answer');
              }
            } catch (err) {
              console.error('解析事件数据失败:', err);
              isWorkflowStarted.value = false;
            }
          }
        }
      }

      // 最终更新答案
      answer.value = accumulatedAnswer;
    } catch (error) {
      console.error('提问失败:', error);
      if (error.message === 'BodyStreamBuffer was aborted') {
        answer.value = 'Answer has been terminated.';
      } else {
        proxy.$modal.msgError(
          error.message || 'Question failed. Please try again later.',
        );
        answer.value =
          'Sorry, there was an error generating your answer, please try again later!';
      }
      displayedAnswer.value = answer.value;
      streamingAnswer.value = ''; // 清空流式回答
      isLoading.value = false;
      isWorkflowStarted.value = false;

      // 错误也添加到对话历史
      addChatRecord('assistant', answer.value);
    } finally {
      currentController.value = null;
      await nextTick(() => {
        // 停止思考动画
        const divList = document.querySelectorAll('div.ai-thinking');
        divList.forEach(div => {
          div.innerHTML = initThinkingDiv(false);
        });
      });
    }
  };

  // 停止响应
  const stopResponse = async () => {
    if (currentController.value) {
      currentController.value.abort();
      currentController.value = null;
    }

    if (currentTaskId) {
      try {
        await qa_api.stopResponse(currentTaskId);
      } catch (error) {
        console.error('停止响应失败:', error);
      }
      currentTaskId = null;
    }

    isLoading.value = false;
    isWorkflowStarted.value = false;

    // 如果有流式回答内容，保存到对话历史
    if (streamingAnswer.value) {
      addChatRecord(
        'assistant',
        streamingAnswer.value + '\n\n_(Answer has been terminated)_',
      );
      streamingAnswer.value = '';
    }
  };

  // 添加复制特定消息内容的方法
  const copyTextFromMsg = async content => {
    try {
      const options = {
        // 保留换行和段落结构
        wordwrap: false,
        // 通过 selectors 过滤掉不需要的标签
        selectors: [
          { selector: 'details', format: 'skip' },
          { selector: '.pmid-icon', format: 'skip' },
        ],
      };

      const htmlContent = markdownToHtml(content);
      const plainText = htmlToText(htmlContent, options);
      await navigator.clipboard.writeText(plainText);
      proxy.$modal.msgSuccess('Copy successfully');
    } catch (err) {
      console.error('Copy failed:', err);
    }
  };

  // 添加一个计算属性计算当前显示的AI回答内容
  const currentAiResponse = computed(() => {
    // 优先显示流式回答
    if (streamingAnswer.value) {
      return processThinkingContent(markdownToHtml(streamingAnswer.value));
    }
    return '';
  });

  // 监听答案变化，应用动画效果
  watch(answer, newValue => {
    if (newValue && answerText.value) {
      gsap.fromTo(
        answerText.value,
        { opacity: 0.8 },
        { opacity: 1, duration: 0.3 },
      );
    }
  });

  // 组件挂载时添加事件监听
  onMounted(() => {
    // 初始化动画效果
    if (answerText.value) {
      gsap.fromTo(
        '.question-answer-container',
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' },
      );
    }

    // 添加滚动事件监听
    nextTick(() => {
      setupScrollListener();
    });
  });

  // 组件销毁前移除事件监听器
  onBeforeUnmount(() => {
    stopResponse();

    // 移除滚动事件监听
    removeScrollListener();
  });

  const homeAiSearch = queryKeyword => {
    question.value = trimStr(queryKeyword);
    handleAiSubmit();
  };

  // 对外暴露方法
  defineExpose({
    homeAiSearch,
  });
</script>

<style lang="scss" scoped>
  .ai-mode {
    min-height: 70vh;
    display: flex;
    flex-direction: column;

    &.has-conversation {
      height: 100%;
    }
  }

  /* 中央输入框样式 */
  .center-input-container {
    width: 100%;
    margin: 5px auto;
    padding: 5px;

    .welcome-message {
      text-align: center;
      margin-bottom: 30px;

      h2 {
        font-size: 26px;
        margin-bottom: 10px;
        color: #333;
      }

      p {
        color: #666;
        font-size: 16px;
      }
    }

    .input-area {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      padding: 20px;

      .animated-textarea {
        margin-bottom: 15px;

        :deep(.el-textarea__inner) {
          border-radius: 8px;
          padding: 12px;
          font-size: 16px;
          resize: none;
        }
      }

      .button-container {
        margin-top: 5px;
        display: flex;
        justify-content: flex-end;

        .submit-btn {
          min-width: 100px;
          height: 40px;
          border-radius: 6px;
        }
      }
    }
  }

  /* 聊天界面样式 */
  .chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin: 0 auto;
    width: 98%;

    .conversation-header {
      padding: 8px 8px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fff;

      .conversation-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .messages-container {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      background-color: #f9f9f9;
      min-height: 62vh;
      max-height: 65vh;

      .message-wrapper {
        display: flex;
        width: 100%;
        margin-bottom: 20px;
      }

      .chat-message {
        padding: 15px;
        border-radius: 8px;
        word-break: break-word;
        max-width: 90%;

        .message-header {
          padding-bottom: 5px;
          margin-bottom: 8px;
          font-size: 14px;
          opacity: 0.8;
          border-bottom: 1px solid #afa2a2c2;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .message-content {
          font-size: 15px;
          line-height: 1.5;

          :deep(a) {
            color: #409eff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          :deep(code) {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 4px;
          }

          :deep(pre) {
            background-color: #282c34;
            color: #abb2bf;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;

            code {
              background-color: transparent;
              padding: 0;
            }
          }
        }

        .message-footer {
          margin-top: 5px;
          display: flex;
          justify-content: flex-end;

          .copy-text-btn {
            opacity: 0.8;
            background: none;
            border: none;
            padding: 4px 8px;
            color: #909399;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;
            border-radius: 4px;
            transition: all 0.2s;

            &:hover {
              opacity: 1;
              color: #409eff;
              background-color: rgba(64, 158, 255, 0.08);
            }
          }
        }
      }

      .user-message {
        background-color: #ecf5ff;
        border-right: 3px solid #409eff;
        margin-left: auto;
        width: auto;
        display: inline-block;
        text-align: left;
      }

      .ai-message {
        background-color: #fff;
        border-left: 3px solid #67c23a;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-right: auto;
        margin-left: 0;
      }

      .loading-message {
        background-color: #fff;
        border-left: 3px solid #67c23a;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        padding: 15px;
        border-radius: 8px;
        max-width: 90%;
        margin-right: auto;

        .message-header {
          padding-bottom: 5px;
          margin-bottom: 8px;
          font-size: 14px;
          opacity: 0.8;
          border-bottom: 1px solid #afa2a2c2;
        }
      }
    }

    .bottom-input-area {
      padding: 15px;
      background-color: #fff;
      border-top: 1px solid #ebeef5;

      .input-container {
        display: flex;
        align-items: flex-start;
        gap: 10px;

        .animated-textarea {
          flex: 1;

          :deep(.el-textarea__inner) {
            border-radius: 8px;
            padding: 10px;
            resize: none;
            font-size: 14px;
          }
        }

        .bottom-submit-btn {
          min-width: 80px;
          height: 40px;
          border-radius: 6px;
          margin-top: 5px;
        }
      }
    }
  }

  /* 加载动画 */
  .loading-indicator {
    display: flex;
    justify-content: flex-start;
    margin: 15px 0;
  }

  .loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #409eff;
    margin: 0 4px;
    animation: loading-bounce 1.4s infinite ease-in-out both;
  }

  .loading-dot:nth-child(1) {
    animation-delay: -0.32s;
  }

  .loading-dot:nth-child(2) {
    animation-delay: -0.16s;
  }

  @keyframes loading-bounce {
    0%,
    80%,
    100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }

  /* 辅助元素样式 */
  .a-text-overflow {
    font-size: 15px;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
      color: #409eff;
    }
  }
</style>

<style>
  /* 思考内容样式 */
  .ai-thinking {
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    color: #666;
  }

  .thinking-animation {
    display: flex;
    margin-right: 10px;
  }

  .thinking-animation > div {
    width: 6px;
    height: 6px;
    margin: 0 2px;
    border-radius: 50%;
    background-color: #666;
    animation: thinking-bounce 1.4s infinite ease-in-out both;
  }

  .thinking-animation > div:nth-child(1) {
    animation-delay: -0.32s;
  }

  .thinking-animation > div:nth-child(2) {
    animation-delay: -0.16s;
  }

  @keyframes thinking-bounce {
    0%,
    80%,
    100% {
      transform: scale(0.6);
    }
    40% {
      transform: scale(1);
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition:
      opacity 0.3s ease,
      transform 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: translateY(20px);
  }

  .highlight {
    color: #f56c6c;
  }

  .pmid-icon {
    display: inline-block;
    vertical-align: middle;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;
    background-color: #48aaff;
    color: #fff;
    font-size: 12px;
    margin: 0 4px;
    cursor: pointer;
  }

  .pmid-link {
    color: #409eff !important;
    cursor: pointer;
  }

  .pmid-link:hover {
    color: rgb(51, 117, 185);
  }

  .ai-answer-div {
    overflow-x: auto;
  }

  .ai-answer-div table {
    width: max-content;
    max-width: max-content;
    border-collapse: collapse;
  }

  .ai-answer-div td {
    border-color: #0c0c0c;
    min-width: 100px;
    max-width: max(30vw, 320px);
  }

  .ai-answer-div th {
    color: #0c0c0c;
    padding: calc(1 * 6px) calc(1 * 12px);
    border-bottom: 1px solid #0c0c0c;
    border-top: 1px solid #0c0c0c;
    font-weight: 600;
  }

  .ai-answer-div th,
  .ai-answer-div td {
    padding: 7px;
    font-size: 15px;
    line-height: 1.72;
  }

  .ai-answer-div td {
    padding: calc(1 * 6px) calc(1 * 12px);
    border-bottom: 1px solid #0c0c0c;
  }

  .ai-mode ul {
    list-style: disc outside none !important;
    padding-left: 1em !important;
    margin-top: 1em !important;
    margin-bottom: 1em !important;
  }

  .ai-mode ol {
    list-style: decimal outside none !important;
    padding-left: 1em !important;
    margin-top: 1em !important;
    margin-bottom: 1em !important;
  }

  .ai-mode ol > li {
    list-style: decimal outside none !important;
  }

  .ai-mode li {
    display: list-item !important;
    margin: 0.5em 0 !important;
  }

  .ai-mode > div > ul > li {
    list-style: disc outside none !important;
  }

  .ai-mode > div > ol > li {
    list-style: decimal outside none !important;
  }

  .ai-mode ul ul,
  .ai-mode ol ul {
    list-style: circle outside none !important;
    padding-left: 1em !important;
  }

  .ai-mode ul ul > li,
  .ai-mode ol ul > li {
    list-style: circle outside none !important;
  }

  .ai-mode ol ol {
    list-style: lower-alpha outside none !important;
    padding-left: 1em !important;
  }

  .ai-mode ol ol > li {
    list-style: lower-alpha outside none !important;
  }

  .ai-mode ul ul ul,
  .ai-mode ol ul ul,
  .ai-mode ul ol ul,
  .ai-mode ol ol ul {
    list-style: square outside none !important;
    padding-left: 1em !important;
  }

  .ai-mode ul ul ul > li,
  .ai-mode ol ul ul > li,
  .ai-mode ul ol ul > li,
  .ai-mode ol ol ul > li {
    list-style: square outside none !important;
  }

  .ai-mode ol ol ol {
    list-style: lower-roman outside none !important;
    padding-left: 1em !important;
  }

  .ai-mode ol ol ol > li {
    list-style: lower-roman outside none !important;
  }
</style>
