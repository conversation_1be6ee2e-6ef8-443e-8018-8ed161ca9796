package org.biosino.lf.mash.mashweb.config;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RestController;

import java.beans.PropertyEditorSupport;

@ControllerAdvice(annotations = {Controller.class, RestController.class})
//@Order(10000)
public class AppControllerAdvice {

    @InitBinder
    public void initBinder(ServletRequestDataBinder binder) {

        final String[] abd = new String[]{"class.*", "Class.*", "*.class.*", "*.Class.*"};
        binder.setDisallowedFields(abd);
        binder.setAutoGrowCollectionLimit(40000);

        binder.registerCustomEditor(String.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                setValue(StrUtil.trimToNull(text));
            }
        });

        // Date 类型转换
        /*binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });*/

        // Timestamp 类型转换
        /*binder.registerCustomEditor(Timestamp.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                Date date = DateUtils.parseDate(text);
                setValue(date == null ? null : new Timestamp(date.getTime()));
            }
        });*/
    }

}
