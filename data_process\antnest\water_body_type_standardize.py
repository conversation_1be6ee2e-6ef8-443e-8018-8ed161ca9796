# -*- coding: utf-8 -*-
"""
# 将AI治理后的水体名称人工进行名称标准化
# Author: LRJ
# Date: 2024-12-05
# Version: 1.0.0
"""

import pandas as pd

from cust_utils import time_stat, load_data, write_result_data
from custom_log_config import init_default_logger
from cust_utils import str_val
from process_file_config import get_curr_process_item

# 配置输入输出文件

# 输入文件
# filepath = "data/mash_12580.xlsx"
curr_process_item = get_curr_process_item()
# 输入文件
filepath = curr_process_item.get('main_process_input_filepath')

# 水体类型名称标准化数据
water_body_name_file = 'data/water_body_type_stand_data.xlsx'

# 配置日志
log_file_name = 'water_body_type_standardize.log'
logger = init_default_logger(log_file_name)


# 项目启动入口
@time_stat(logger)
def main():
    logger.info("开始执行 water_body_type_standardize.py")

    df = load_data(filepath)

    # 数据治理
    df = standardize_data(df)

    output_file = write_result_data(df, filepath)
    logger.info("Processed data saved to %s", output_file)
    logger.info("water_body_type_standardize.py 执行完毕")


# 读水体名称文件
def load_water_body_name(file):
    try:
        df = pd.read_excel(file)
        dict_data = dict(zip(df['原始名称'].str.upper(), df['标准化名称']))
        logger.info("water_body_name dictionary loaded successfully.")
        return dict_data
    except Exception as e:
        logger.error(f"Error loading water_body_name dictionary: {e}")
        return {}


# 规范名称
def standardize_data(df):
    # 加载水体数据标准化字典
    water_body_name_dict = load_water_body_name(water_body_name_file)

    # 获取字典的所有值并转换为 set
    # values_set = set(water_body_name_dict.values())

    if not water_body_name_dict:
        logger.error("No water_body_name data available, aborting process.")
        return

    for index, row in df.iterrows():
        water_body = row.get('Water Body Type', None)
        if pd.isnull(water_body):
            run_id = str_val(row, 'RunID')
            logger.error(f"RunID {run_id}:Water Body Type is empty.")
            continue

        target_column = 'Water Body Type'

        if water_body.upper() in water_body_name_dict:
            df.at[index, target_column] = water_body_name_dict[water_body.upper()]

    return df


if __name__ == "__main__":
    main()
