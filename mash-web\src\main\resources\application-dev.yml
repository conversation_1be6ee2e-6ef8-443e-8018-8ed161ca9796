spring:
  data:
    mongodb:
      uri: **********************************************************************
      auto-index-creation: true
  elasticsearch:
    uris: http://************:30161
    socket-keep-alive: true
  ai:
    embedding:
      transformer:
        enabled: true
        onnx:
          model-uri: file:D:\model\bce-embedding-base_v1-onnx\model.onnx
          model-output-name: token_embeddings
        # HuggingFaceTokenizer options such as ‘addSpecialTokens’, ‘modelMaxLength’, ‘truncation’, ‘padding’, ‘maxLength’, ‘stride’, ‘padToMultipleOf’. Leave empty to fallback to the defaults.
        tokenizer:
          uri: file:D:\model\bce-embedding-base_v1-onnx\tokenizer.json
          options:
            maxLength: 512
            modelMaxLength: 512
            #truncation: true
            # 更灵活的填充策略（可选值：true, false, 'max_length'）
            #padding: false
            # 截断时的重叠步长（用于长文本分块）
            #stride: 128
            # 填充长度对齐到 8 的倍数（优化 GPU 计算）
            #padToMultipleOf: 8


logging:
  level:
    org.springframework.data.elasticsearch: debug
    org.elasticsearch.client: debug
    # 打印mongo查询语句
    org.springframework.data.mongodb.core.MongoTemplate: debug



app:
  data-home: "D:\\Temp\\mash_data\\data"
  # 统计页面验证token
  stat-token: "kZ7mXbLQwE92Rt"
  # Dify api请求地址
  dify-url: "https://www.biosino.org/dify/v1"
  # Dify api密钥
  dify-token: "app-LPZxTYAyycY79PmhBf95fF21"
  # milvus知识库请求地址
#  knowledge-url: "http://************:32080/zsk-api"
  knowledge-url: "https://www.biosino.org/knowledge-api"
  # milvus知识库名称
  knowledge-id: "mash_250626_170w"
  # milvus知识库密钥
  knowledge-token: "lrj_test_token_2025"
  # 专家问答收件人管理员邮箱
  admin-email: "<EMAIL>"
  # 专家问答邮件抄送管理员邮箱
  cc-emails:
    - "<EMAIL>"
  node:
    endpoint: https://dev.biosino.org/keep-node2/prod-api/api
    api-token: d08779ddd3a944aa17ad0f20fb256547
    get-experiment-metadata-url: ${app.node.endpoint}/metadata/experiment/list
    save-request-data-url: ${app.node.endpoint}/node/resourceAuthorize/saveBatch
    get-metadata-url: ${app.node.endpoint}/metadata/{}/list
  antnest:
    admin-email: <EMAIL>
