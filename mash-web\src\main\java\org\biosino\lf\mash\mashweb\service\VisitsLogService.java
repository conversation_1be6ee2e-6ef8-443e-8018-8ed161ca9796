package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.VisitsLogDTO;
import org.biosino.lf.mash.mashweb.mapstruct.VisitsLogStructMapper;
import org.biosino.lf.mash.mashweb.mongo.entity.VisitsLog;
import org.biosino.lf.mash.mashweb.mongo.repository.VisitsLogRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.*;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 * 访问日志服务层
 *
 * <AUTHOR>
 * @date 2025/6/10
 */
@Service
@RequiredArgsConstructor
public class VisitsLogService {
    private final VisitsLogRepository visitsLogRepository;
    private final MongoTemplate mongoTemplate;
    private final AppConfig appConfig;

    /**
     * 功能模块列表
     */
    private static final Collection<String> FUNCTION_MODULES = CollUtil.unmodifiable(Arrays.asList(
            "Scholarly Archive Search", "Export Data",
            "Apply NODE Data", "Apply Ant Nest Data", "Ask AI", "Consult Expert"
    ));

    private static final Collection<String> VISITS_URLS = CollUtil.unmodifiable(Arrays.asList(
            "/home",
            "/browse",
            "/scholarly",
            "/expert-qa",
            "/diversity",
            "/genomic",
            "/biota",
            "/diversity/detail"
    ));

    /**
     * 添加访问日志
     */
    @PostMapping("/addVisitsLog")
    public Map<String, Object> addVisitsLog(final VisitsLogDTO dto, final HttpServletRequest request) {
        final Map<String, Object> result = new HashMap<>();
        try {
            final String functionModule = dto.getFunctionModule();
            if (StrUtil.isNotBlank(functionModule) && !FUNCTION_MODULES.contains(functionModule)) {
                throw new ServiceException("Unknown function module");
            }

            // 全部转为小写url
            String url = dto.getUrl().toLowerCase();
            // 去除url末尾的斜杠
            url = url.endsWith("/") ? url.substring(0, url.lastIndexOf('/')) : url;

            boolean isVisitsUrl = false;
            for (String visitsUrl : VISITS_URLS) {
                if (url.equals(visitsUrl)) {
                    isVisitsUrl = true;
                    break;
                }
            }
            // 统一设置url
            dto.setUrl(url);
            if (!isVisitsUrl) {
                result.put("code", 500);
                result.put("msg", "Unknown visits url");
                return result;
            }

            final VisitsLog visitsLog = new VisitsLog();
            VisitsLogStructMapper.INSTANCE.copyToDb(dto, visitsLog);

            visitsLog.setQuery(objToJsonStr(dto.getQueryObj()));
            visitsLog.setParams(objToJsonStr(dto.getParamsObj()));
            visitsLog.setData(objToJsonStr(dto.getDataObj()));

            // 获取请求IP
            //ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (request != null) {
                visitsLog.setIp(JakartaServletUtil.getClientIP(request));
            }

            // 设置创建时间
            visitsLog.setCreateTime(new Date());

            // 保存到MongoDB
            visitsLogRepository.save(visitsLog);

            result.put("code", 200);
            result.put("msg", "success");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "Failed to save visit log: " + e.getMessage());
        }
        return result;
    }

    private String objToJsonStr(final Object obj) {
        if (obj == null) {
            return null;
        }
        // 接取字符，防止数据量过大
        final String sub = StrUtil.trimToNull(StrUtil.sub(JSON.toJSONString(obj), 0, 2500));
        return (sub == null || "{}".equals(sub) || "[]".equals(sub)) ? null : sub;
    }


    public ResponseEntity<Map<String, Object>> getStatistics(HttpServletRequest request) {
        // 验证令牌
        final String token = request.getHeader("X-Stats-Token");
        if (token == null || !token.equals(appConfig.getStatToken())) {
            throw new ServiceException("访问令牌错误");
        }

        Map<String, Object> result = new HashMap<>();
        Map<String, Object> data = new HashMap<>();

        try {
            // 查询总访问量(PV)
            final long totalVisits = visitsLogRepository.count();

            // 查询独立访客数(UV) - 需要对sessionId去重
            Aggregation uvAggregation = Aggregation.newAggregation(
                    group("sessionId"),
                    count().as("count")
            );
            AggregationResults<Map> uvResults = mongoTemplate.aggregate(
                    uvAggregation, VisitsLog.class, Map.class
            );
            final List<Map> uvResultsMappedResults = uvResults.getMappedResults();
            long uniqueVisitors = 0;
            if (CollUtil.isNotEmpty(uvResultsMappedResults)) {
                for (Map map : uvResultsMappedResults) {
                    uniqueVisitors = Long.parseLong(map.get("count").toString());
                }
            }

            // 1. 页面访问统计（functionModule为null的访问）
            // URL分组统计
            final Aggregation pageAggregation = Aggregation.newAggregation(
                    match(Criteria.where("functionModule").isNull()),
                    group("url").count().as("count"),
                    sort(Sort.Direction.DESC, "count")
            );

            final AggregationResults<Map> pageResults = mongoTemplate.aggregate(
                    pageAggregation, "visits_log", Map.class
            );

            // 计算页面访问总量
            long pageVisitsTotal = 0;
            for (Map doc : pageResults.getMappedResults()) {
                Number count = (Number) doc.get("count");
                if (count != null) {
                    pageVisitsTotal += count.longValue();
                }
            }

            // 转换为前端需要的格式
            List<Map<String, Object>> pageStats = new ArrayList<>();
            for (Map doc : pageResults.getMappedResults()) {
                Map<String, Object> item = new HashMap<>();
                // 从_id字段中获取URL
                String url = doc.get("_id") != null ? doc.get("_id").toString() : "";
                Number count = (Number) doc.get("count");
                long visitCount = count != null ? count.longValue() : 0;

                item.put("url", url);
                item.put("count", visitCount);

                // 计算占比
                double percentage = pageVisitsTotal > 0 ? (visitCount * 100.0 / pageVisitsTotal) : 0;
                item.put("percentage", Math.round(percentage * 100) / 100.0); // 保留两位小数

                pageStats.add(item);
            }

            // 2. 功能统计（functionModule不为null的访问）
            Aggregation funcAggregation = Aggregation.newAggregation(
                    match(Criteria.where("functionModule").ne(null)),
                    group("functionModule", "url").count().as("count"),
                    sort(Sort.Direction.DESC, "count")
            );

            AggregationResults<Map> funcResults = mongoTemplate.aggregate(
                    funcAggregation, "visits_log", Map.class
            );

            // 计算功能访问总量
            long funcVisitsTotal = 0;
            for (Map doc : funcResults.getMappedResults()) {
                Number count = (Number) doc.get("count");
                if (count != null) {
                    funcVisitsTotal += count.longValue();
                }
            }

            // 转换为前端需要的格式
            List<Map<String, Object>> funcStats = new ArrayList<>();
            for (Map doc : funcResults.getMappedResults()) {
                Map<String, Object> item = new HashMap<>();

                Map idObj = (Map) doc.get("_id");
                String funcName = idObj.get("functionModule") != null ? idObj.get("functionModule").toString() : "";
                String url = idObj.get("url") != null ? idObj.get("url").toString() : "";
                Number count = (Number) doc.get("count");
                long visitCount = count != null ? count.longValue() : 0;

                item.put("name", funcName);
                item.put("url", url);
                item.put("count", visitCount);

                // 计算占比
                double percentage = funcVisitsTotal > 0 ? (visitCount * 100.0 / funcVisitsTotal) : 0;
                item.put("percentage", Math.round(percentage * 100) / 100.0); // 保留两位小数

                funcStats.add(item);
            }

            // 组装返回数据
            data.put("pv", totalVisits);
            data.put("uv", uniqueVisitors);
            data.put("pageStats", pageStats);
            data.put("funcStats", funcStats);

            result.put("code", 200);
            result.put("msg", "获取统计数据成功");
            result.put("data", data);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "获取统计数据失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

}
