package org.biosino.lf.mash.mashweb;

import org.biosino.lf.mash.mashweb.config.NodeProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication
@EnableConfigurationProperties(NodeProperties.class)
public class MashWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(MashWebApplication.class, args);
    }

}
