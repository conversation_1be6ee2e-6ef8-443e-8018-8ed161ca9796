package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.es.entity.MashArticleEs;
import org.biosino.lf.mash.mashweb.es.repository.MashArticleEsRepository;
import org.biosino.lf.mash.mashweb.mongo.entity.MashArticleMongo;
import org.biosino.lf.mash.mashweb.mongo.repository.MashArticleMongoRepository;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.transformer.splitter.TextSplitter;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EsService {
    private final ElasticsearchTemplate elasticsearchTemplate;
    private final EmbeddingModel embeddingModel;
    private final TextSplitter textSplitter;
    private final MashArticleEsRepository mashArticleEsRepository;
    private final MashArticleMongoRepository mashArticleMongoRepository;

    /**
     * 项目启动后创建索引
     */
    @PostConstruct
    public void createEsIndex() {
        final IndexOperations indexOperations = elasticsearchTemplate.indexOps(MashArticleEs.class);
        if (!indexOperations.exists()) {
            indexOperations.createWithMapping();
            log.info("创建索引成功: {}", MashArticleEs.ES_INDEX_NAME);
        } else {
            log.info("索引已存在: {}", MashArticleEs.ES_INDEX_NAME);
        }
    }


    public void initEs() {
        final long start = System.currentTimeMillis();
        log.warn("开始初始化索引: {}", MashArticleEs.ES_INDEX_NAME);
        mashArticleEsRepository.deleteAll();
        final List<MashArticleMongo> list = mashArticleMongoRepository.findAllBySource("MASH");
        final int mashSize = CollUtil.size(list);
        log.warn("MASH文献数量：{}", mashSize);
        if (mashSize > 0) {
            for (int i = 0; i < mashSize; i++) {
                final MashArticleMongo item = list.get(i);
                log.warn("开始插入 {} 条数据到es索引, PMID: {}", i, item.getPmid());
                insertArticleEs(item);
            }
        }
        final IndexOperations indexOperations = elasticsearchTemplate.indexOps(MashArticleEs.class);
        indexOperations.refresh();
        log.warn("成功插入 {} 条数据到索引: {}，耗时：{}", mashSize, MashArticleEs.ES_INDEX_NAME,
                DateUtil.formatBetween(System.currentTimeMillis() - start, BetweenFormatter.Level.SECOND));
    }

    private Long insertArticleEs(final MashArticleMongo item) {
        final MashArticleEs mashArticleEs = new MashArticleEs();
        final Long pmid = item.getPmid();
        mashArticleEs.setId(pmid.toString());
        mashArticleEs.setPmid(pmid);
        mashArticleEs.setTitle(item.getTitle());
        mashArticleEs.setDoi(item.getDoi());
        mashArticleEs.setJournalTitle(item.getJournalTitle());
        mashArticleEs.setYear(item.getYear());
        mashArticleEs.setVolume(item.getVolume());
        mashArticleEs.setIssue(item.getIssue());
        mashArticleEs.setAuthor(item.getAuthor());
        mashArticleEs.setCreateDate(item.getCreateDate());
        mashArticleEs.setEsUpdateDate(new Date());

        mashArticleEs.setNcbiSraIds(item.getNcbiSraIds());
        mashArticleEs.setNodeIds(item.getNodeIds());

        mashArticleEs.setBioProjectsCount(CollUtil.size(item.getNcbiSraIds()) + CollUtil.size(item.getNodeIds()));

        final String abs = item.getAbs();
        mashArticleEs.setArticleAbstract(abs);
        mashArticleEs.setTitleVector(normalizeVector(embeddingModel, item.getTitle()));

        final MashArticleMongo.PdfContent pdfDocContent = item.getPdfDocContent();
        final Set<String> absSet = new LinkedHashSet<>();
        if (StrUtil.isNotBlank(abs)) {
            absSet.add(abs);
        }

        if (pdfDocContent != null) {
            mashArticleEs.setContentVectors(initVectorsList(pdfDocContent.getBody()));

            final List<String> abstractInfo = pdfDocContent.getAbstractInfo();
            if (CollUtil.isNotEmpty(abstractInfo)) {
                absSet.addAll(abstractInfo);
            }
        }
        mashArticleEs.setAbstractVectors(initVectorsList(absSet));

        mashArticleEsRepository.save(mashArticleEs);
        return pmid;
    }

    /**
     * bce-embedding-base_v1模型生成的数据没有归一化，此方法可进行向量归一化.
     * 归一化是余弦相似度计算的重要前提，可以显著提高检索质量和稳定性。确保在整个流程中保持向量归一化的一致性。
     */
    public static float[] normalizeVector(EmbeddingModel embeddingModel, String text) {
        final float[] vector = embeddingModel.embed(text);
        double squareSum = 0.0;
        for (float v : vector) {
            squareSum += v * v;
        }
        final float norm = (float) Math.sqrt(squareSum);

        float[] normalized = new float[vector.length];
        for (int i = 0; i < vector.length; i++) {
            normalized[i] = vector[i] / norm;
        }
        return normalized;
    }

    /**
     * bce-embedding-base_v1模型生成的数据没有归一化，此方法可进行向量归一化。
     * 归一化是余弦相似度计算的重要前提，可以显著提高检索质量和稳定性。确保在整个流程中保持向量归一化的一致性。
     * 返回集合
     */
    public static List<Float> normalizeVectorList(EmbeddingModel embeddingModel, String text) {
        final float[] vector = normalizeVector(embeddingModel, text);
        final List<Float> normalized = new ArrayList<>(vector.length);
        for (float v : vector) {
            normalized.add(v);
        }
        return normalized;
    }


    private List<MashArticleEs.TextAndVectors> initVectorsList(final Collection<String> cols) {
        if (CollUtil.isNotEmpty(cols)) {
            List<MashArticleEs.TextAndVectors> vectors = new ArrayList<>();
            for (String s : cols) {
                if (StrUtil.isBlank(s)) {
                    continue; // 跳过空字符串
                }

                try {
                    // 分块处理长文本
                    final List<Document> chunks = textSplitter.split(new Document(s));
                    /*final int size = CollUtil.size(chunks);
                    if (size > 1) {
                        log.debug("长文本分块数量：{}", size);
                    }*/

                    for (Document chunk : chunks) {
                        String text = chunk.getText();
                        if (StrUtil.isNotBlank(text)) {
                            MashArticleEs.TextAndVectors item = new MashArticleEs.TextAndVectors();
                            item.setText(text);
                            try {
                                item.setVector(normalizeVector(embeddingModel, text));
                                vectors.add(item);
                            } catch (Exception e) {
                                log.error("生成文本向量失败，文本长度：{}，错误：{}", text.length(), e.getMessage());
                                // 可以选择跳过失败的文本块或使用默认向量
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("文本分块处理失败，文本长度：{}，错误：{}", s.length(), e.getMessage());
                    // 如果分块失败，可以尝试直接处理原文本
                    try {
                        MashArticleEs.TextAndVectors item = new MashArticleEs.TextAndVectors();
                        item.setText(s);
                        item.setVector(normalizeVector(embeddingModel, s));
                        vectors.add(item);
                    } catch (Exception ex) {
                        log.error("直接处理文本也失败，跳过该文本", ex);
                    }
                }
            }

            return CollUtil.isEmpty(vectors) ? null : vectors;
        } else {
            return null;
        }
    }

}
