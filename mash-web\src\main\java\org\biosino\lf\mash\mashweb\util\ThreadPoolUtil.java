package org.biosino.lf.mash.mashweb.util;

import cn.hutool.core.thread.ThreadUtil;

import java.util.concurrent.ExecutorService;

/**
 * 线程池
 *
 * <AUTHOR>
 */
public class ThreadPoolUtil {
    private static ExecutorService executorService;

    public static synchronized ExecutorService getExecutor() {
        if (executorService == null) {
            executorService = ThreadUtil.newExecutor(5, 10, 1024 * 8);
        }
        return executorService;
    }

}
