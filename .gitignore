######################################################################
# Build Tools

.gradle
/build/
/logs/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml
### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
.nb-gradle/
keep-mash
keep-mash.zip

######################################################################
# Others
*.log
*.xml.versionsBackup
*.swp

!*/build/*.java
!*/build/*.html
!*/build/*.xml

# python
__pycache__
venv
/data_process/antnest/data/
/data_process/antnest/logs/

/app/mash
/app/mash.zip
/data_process/project_article/logs/
/data_process/project_article/data/downloads/
/data_process/project_article/test1.py
/mash-web/src/test/java/org/biosino/lf/mash/mashweb/mytest/
/data_process/project_article/data/grobid_xml/
/data_process/project_article/data/excel/
/data_process/project_article/data/top100/
