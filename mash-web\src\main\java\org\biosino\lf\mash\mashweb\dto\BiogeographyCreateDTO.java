package org.biosino.lf.mash.mashweb.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @date 2025/7/21
 */
@Data
public class BiogeographyCreateDTO {

    /**
     * diversity 页面用 domain、taxonomy、speciesName
     */
    private String domain;

    private String taxonomy;

    private List<String> speciesNames;

    /**
     * genomic 页面用 kos，pathway
     */
    private List<String> kos;

    private String pathway;

    /**
     * runIds 和 queryDTO 是这两个页面公用的
     */
    private List<String> runIds;

    private SamplesQueryDTO queryDTO;
}
