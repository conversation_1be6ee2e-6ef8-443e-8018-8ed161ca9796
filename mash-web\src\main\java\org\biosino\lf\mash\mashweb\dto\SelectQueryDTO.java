package org.biosino.lf.mash.mashweb.dto;

import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
public class SelectQueryDTO {
    /**
     * 第几页
     */
    private Integer pageNum = 0;
    /**
     * 每页多少条
     */
    private Integer pageSize = 50;

    /**
     * 查询条件
     */
    private String search;

    /**
     * MongoDB分页
     *
     * @return
     */
    public Pageable getPageable() {
        pageSize = pageSize == null || pageSize <= 0 ? Integer.MAX_VALUE : pageSize;
        return PageRequest.of(pageNum, pageSize);
    }
}
