package org.biosino.lf.mash.mashweb.util.kegg;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> @date 2025/7/22
 */
public class KeggParser {

    public static List<KeggEntry> parseKeggFile(File file) throws IOException {
        return parseKeggFile(file.getAbsolutePath());
    }

    public static List<KeggEntry> parseKeggFile(String filePath) throws IOException {
        List<KeggEntry> result = new ArrayList<>();
        String levelA = null, levelB = null, levelC = null, pathwayName = null;

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 跳过注释和 HTML 标签、空行
                if (line.isEmpty() || line.startsWith("#") || line.startsWith("!") || line.startsWith("+D")) {
                    continue;
                }

                if (line.length() < 2) continue;

                char prefix = line.charAt(0);
                String content = line.substring(1).trim();

                switch (prefix) {
                    case 'A':
                        levelA = content;
                        levelB = null;
                        levelC = null;
                        break;
                    case 'B':
                        levelB = content;
                        levelC = null;
                        break;
                    case 'C':
                        Pattern pattern = Pattern.compile("\\[PATH:(.*?)\\]");
                        Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            String extractedValue = matcher.group(1);
                            levelC = extractedValue;
                        }
                        pathwayName = content;
                        break;
                    case 'D':
                        String[] parts = content.split("\\s+", 2);
                        String koId = parts[0];
                        String desc = parts.length > 1 ? parts[1] : "";
                        result.add(new KeggEntry(levelA, levelB, levelC, koId, desc, pathwayName));
                        break;
                    default:
                        // 忽略其它非标准前缀
                        break;
                }
            }
        }
        // 过滤掉level c是空的
        return result.stream().filter(x -> x.getLevelC() != null).toList();
    }
}
