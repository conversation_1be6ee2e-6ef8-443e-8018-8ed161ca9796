package org.biosino.lf.mash.mashweb.mongo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 * @date 2023/9/8
 */

@Data
@Document("kegg_energy_metabolism")
public class KEGGEnergyMetabolism {

    @Id
    private String id;

    @Alias("Pathway_KO")
    @Field("pathway_ko")
    private String pathwayKO;

    @Alias("Pathway_Entry")
    @Field("pathway_entry")
    private String pathwayEntry;

    @Alias("Pathway_Name")
    @Field("pathway_name")
    private String pathwayName;

    @Alias("Orthology_Symbol")
    @Field("orthology_symbol")
    private String orthologySymbol;

    @Alias("Orthology_Entry")
    @Field("orthology_entry")
    private String orthologyEntry;

    @Alias("KO_Symbol")
    @Field("ko_symbol")
    private String koSymbol;

    @Alias("Orthology_Name")
    @Field("orthology_name")
    private String orthologyName;

}
