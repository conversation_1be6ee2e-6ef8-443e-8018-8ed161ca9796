import time
import psycopg2
from sentence_transformers import SentenceTransformer
from tqdm import tqdm
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk

# ------------------------------
# 配置参数
# ------------------------------
ELASTICSEARCH_HOST = "http://**************:9200"
NUM_PAPERS = 10000000 # 同步的文献总数
PGSQL_BATCH_SIZE = 100 # 单次从PgSQL查询的数量
INSERT_BATCH_SIZE = 100 # 单次插入ES中的数量
MAX_ABSTRACT_VECTORS = 20  # 每篇摘要最多存储的句子向量数
INDEX_NAME = f"papers_multi_vectors_{NUM_PAPERS}"

# ------------------------------
# 初始化组件
# ------------------------------
es = Elasticsearch(ELASTICSEARCH_HOST)
model = SentenceTransformer(r'D:\model\all-MiniLM-L6-v2')
vector_dim = model.get_sentence_embedding_dimension()

# ------------------------------
# PostgreSQL配置（保持不变）
# ------------------------------
DB_CONFIG = {
    'host': '************',
    'port': '31909',
    'dbname': 'plosp',
    'user': 'postgres',
    'password': 'Biosino+2025'
}

def fetch_papers_from_db(offset=0, limit=1000):
    """从 PostgreSQL 数据库中获取论文数据，筛选出 article_abstract 有值的记录，支持分页"""
    connection = psycopg2.connect(**DB_CONFIG)
    cursor = connection.cursor()

    query = f"""
        SELECT pmid, language, title, published_year, year, volume, issue, author, update_date, article_abstract
        FROM plosp_lwj.tb_dds_article
        WHERE article_abstract IS NOT NULL
        OFFSET {offset} LIMIT {limit};
    """
    cursor.execute(query)
    papers = cursor.fetchall()
    cursor.close()
    connection.close()

    paper_list = [
        {
            'pmid': pmid,
            'language': language,
            'title': title,
            'published_year': published_year,
            'year': year,
            'volume': volume,
            'issue': issue,
            'author': author,
            'update_date': update_date,
            'article_abstract': article_abstract
        }
        for pmid, language, title, published_year, year, volume, issue, author, update_date, article_abstract in papers
    ]
    return paper_list

# ------------------------------
# 新增：摘要分割函数
# ------------------------------
def split_abstract(text):
    """分割摘要为有效句子并过滤"""
    sentences = [s.strip() for s in text.split('.') if len(s.strip()) >= 20]
    return sentences[:MAX_ABSTRACT_VECTORS]

# ------------------------------
# 修改后的数据插入逻辑
# ------------------------------
def insert_papers(num_papers, pgsql_batch_size=PGSQL_BATCH_SIZE, insert_batch_size=INSERT_BATCH_SIZE):
    print(f"Fetching {num_papers} papers from PostgreSQL...")

    # 删除旧索引（新增映射定义）
    if es.indices.exists(index=INDEX_NAME):
        es.indices.delete(index=INDEX_NAME)
    
    # 新映射定义
    mapping = {
        "mappings": {
            "properties": {
                "pmid": {"type": "keyword"},
                "language": {"type": "keyword"},
                "title": {"type": "text"},
                "published_year": {"type": "integer"},
                "year": {"type": "integer"},
                "volume": {"type": "keyword"},
                "issue": {"type": "keyword"},
                "author": {"type": "keyword"},
                "update_date": {"type": "date"},
                "article_abstract": {"type": "text"},
                "title_vector": {
                    "type": "dense_vector",
                    "dims": vector_dim
                },
                "abstract_vectors": {
                    "type": "nested",
                    "properties": {
                        "text": {"type": "text"},
                        "vector": {"type": "dense_vector", "dims": vector_dim}
                    }
                }
            }
        }
    }
    es.indices.create(index=INDEX_NAME, body=mapping)

    start_time = time.time()
    with tqdm(total=num_papers, desc="Inserting papers", unit="paper") as pbar:
        offset = 0
        while offset < num_papers:
            batch_papers = fetch_papers_from_db(offset=offset, limit=pgsql_batch_size)
            if not batch_papers:
                break

            actions = []
            for j, p in enumerate(batch_papers):
                # 处理标题向量
                title_vector = model.encode(p["title"]).tolist()
                
                # 处理摘要句子
                abstract_sentences = split_abstract(p["article_abstract"])
                abstract_items = []
                if abstract_sentences:
                    # 生成摘要向量（标题+摘要句子）
                    texts_to_encode = [p["title"]] + abstract_sentences
                    vectors = model.encode(texts_to_encode).tolist()
                    abstract_items = [
                        {"text": text, "vector": vec}
                        for text, vec in zip(texts_to_encode, vectors)
                    ]

                # 构建文档
                doc = {
                    **p,
                    "title_vector": title_vector,
                    "abstract_vectors": abstract_items
                }
                actions.append({
                    "_index": INDEX_NAME,
                    "_id": p["pmid"],
                    "_source": doc
                })

            # 批量插入
            for i in range(0, len(actions), insert_batch_size):
                bulk(es, actions[i:i+insert_batch_size])
            
            pbar.update(len(batch_papers))
            offset += len(batch_papers)

    print(f"Inserted {offset} papers in {time.time()-start_time:.2f}s")

# ------------------------------
# 新增多向量检索方法
# ------------------------------
def multi_vector_search(query_text, author=None, year_range=None, top_k=5, page=1):
    """多向量联合检索与聚合分析
    :param page: 分页页码（从1开始）
    """
    # 新增分页计算
    page_size = top_k  # 每页结果数
    from_ = (page - 1) * page_size
    
    query_vector = model.encode(query_text).tolist()
    
    # 构建基础查询
    query = {
        "query": {
            "nested": {
                "path": "abstract_vectors",
                "score_mode": "max",
                "query": {
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'abstract_vectors.vector') + 1.0",
                            "params": {"query_vector": query_vector}
                        }
                    }
                }
            }
        },
        "aggs": {
            "author_distribution": {
                "terms": {"field": "author", "size": 10}
            },
            "year_distribution": {
                "histogram": {
                    "field": "published_year",
                    "interval": 5,
                    "extended_bounds": {
                        "min": 1900,
                        "max": 2025
                    }
                }
            }
        },
        "size": top_k
    }
    # 修改查询体添加分页参数
    query.update({
        "from": from_,
        "size": page_size,
        "track_total_hits": True  # 新增：确保获取精确总数
    })
    # 添加过滤条件
    if author or year_range:
        filter_clause = []
        if author:
            filter_clause.append({"term": {"author": author}})
        if year_range:
            filter_clause.append({
                "range": {
                    "published_year": {
                        "gte": year_range[0],
                        "lte": year_range[1]
                    }
                }
            })
        query["query"] = {
            "bool": {
                "must": [query["query"]],
                "filter": filter_clause
            }
        }

    # 执行查询
    start_time = time.time()
    response = es.search(index=INDEX_NAME, body=query)
    elapsed_time = time.time() - start_time

    # 处理结果
    print(f"\n🔍 Multi-Vector Search: '{query_text}' (Top {top_k})")
    for hit in response["hits"]["hits"]:
        source = hit["_source"]
        print(f"PMID: {source['pmid']} | Title: {source['title'][:50]}...")
        print(f"  Score: {hit['_score']:.4f} | Author: {source['author']} | Year: {source['published_year']}\n")

    # 输出聚合结果
    print("\n📊 Author Distribution:")
    for bucket in response["aggregations"]["author_distribution"]["buckets"]:
        print(f" - {bucket['key']}: {bucket['doc_count']} papers")

    print("\n📅 Publication Year Distribution:")
    for bucket in response["aggregations"]["year_distribution"]["buckets"]:
        print(f" - {int(bucket['key'])}s: {bucket['doc_count']} papers")
    # 修改结果处理部分
    total_hits = response["hits"]["total"]["value"]
    print(f"\n🔍 找到 {total_hits} 条结果 | 第 {page} 页 (每页 {page_size} 条)")
    for hit in response["hits"]["hits"]:
        source = hit["_source"]
        print(f"PMID: {source['pmid']} | Title: {source['title'][:50]}...")
        print(f"  Score: {hit['_score']:.4f} | Author: {source['author']} | Year: {source['published_year']}\n")

    # 在最后添加分页信息
    print(f"\n📄 分页信息：第 {page} 页/共 {max(1, (total_hits + page_size - 1) // page_size)} 页")
    print(f"\nTotal search time: {elapsed_time:.2f}s")

# ------------------------------
# 主程序
# ------------------------------
if __name__ == "__main__":
    # 数据插入
    # insert_papers(NUM_PAPERS)
    
    # 测试多向量搜索
    multi_vector_search(
        "The results, based on data for the period 1970-1981, show sharp differentials by sex and zone",
        page=1,
        top_k=5
    )