# -*- coding: utf-8 -*-

"""
grobid pdf解析
Author: LWJ
Version: 1.0.0
"""

import argparse
import os

from grobid_client.grobid_client import GrobidClient, ServerUnavailableException


# nohup tar -zxvf downloads.tar.gz > tar-run.log 2>&1 & echo $! > tar-run.pid
# docker run -d --name mash-grobid --rm --gpus '"device=2"' --init --ulimit core=0 -p 18081:8070 grobid/grobid:0.8.0
# bash
# conda activate grobid
# python 2.process_pdfs.py http://127.0.0.1:18081 mash/input_3w/downloads mash/input_3w/output

def process_pdfs(grobid_server, input_dir, output_dir, batch_size=40, n=2):
    """
    使用 Grobid 解析 PDF 文件
    :param grobid_server: Grobid 服务器地址
    :param input_dir: PDF 输入目录
    :param output_dir: 解析后 XML 输出目录
    :param batch_size: 每批次处理的文件数量
    :param n: 并行请求数量
    """
    if not os.path.exists(input_dir):
        print(f"错误：输入目录 {input_dir} 不存在！")
        return

    os.makedirs(output_dir, exist_ok=True)  # 确保输出目录存在

    try:
        client = GrobidClient(
            grobid_server=grobid_server,
            batch_size=batch_size,
            sleep_time=5,
            timeout=600,
            check_server=True,
            coordinates=["persName", "figure", "ref", "biblStruct", "formula", "s", "head", "note"],
        )

        client.process(
            "processFulltextDocument",
            input_dir,
            output=output_dir,
            segment_sentences=False,
            generateIDs=True,
            n=n,
            consolidate_header=True,
            consolidate_citations=False,
            tei_coordinates=True,
            force=True
        )

        print(f"解析完成！输入目录：{input_dir}，输出目录：{output_dir}")

    except ServerUnavailableException:
        print(f"错误：Grobid 服务 {grobid_server} 无法访问，请检查服务器状态！")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="使用 Grobid 解析 PDF 文件")
    parser.add_argument("grobid_server", type=str, help="Grobid 服务器地址，例如：http://localhost:8070")
    parser.add_argument("input_dir", type=str, help="PDF 输入目录")
    parser.add_argument("output_dir", type=str, help="解析后 XML 输出目录")
    parser.add_argument("--batch_size", type=int, default=40, help="每批次处理的文件数量 (默认 40)")
    parser.add_argument("--n", type=int, default=2, help="并行请求数量 (默认 2)")

    args = parser.parse_args()
    process_pdfs(args.grobid_server, args.input_dir, args.output_dir, args.batch_size, args.n)
