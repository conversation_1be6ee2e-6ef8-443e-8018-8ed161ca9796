package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.MilvusMetaQueryDTO;
import org.springframework.web.bind.annotation.*;

/**
 * Milvus向量库元数据
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zsk")
public class MilvusMetaController extends BaseController {
    private final AppConfig appConfig;


    @PostMapping("/metadata")
    public JSONObject proxyStreaming(@RequestHeader("Authorization") final String auth,
                                     @RequestBody MilvusMetaQueryDTO queryDTO) {
        // 1. 验证本地token
        if (!validateLocalToken(auth)) {
            throw new ServiceException("Invalid token!");
        }


        final JSONObject paramMap = new JSONObject();
        paramMap.put("pmid", queryDTO.getPmid());
        final String childText = queryDTO.getChild_text();
        if (childText != null) {
            paramMap.put("child_text", childText);
        }
        paramMap.put("knowledge_id", appConfig.getKnowledgeId());

        final String urlString = appConfig.getKnowledgeUrl() + "/metadata";
        try (HttpResponse response = post(urlString, paramMap.toJSONString(), appConfig.getKnowledgeToken())
                .execute()) {
            final int status = response.getStatus();
            final String body = response.body();
            if (status != 200) {
                throw new ServiceException("Milvus知识库检索出错, Http code:" + status + ". Error info: \n" + body);
            }
            return JSON.parseObject(body);
        }
    }


}
