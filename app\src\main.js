import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import '@/assets/styles/icon.css';
import App from './App.vue';
import router from '@/router';
import { download } from '@/utils/request';
import '@/assets/styles/index.scss';
import '@/assets/styles/matertialIcon.scss';
import '@/assets/main.css';
import 'leaflet/dist/leaflet.css';
// eslint-disable-next-line no-unused-vars
import L from 'leaflet';
// 引入地图所需依赖，确保全局可用
import '@/assets/js/geojson-vt';
import '@/assets/js/leaflet-geojson-vt.js';
import directive from './directive'; // directive
import ElTableInfiniteScroll from 'el-table-infinite-scroll';

import './permission'; // permission control
import ClickOutside from 'element-plus/es/directives/click-outside/index';
import _ from 'lodash';
// svg图标
import 'virtual:svg-icons-register';
import SvgIcon from '@/components/SvgIcon/index.vue';

// 注册指令
import plugins from './plugins';
import store from './store';
// 将Leaflet绑定到全局window对象，确保在所有组件中可用
window.L = L;

const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.config.globalProperties.$_ = _;
app.config.globalProperties.download = download;
app.use(ElTableInfiniteScroll);
app.use(router);
app.use(store);
// app.use(HotTable)
app.use(plugins);

app.component('SvgIcon', SvgIcon);
app.directive('click-outside', ClickOutside);
directive(app);
app.use(ElementPlus, {
  // locale: zhCn,
});

app.mount('#app');
