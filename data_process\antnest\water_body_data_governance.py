import logging
import os
from typing import Tuple, Dict

import geopandas as gpd
import pandas as pd
from shapely.geometry import Point

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 文件路径
EXCEL_FILE_PATH = 'data/MASH_Merge_250512.xlsx'
CONTINENTS_GEOJSON_PATH = 'data/geojson/continents.geojson'
OCEAN_GEOJSON_PATH = 'data/geojson/ocean.geojson'
WATER_BODY_DICT_PATH = 'data/water_body_governance_dict.xlsx'


def load_water_body_dict() -> Dict[str, Dict[str, str]]:
    """加载水体治理字典"""
    logger.info(f"加载水体治理字典: {WATER_BODY_DICT_PATH}")
    try:
        # 读取Excel文件
        dict_df = pd.read_excel(WATER_BODY_DICT_PATH)
        
        # 确保必要的列存在
        required_columns = ['Water Body Name', 'Hydrosphere Type', 
                          'Water Body Type (By Classification)', 'Water Body Type (By Geographic)']
        for col in required_columns:
            if col not in dict_df.columns:
                logger.error(f"水体治理字典缺少必要的列: {col}")
                return {}
        
        # 构建字典，以Water Body Name为键
        water_body_dict = {}
        for _, row in dict_df.iterrows():
            water_body_name = row['Water Body Name']
            if pd.notna(water_body_name) and water_body_name != 'unspecified':
                water_body_dict[water_body_name] = {
                    'Hydrosphere Type': row['Hydrosphere Type'],
                    'Water Body Type (By Classification)': row['Water Body Type (By Classification)'],
                    'Water Body Type (By Geographic)': row['Water Body Type (By Geographic)']
                }
        
        logger.info(f"水体治理字典加载完成，共{len(water_body_dict)}条记录")
        return water_body_dict
    except Exception as e:
        logger.error(f"加载水体治理字典时出错: {str(e)}")
        return {}


def load_geospatial_data() -> Tuple[gpd.GeoDataFrame, gpd.GeoDataFrame]:
    """加载地理空间数据文件"""
    logger.info("加载地理空间数据文件...")
    continents_gdf = gpd.read_file(CONTINENTS_GEOJSON_PATH)
    ocean_gdf = gpd.read_file(OCEAN_GEOJSON_PATH)
    logger.info(f"已加载大洲数据: {len(continents_gdf)}行, 海洋数据: {len(ocean_gdf)}行")
    return continents_gdf, ocean_gdf


def find_nearest_geometry(point: Point, gdf: gpd.GeoDataFrame, attribute_name: str) -> str:
    """找到距离点最近的几何图形并返回指定属性值"""
    if gdf.empty:
        return "Unclassfied"

    min_distance = float('inf')
    nearest_attribute = "Unclassfied"

    for idx, row in gdf.iterrows():
        try:
            # 计算点到几何图形的距离
            distance = point.distance(row.geometry)
            if distance < min_distance:
                min_distance = distance
                nearest_attribute = row[attribute_name]
        except Exception as e:
            logger.warning(f"计算距离时出错: {str(e)}")
            continue

    return nearest_attribute


def get_location_type_by_hydrosphere(lat: float, lon: float, hydrosphere_type: str,
                                     continents_gdf: gpd.GeoDataFrame, ocean_gdf: gpd.GeoDataFrame) -> str:
    """根据水文圈类型和经纬度找到最近的大洲或海洋"""
    if pd.isna(lat) or pd.isna(lon):
        return "Unclassfied"

    try:
        point = Point(lon, lat)  # 注意：经度在前，纬度在后

        # 根据水文圈类型决定查找大洲还是海洋
        if hydrosphere_type == "Inland water":
            return find_nearest_geometry(point, continents_gdf, 'CONTINENT')
        elif hydrosphere_type == "Marine":
            return find_nearest_geometry(point, ocean_gdf, 'name')
        else:
            return "Unclassfied"
    except Exception as e:
        logger.error(f"处理经纬度({lon}, {lat})时出错: {str(e)}")
        return "Unclassfied"


def determine_hydrosphere_type(water_body_type: str, water_body_name: str, biome: str) -> str:
    """确定水文圈类型"""
    # 内陆水体类型
    inland_water_types = [
        "Ice Caps & Glaciers", "Groundwater", "Lakes", "Rivers", "Saline lakes",
        "Wetlands", "Soli moisture", "Atomsphere", "Biota", "Freshwater lakes"
    ]

    # 海洋水体类型
    marine_water_types = [
        "Inland Seas", "North Pacific", "South Pacific", "Arctic Ocean", "Baltic Sea",
        "Indian Ocean", "Mediterranean Sea", "North Atlantic", "South Atlantic"
    ]

    # 红树林是海洋体系
    if water_body_type == 'Wetlands' and 'mangrove' in water_body_name.lower():
        return "Marine"

    # 根据Water Body Type反向推导
    if water_body_type in inland_water_types:
        return "Inland water"
    elif water_body_type in marine_water_types:
        return "Marine"

    if pd.notna(biome) and "marine" in biome.lower():
        return "Marine"

    return "Inland water"


def determine_classification(hydrosphere_type: str, water_body_type: str, water_body_name: str) -> str:
    """确定水体分类类型"""

    # 七大洲名称
    continents = ["Africa", "Antarctica", "Asia", "Europe", "North America", "Oceania", "South America"]

    # 海洋名称
    oceans = ["Arctic Ocean", "North Atlantic", "South Atlantic", "Indian Ocean",
              "North Pacific", "South Pacific", "Southern Ocean", "Mediterranean Sea", "Baltic Sea"]

    # 如果是内陆水体
    if hydrosphere_type == "Inland water":
        # 检查是否是人工水体
        artificial_keywords = ["canal", "reservoir", "tap water", "waste", "water tank", "drainage ditch"]
        if any(keyword in water_body_name.lower() for keyword in artificial_keywords):
            return "Artificial Water Bodies"

        # 特殊类型处理
        if water_body_type == "Freshwater lakes":
            return "Lakes"
        elif water_body_type in ["Soli moisture", "Atomsphere", "Biota"]:
            return "Unclassfied"

        if water_body_type in continents or water_body_type in oceans:
            return "Unclassfied"

        return water_body_type

    # 如果是海洋水体
    elif hydrosphere_type == "Marine":

        if "sea" in water_body_name.lower():
            return "Sea"
        elif "gulf" in water_body_name.lower():
            return "Gulf"
        elif "bay" in water_body_name.lower():
            return "Bay"
        elif "strait" in water_body_name.lower():
            return "Strait"
        elif "channel" in water_body_name.lower():
            return "Channel"
        elif "marsh" in water_body_name.lower() or "estuary" in water_body_name.lower():
            return "Marsh & Estuary"
        elif "lagoon" in water_body_name.lower():
            return "Lagoon"
        elif "mangrove" in water_body_name.lower():
            return "Mangrove"
        elif "fjord" in water_body_name.lower():
            return "Fjord"

    return "Unclassfied"


# Sampling Substrate（采样基质）
def process_sampling_substrate(df):
    logger.info("Processing Sampling Substrate column.")

    def infer_substrate(organism):
        if not pd.isnull(organism):
            organism_lower = organism.lower()
            if "soil" in organism_lower:
                return "Soil"
            elif "sediment" in organism_lower:
                return "Sediment"
        return "Water"

    df['Sampling Substrate'] = df['Organism'].apply(infer_substrate)
    logger.info("Sampling Substrate processing completed.")
    return df


def process_critical_zone(df):
    """处理Critical Zone字段"""
    logger.info("正在处理Critical Zone字段...")

    # 创建新列，如果不存在
    if "Critical Zone" not in df.columns:
        df["Critical Zone"] = ""

    # 定义要检查的字段列表
    fields_to_check = [
        'Sample_title', 'Sample_description', 'BioProject_description',
        'BioProject_name', 'BioProject_title', 'Biome'
    ]

    def determine_critical_zone(row):
        hydrosphere_type = row.get('Hydrosphere Type', '')

        # 如果水文圈类型是Marine
        if hydrosphere_type == 'Marine':
            # 检查是否包含特定关键字
            marine_keywords = ['marsh', 'estuary', 'mangrove']

            for field in fields_to_check:
                value = row.get(field, '')
                if pd.notna(value) and isinstance(value, str):
                    if any(keyword in value.lower() for keyword in marine_keywords):
                        return 'IN'

            return 'OUT'

        # 如果水文圈类型是Inland water
        elif hydrosphere_type == 'Inland water':
            water_types = ["Rivers", "Inland Seas", "Wetlands"]

            water_body_type_col = row.get('Water Body Type', '')

            if water_body_type_col in water_types:
                # 检查是否包含特定关键字
                inland_keywords = ['nearshore', 'estuary', 'coastal', 'delta']

                for field in fields_to_check:
                    value = row.get(field, '')
                    if pd.notna(value) and isinstance(value, str):
                        if any(keyword in value.lower() for keyword in inland_keywords):
                            return 'IN'

            return 'OUT'

        # 如果水文圈类型是其他值或未知
        return 'Unclassfied'

    # 应用函数处理每一行
    df['Critical Zone'] = df.apply(determine_critical_zone, axis=1)
    logger.info("Critical Zone字段处理完成")

    return df


def process_data():
    """处理Excel文件中的数据"""
    logger.info(f"开始处理数据文件: {EXCEL_FILE_PATH}")

    # 检查Excel文件是否存在
    if not os.path.exists(EXCEL_FILE_PATH):
        logger.error(f"文件不存在: {EXCEL_FILE_PATH}")
        return

    # 加载水体治理字典 - 高优先级规则
    water_body_dict = load_water_body_dict()
    
    # 加载地理数据
    continents_gdf, ocean_gdf = load_geospatial_data()

    # 读取Excel文件
    logger.info("读取Excel文件...")
    df = pd.read_excel(EXCEL_FILE_PATH)
    logger.info(f"Excel文件已加载，共{len(df)}行")

    # 创建新列，如果不存在
    if "Water Body Type (By Geographic)" not in df.columns:
        df["Water Body Type (By Geographic)"] = ""

    if "Hydrosphere Type" not in df.columns:
        df["Hydrosphere Type"] = ""

    if "Water Body Type (By Classification)" not in df.columns:
        df["Water Body Type (By Classification)"] = ""
    
    # 处理每一行
    total_rows = len(df)
    logger.info(f"开始处理{total_rows}行数据...")

    # 应用高优先级水体治理规则
    logger.info("应用高优先级水体治理规则...")
    
    def apply_high_priority_rule(row):
        water_body_name = row.get('Water Body Name', '')
        
        # 如果Water Body Name不是unspecified且在字典中存在，则使用字典值填充
        if pd.notna(water_body_name) and water_body_name != 'unspecified' and water_body_name in water_body_dict:
            dict_entry = water_body_dict[water_body_name]
            return {
                'use_dict': True,
                'Hydrosphere Type': dict_entry['Hydrosphere Type'],
                'Water Body Type (By Classification)': dict_entry['Water Body Type (By Classification)'],
                'Water Body Type (By Geographic)': dict_entry['Water Body Type (By Geographic)']
            }
        else:
            # 如果在字典中找不到，返回标记，表示使用原有规则处理
            return {'use_dict': False}
    
    # 应用高优先级规则
    df['high_priority_result'] = df.apply(apply_high_priority_rule, axis=1)
    
    # 从高优先级结果中填充相关字段
    for idx, row in df.iterrows():
        if row['high_priority_result']['use_dict']:
            df.at[idx, 'Hydrosphere Type'] = row['high_priority_result']['Hydrosphere Type']
            df.at[idx, 'Water Body Type (By Classification)'] = row['high_priority_result']['Water Body Type (By Classification)']
            df.at[idx, 'Water Body Type (By Geographic)'] = row['high_priority_result']['Water Body Type (By Geographic)']
    
    # 删除临时列
    df.drop(columns=['high_priority_result'], inplace=True)
    
    # 对于没有使用字典的数据，继续使用原有规则处理
    # 获取尚未处理的行（在字典中未找到匹配项的行）
    mask_not_processed = df['Hydrosphere Type'].isna() | (df['Hydrosphere Type'] == '')
    
    logger.info(f"高优先级规则应用完成，{total_rows - sum(mask_not_processed)}行使用了字典数据，{sum(mask_not_processed)}行使用原有规则")
    
    # 以下是原有逻辑，只处理未被字典处理的行
    
    # 处理Hydrosphere Type字段
    logger.info("正在处理Hydrosphere Type字段...")
    
    def process_hydrosphere_type(row):
        # 如果已经通过字典处理过，则跳过
        if pd.notna(row['Hydrosphere Type']) and row['Hydrosphere Type'] != '':
            return row['Hydrosphere Type']
        
        # 否则使用原有逻辑
        return determine_hydrosphere_type(
            row.get("Water Body Type", ""),
            row.get("Water Body Name", ""),
            row.get("Biome", "")
        )
    
    df["Hydrosphere Type"] = df.apply(process_hydrosphere_type, axis=1)

    # 处理Water Body Type (By Geographic)字段
    def process_geographic_type(row):
        # 如果已经通过字典处理过，则跳过
        if pd.notna(row['Water Body Type (By Geographic)']) and row['Water Body Type (By Geographic)'] != '':
            return row['Water Body Type (By Geographic)']
            
        lat = row.get('Lat')
        lon = row.get('Lon')
        lat_lon = row.get('Lat_lon', "")

        # 如果有明确的Lat和Lon字段，使用它们
        if not pd.isna(lat) and not pd.isna(lon):
            return get_location_type_by_hydrosphere(lat, lon, row.get("Hydrosphere Type", ""), continents_gdf,
                                                    ocean_gdf)

        # 如果有Lat_lon字段且不为空，尝试解析
        elif not pd.isna(lat_lon) and lat_lon and lat_lon.lower() != "无":
            try:
                # 假设Lat_lon格式为"lat,lon"
                parts = lat_lon.split(',')
                if len(parts) == 2:
                    lat = float(parts[0].strip())
                    lon = float(parts[1].strip())
                    return get_location_type_by_hydrosphere(lat, lon, row.get("Hydrosphere Type", ""), continents_gdf,
                                                            ocean_gdf)
            except Exception as e:
                logger.warning(f"无法解析Lat_lon: {lat_lon}, 错误: {str(e)}")

        return "Unclassfied"

    logger.info("正在处理Water Body Type (By Geographic)字段...")
    df["Water Body Type (By Geographic)"] = df.apply(process_geographic_type, axis=1)

    # 处理特殊情况：Hydrosphere Type是Marine，Water Body Type (By Geographic)是9大洋之一，且Water Body Type是Inland Seas
    # 则把Water Body Type改为Water Body Type (By Geographic)列的值。
    logger.info("处理特殊情况：Marine和Inland Seas...")
    oceans = ["Arctic Ocean", "North Atlantic", "South Atlantic", "Indian Ocean",
              "North Pacific", "South Pacific", "Southern Ocean", "Mediterranean Sea", "Baltic Sea"]

    def update_water_body_type(row):
        if (row['Hydrosphere Type'] == 'Marine' and
                row['Water Body Type (By Geographic)'] in oceans and
                row.get('Water Body Type') == 'Inland Seas'):
            return row['Water Body Type (By Geographic)']
        return row.get('Water Body Type', '')

    # 更新Water Body Type字段
    df['Water Body Type'] = df.apply(update_water_body_type, axis=1)
    logger.info("特殊情况处理完成")

    # 处理Water Body Type (By Classification)字段
    logger.info("正在处理Water Body Type (By Classification)字段...")
    
    def process_classification(row):
        # 如果已经通过字典处理过，则跳过
        if pd.notna(row['Water Body Type (By Classification)']) and row['Water Body Type (By Classification)'] != '':
            return row['Water Body Type (By Classification)']
            
        # 否则使用原有逻辑
        return determine_classification(
            row["Hydrosphere Type"],
            row.get("Water Body Type", ""),
            row.get("Water Body Name", "")
        )
        
    df["Water Body Type (By Classification)"] = df.apply(process_classification, axis=1)

    # 处理Sampling Substrate
    process_sampling_substrate(df)

    # 处理Critical Zone字段
    process_critical_zone(df)

    # 保存结果
    output_file = os.path.splitext(EXCEL_FILE_PATH)[0] + "_processed.xlsx"
    logger.info(f"保存处理结果到: {output_file}")
    df.to_excel(output_file, index=False)
    logger.info("数据处理完成")


if __name__ == "__main__":
    try:
        process_data()
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}", exc_info=True)
