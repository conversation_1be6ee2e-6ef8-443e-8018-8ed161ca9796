# -*- coding: utf-8 -*-

"""
 水体数据 按照项目合并，精简数据
 Author: 尚尉
 Date: 2024-12-25
 Version: 1.0.0

"""

import pandas as pd

from cust_utils import time_stat, trim_to_none, load_data, write_result_data
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item

# 创建一个自定义的日志记录器
log_file_name = 'water_body_project_merge.log'
logger = init_default_logger(log_file_name)

# 获取当前配置项
curr_process_item = get_curr_process_item()


def process_columns(df: pd.DataFrame, columns=[]):
    if columns is not None and len(columns) > 0:
        # 批量处理列操作：填充NaN值、去除空白
        for column in columns:
            # df[column] = df[column].fillna('').str.strip().replace(['无', '-'], '')
            df[column] = df[column].fillna('').str.strip()
    return df


@time_stat(logger)
def merge_project():
    input_filename = trim_to_none(curr_process_item.get('all_file_path'))
    logger.info(f"开始读取文件: {input_filename}")
    df = load_data(input_filename)

    # 获取列名
    output_columns = df.columns.tolist()

    columns_arr = curr_process_item.get('merge_columns', None)
    # 处理指定列
    df = process_columns(df, columns_arr)

    # 提取 BioprojectID 无效数据
    invalid_val = ['-', '无', '']
    id_col = trim_to_none(curr_process_item.get('merge_id_column', None))
    if id_col:
        df_special = df[df['BioprojectID'].isin(invalid_val)]

        # 过滤掉 BioprojectID 无效的行
        df_valid = df[~df['BioprojectID'].isin(invalid_val)]
    else:
        df_special = None
        df_valid = pd

    # 去重：按照指定列去重
    df_unique = df_valid.drop_duplicates(subset=columns_arr, keep='first')

    # 合并特殊数据和去重的数据，确保不会引入重复数据
    if df_special is not None:
        df_unique_final = pd.concat([df_special, df_unique], ignore_index=True).drop_duplicates(ignore_index=True)
    else:
        df_unique_final = df_unique.drop_duplicates(ignore_index=True)

    # 将过滤后的数据写入新的CSV文件
    output_file = write_result_data(df_unique_final, input_filename, 'merge', output_columns)
    logger.info(f"处理后的数据已保存到 {output_file}")


if __name__ == '__main__':
    merge_project()
