# -*- coding: utf-8 -*-

"""
Excel名称列读取工具
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import configparser
import copy
import os
import re
from time import sleep

import pandas as pd
import requests
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import time_stat, trim_to_none
from custom_log_config import init_default_logger

# 初始化日志
logger = init_default_logger('find_pmid_by_title.log')

f_path = 'data/水圈项目&论文.xlsx'

# 加载配置文件
config = configparser.ConfigParser()
# 读取配置文件
config.read('config/config.ini', encoding='utf-8')

# 访问MongoDB配置
mongo_uri = config['mongo']['uri']
mongo_db_name = config['mongo']['db_name']
mongo_collection_name = config['mongo']['collection_name']
# 访问API配置
base_api_url = config['api']['url']
api_url = base_api_url + '/article/findByTitle.do'
api_token = config['api']['token']

HEADERS = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36",
}

stat_info = {
    'total': 0,
    'not_found': 0,
    'insert': 0,
    'exist': 0,
    'failed': 0,
}
stat_info2 = copy.deepcopy(stat_info)

not_found_titles = []
title_and_pmid = {}


def add_stat(stat_obj: dict[str, int], key: str, value: int = 1):
    """增加统计信息"""
    if key in stat_obj:
        stat_obj[key] += value
    else:
        stat_obj[key] = value


def send_request_and_store_to_mongodb(title_query_count, _session: requests.Session, title: str, original_title: str,
                                      collection: Collection,
                                      stat_obj: dict[str, int]) -> None:
    """发送GET请求并将返回的JSON数据存储到MongoDB"""
    try:
        # 更新title的递归次数
        if original_title in title_query_count:
            title_query_count[original_title] += 1
        else:
            title_query_count[original_title] = 1

        sleep(0.5)
        # 根据title或者title+.判断MongoDB库中文章是否存在
        title_query = {
            "$or": [
                {"title": {"$regex": re.escape(title), "$options": "i"}},
            ]
        }
        if title.endswith('.'):
            # title_query["$or"].append({"title": title[:-1]})
            title_query["$or"].append({"title": {"$regex": re.escape(title[:-1]), "$options": "i"}})
        else:
            # title_query["$or"].append({"title": f'{title}.'})
            title_query["$or"].append({"title": {"$regex": re.escape(f'{title}.'), "$options": "i"}})

        db_item = collection.find_one(title_query)
        if db_item:
            title_and_pmid[original_title] = db_item.get('pmid')
            logger.info(f'已存在标题: {title}')
            add_stat(stat_obj, 'exist')
            return
        # 发送GET请求
        # response = _session.get(api_url + title, timeout=50)
        params = {
            'token': api_token,
            'title': title,
        }
        response = _session.request("GET", api_url, params=params, timeout=50)
        response.raise_for_status()  # 检查请求是否成功
        json_data = response.json()
        status = json_data.get('status', '')
        msg = json_data.get('msg', '')
        articles = json_data.get('articles', [])
        if status == 'success':
            if articles and len(articles) > 0:
                for article in articles:
                    pmid = int(article.get('pmid'))
                    title_and_pmid[original_title] = pmid
                    # 判断是否已经存在该pmid
                    if collection.find_one({'pmid': pmid}):
                        logger.info(f'已存在pmid: {pmid}，标题: {title}')
                        add_stat(stat_obj, 'exist')
                        continue
                    # 插入数据
                    collection.insert_one(article)
                    add_stat(stat_obj, 'insert')
                logger.info(f'成功处理标题: {title}')
            else:
                # 未查询到文章数据时
                if (not title.endswith('.')) and title_query_count[original_title] < 2:
                    # 如果title不以.结尾，则在末尾添加.后，重新发送请求
                    new_title = title + '.'
                    send_request_and_store_to_mongodb(title_query_count, _session, new_title, original_title,
                                                      collection, stat_obj)
                elif title.endswith('.') and title_query_count[original_title] < 2:
                    # 如果title以.结尾，则去除末尾.后，重新发送请求
                    new_title = title[:-1]
                    send_request_and_store_to_mongodb(title_query_count, _session, new_title, original_title,
                                                      collection, stat_obj)
                else:
                    if original_title not in not_found_titles:
                        not_found_titles.append(original_title)

                    logger.info(f'未找到标题: {title}')
                    add_stat(stat_obj, 'not_found')
        else:
            logger.error(f'api查询出错: {title}，错误信息: {msg}')
            add_stat(stat_obj, 'failed')

    except requests.exceptions.RequestException as e:
        logger.error(f'请求失败: {title}，错误信息: {str(e)}')
        add_stat(stat_obj, 'failed')
    except Exception as e:
        logger.error(f'处理标题失败: {title}，错误信息: {str(e)}')
        add_stat(stat_obj, 'failed')


def find_by_plosp(filepath: str, collection: Collection):
    _session = None
    try:
        # 检查文件是否存在
        if not os.path.exists(filepath):
            logger.error(f'文件不存在: {filepath}')
            return

        # df = pd.read_excel(filepath, usecols=['名称'], engine='openpyxl')
        # 读取Excel文件
        df = pd.read_excel(filepath, engine='openpyxl')
        if df.empty or '名称' not in df.columns:
            logger.error(f'文件缺少"名称"列或为空: {filepath}')
            return

        # 打印文件路径和列内容
        logger.info(f'处理文件：{filepath}')
        _session = requests.session()
        _session.headers = HEADERS
        # data_to_insert = []

        title_query_count = {}
        for index, row in df.iterrows():
            name = trim_to_none(row['名称'])
            if name:
                print(f'行{index + 1}: {name}')
                add_stat(stat_info, 'total')
                # 调用函数发送请求并存储到MongoDB
                send_request_and_store_to_mongodb(title_query_count, _session, name, name, collection, stat_info)

        logger.info(f'未找到的标题数量: {len(not_found_titles)}')
        if len(not_found_titles) > 0:
            title_query_count = {}
            for not_found_title in not_found_titles:
                if '’' in not_found_title:
                    new_title = not_found_title.replace('’', '\'')
                    add_stat(stat_info2, 'total')
                    send_request_and_store_to_mongodb(title_query_count, _session, new_title, not_found_title,
                                                      collection, stat_info2)

        for index, row in df.iterrows():
            name = trim_to_none(row['名称'])
            if name:
                pmid = title_and_pmid.get(name, None)
                df.at[index, 'pmid'] = pmid

        df.to_excel(filepath, index=False, engine='openpyxl')
        logger.info(f'更新后的文件已保存到: {filepath}')
    except KeyError:
        logger.error(f'文件缺少"名称"列: {filepath}')
    except Exception as e:
        logger.error(f'处理文件失败: {filepath}，错误信息: {str(e)}')
    finally:
        if _session:
            _session.close()


@time_stat(logger)
def process_all_files():
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[mongo_collection_name]
    try:
        # 检查索引是否存在
        existing_indexes = collection.index_information()
        custom_index_name = "unique_pmid_index"  # 自定义索引名称
        if custom_index_name not in existing_indexes:
            # 创建唯一性索引并指定名称
            collection.create_index([('pmid', 1)], unique=True, name=custom_index_name)
            print(f"唯一性索引已成功添加到集合 {mongo_collection_name} 的 'pmid' 字段，索引名称: {custom_index_name}")
        else:
            print(f"唯一性索引已存在，无需重复创建")

        find_by_plosp(f_path, collection)
        logger.info(f'统计信息: {stat_info}，改换“’”的统计信息: {stat_info2}')
    finally:
        client.close()


if __name__ == '__main__':
    logger.info('开始处理Excel文件...')
    process_all_files()
    logger.info('处理完成')
