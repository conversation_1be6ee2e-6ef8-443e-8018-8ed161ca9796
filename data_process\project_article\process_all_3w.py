# -*- coding: utf-8 -*-

"""
Excel数据回填
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import os
import shutil

import pandas as pd
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import str_val, time_stat
from custom_log_config import init_default_logger
from fill_mash_excel import to_int_num
from fill_more_mash_excel import final_300_filepath
from find_pmid_by_title import mongo_uri, mongo_db_name
from process_supp_table_publication import supp_collection_name, all_result_filepath

download_dir = "data/downloads"

# 初始化日志
logger = init_default_logger('process_all_3w.log')


def rename_pdf():
    new_filepath = final_300_filepath
    # 检查文件是否存在
    if not os.path.exists(new_filepath):
        logger.error(f'文件不存在: {new_filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(new_filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {new_filepath}')
        return
    # 打印文件路径和列内容
    logger.info(f'处理文件：{new_filepath}')

    for index, row in df.iterrows():
        has_pdf = str_val(row, '是否存在PDF')
        has_pdf = has_pdf.upper() if has_pdf else None

        project_id = to_int_num(str_val(row, '项目编号'))
        order_num = to_int_num(str_val(row, '序号'))
        if project_id is None or order_num is None:
            raise ValueError('项目编号或序号为空')

        new_path_name = f'{download_dir}/{project_id}.{str(order_num).zfill(2)}.pdf'
        if os.path.exists(new_path_name):
            pmid = to_int_num(str_val(row, 'pmid'))
            if pmid is None:
                logger.error(f'pmid不存在，project_id：{project_id}，order_num：{order_num}')
                continue

            logger.info(f'重命名，project_id：{project_id}，order_num：{order_num}')
            file_path = f'{download_dir}/{pmid}.pdf'
            # 文件已移动并重命名
            shutil.move(new_path_name, file_path)

        else:
            logger.info(f'是否存在PDF：{has_pdf}')


def update_source(collection: Collection, pmid, source):
    collection.update_one({"pmid": pmid}, {"$set": {"source": source}})


@time_stat(logger)
def add_col():
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[supp_collection_name]
    try:
        query = {"$or": [{"node_ids": {"$exists": True}}, {"ncbi_sra_ids": {"$exists": True}}]}
        projection = {"pmid": 1, "node_ids": 1, "ncbi_sra_ids": 1, "_id": 0}
        results = collection.find(query, projection)
        doc_map = {}
        for doc in results:
            pmid = doc.get('pmid')
            doc_map[pmid] = doc

        df = pd.read_excel(all_result_filepath, engine='openpyxl')
        for index, row in df.iterrows():
            if index % 100 == 0:
                logger.info(f'处理进度：{index + 1}')
            pmid = str_val(row, 'PMID')
            source = str_val(row, 'Source')
            node_ids = None
            ncbi_sra_ids = None
            if pmid:
                pmid = to_int_num(pmid)
                if pmid is not None:
                    # 更新Source
                    update_source(collection, pmid, source)

                    # 更新node_ids和ncbi_sra_ids
                    item = doc_map.get(pmid, None)
                    if item:
                        val = item.get('node_ids', None)
                        if val:
                            node_ids = ",".join(val)

                        val = item.get('ncbi_sra_ids', None)
                        if val:
                            ncbi_sra_ids = ",".join(val)

            df.at[index, 'node_ids'] = node_ids
            df.at[index, 'ncbi_sra_ids'] = ncbi_sra_ids

        df.to_excel(all_result_filepath, index=False, engine='openpyxl')
        logger.info(f'处理完成，文件路径：{all_result_filepath}')
    finally:
        client.close()


if __name__ == '__main__':
    add_col()
