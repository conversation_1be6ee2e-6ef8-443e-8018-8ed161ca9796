import pandas as pd

# 读取Excel文件
excel1 = "data/MASH_NODE_Samples_result.xlsx"  # 第1个Excel文件路径
excel2 = "data/MASH_AntNest_Samples_250102.xlsx"  # 第2个Excel文件路径

# 加载数据
df1 = pd.read_excel(excel1)
df2 = pd.read_excel(excel2)

# 获取两个文件的公共列
common_columns = [col for col in df2.columns if col in df1.columns]

# 筛选第2个Excel中存在于第1个Excel中的列
df2_filtered = df2[common_columns]

# 添加Source Type列
df1["Source Type"] = "NODE"
df2_filtered["Source Type"] = "AntNest"

# 合并两个数据框
merged_df = pd.concat([df1, df2_filtered], ignore_index=True)

# 保存合并后的数据
merged_df.to_excel("MASH_Samples_merged.xlsx", index=False)

print("合并完成，结果已保存为 MASH_Samples_merged.xlsx")
