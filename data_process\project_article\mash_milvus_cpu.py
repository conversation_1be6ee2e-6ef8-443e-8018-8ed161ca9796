import json
import time

from langchain_text_splitters import RecursiveCharacterTextSplitter
from pymilvus import connections, utility, FieldSchema, CollectionSchema, DataType, Collection
from pymongo import MongoClient
from sentence_transformers import SentenceTransformer


def load_metadata_from_mongodb(mongo_uri, db_name, collection_name):
    """从MongoDB加载元数据"""
    client = MongoClient(mongo_uri)
    db = client[db_name]
    collection = db[collection_name]
    # query = {}
    metadata_dict = {}
    try:
        for doc in collection.find():
            pmid = str(doc["pmid"])

            # 获取最新年份的影响因子
            latest_if = 0.0
            latest_if_year = 0
            if "year_ifs" in doc and doc["year_ifs"]:
                # 按年份降序排序，取最新的影响因子
                sorted_ifs = sorted(doc["year_ifs"], key=lambda x: x["year"], reverse=True)
                latest_if = sorted_ifs[0]["impact_factor"]
                latest_if_year = sorted_ifs[0]["year"]

            # 获取所有学科分类的中文名称
            section_names_cn = []
            if "zky_sections" in doc and doc["zky_sections"]:
                section_names_cn = [section.get("name_cn", "") for section in doc["zky_sections"] if
                                    "name_cn" in section]

            page_content = ''
            pdf_doc_content = doc.get('pdf_doc_content', None)
            if pdf_doc_content:
                body_list = pdf_doc_content.get('body', None)
                if body_list:
                    page_content = '\n'.join(body_list)

            metadata_dict[pmid] = {
                "title": doc.get("title", ""),
                "doi": str(doc.get("doi", "")) if doc.get("doi") else "",
                "year": int(doc.get("year", 0)) if doc.get("year") else 0,
                "journal": doc.get("journalTitle", ""),
                "impact_factor": float(latest_if),
                "impact_factor_year": int(latest_if_year),
                "section_names_cn": "|".join(section_names_cn) if section_names_cn else "",
                "page_content": page_content,
            }
    finally:
        client.close()
    return metadata_dict


def process_documents(model_path, collection_name, failed_documents_file, metadata_dict):
    """处理文档并存入 Milvus"""
    # 加载模型到 CPU
    model = SentenceTransformer(model_path, trust_remote_code=True, device='cpu')

    # 连接 Milvus
    connections.connect(host='**************', port='19530')

    # 创建 Collection
    if utility.has_collection(collection_name):
        utility.drop_collection(collection_name)

    vector_field_name = 'vector'
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
        FieldSchema(name=vector_field_name, dtype=DataType.FLOAT_VECTOR, dim=384),
        FieldSchema(name="child_text", dtype=DataType.VARCHAR, max_length=4096),
        FieldSchema(name="parent_text", dtype=DataType.VARCHAR, max_length=4096),
        FieldSchema(name="pmid", dtype=DataType.VARCHAR, max_length=4096),
        FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=4096),
        FieldSchema(name="doi", dtype=DataType.VARCHAR, max_length=4096),
        FieldSchema(name="year", dtype=DataType.INT64),
        FieldSchema(name="journal", dtype=DataType.VARCHAR, max_length=4096),
        FieldSchema(name="impact_factor", dtype=DataType.FLOAT),
        FieldSchema(name="impact_factor_year", dtype=DataType.INT16),
        FieldSchema(name="section_names_cn", dtype=DataType.VARCHAR, max_length=4096),  # 新增字段
        FieldSchema(name="chunk_id", dtype=DataType.INT64)
    ]
    schema = CollectionSchema(fields, description="Document embeddings with metadata")
    collection = Collection(name=collection_name, schema=schema)

    # 文档分割器
    parent_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=200)
    child_splitter = RecursiveCharacterTextSplitter(chunk_size=400, chunk_overlap=100)

    failed_documents = []
    total = len(metadata_dict)
    idx = 0
    for pmid in metadata_dict:
        idx += 1
        if idx % 200 == 0:
            rounded_value = round(((idx + 0.0) / total) * 100, 2)
            print(f"Processed {idx}/{total} documents, {rounded_value}% completed")

        # 获取 PMID
        # pmid = os.path.basename(doc.metadata['source']).replace('.txt', '')
        # filename = os.path.basename(doc.metadata['source'])
        # pmid = filename.split('.')[0]  # 获取第一个"."之前的部分作为PMID

        # 获取元数据
        doc_metadata = metadata_dict.get(pmid, {
            "title": "",
            "doi": "",
            "year": 0,
            "journal": "",
            "impact_factor": 0.0,
            'impact_factor_year': 0,
            "section_names_cn": "",
        })

        # 处理文档
        parent_docs = parent_splitter.split_text(doc_metadata.get("page_content", ""))
        child_docs_with_parents = []  # 存储 (child_text, parent_text)

        for parent in parent_docs:
            child_docs = child_splitter.split_text(parent)
            for child in child_docs:
                child_docs_with_parents.append((child, parent))  # 记录子块和父块

        # 去重
        seen_texts = set()
        unique_child_docs = []
        for child_text, parent_text in child_docs_with_parents:
            if child_text not in seen_texts:
                unique_child_docs.append((child_text, parent_text))
                seen_texts.add(child_text)

        # 生成 embeddings
        embeddings = model.encode([child[0] for child in unique_child_docs], normalize_embeddings=True)

        # 准备插入数据
        pmid_int = int(pmid) if pmid.isdigit() else 0

        batch_entities = []
        batch_size = 10

        for i, (embedding, (child_text, parent_text)) in enumerate(zip(embeddings, unique_child_docs)):
            embedding_list = embedding.tolist()

            if len(embedding_list) != 384:
                print(f"Warning: Embedding size mismatch for PMID {pmid}, expected 384 but got {len(embedding_list)}")

            entity = {
                "id": pmid_int * 10000 + (i + 1),
                vector_field_name: embedding_list,
                "child_text": child_text,
                "parent_text": parent_text,
                "pmid": pmid,
                "title": doc_metadata["title"],
                "doi": doc_metadata["doi"],
                "year": doc_metadata["year"],
                "journal": doc_metadata["journal"],
                "impact_factor": doc_metadata["impact_factor"],
                "impact_factor_year": doc_metadata["impact_factor_year"],
                "section_names_cn": doc_metadata["section_names_cn"],  # 新增字段
                "chunk_id": i + 1
            }
            batch_entities.append(entity)

            # 每 batch_size 条数据插入一次
            if len(batch_entities) >= batch_size:
                try:
                    collection.insert(batch_entities)
                    batch_entities = []  # 清空列表，准备下一批
                except Exception as e:
                    print(f"Failed to insert batch for PMID {pmid}: {e}")
                    failed_documents.append({
                        "pmid": pmid,
                        "error": str(e),
                        "error_entity": batch_entities
                    })

        # 处理最后不足 batch_size 的数据
        if batch_entities:
            try:
                collection.insert(batch_entities)
            except Exception as e:
                print(f"Failed to insert final batch for PMID {pmid}: {e}")
                failed_documents.append({
                    "pmid": pmid,
                    "error": str(e),
                    "error_entity": batch_entities
                })

    # 创建AUTOINDEX索引（使用COSINE度量类型）
    index_params = {
        "index_type": "AUTOINDEX",
        "metric_type": "COSINE",
        "params": {}
    }
    # 为指定字段创建索引
    collection.create_index(
        field_name=vector_field_name,
        index_params=index_params
    )

    print(f"已为集合 {collection_name} 的字段 {vector_field_name} 创建 AUTOINDEX 索引（COSINE）")
    # 保存失败的文档
    if failed_documents:
        with open(failed_documents_file, "w") as f:
            json.dump(failed_documents, f, indent=4)


def main():
    # 配置参数
    docs_dir = './data/top100'
    model_path = r'D:\Temp\mash_data\sentence-transformers\all-MiniLM-L6-v2'
    failed_documents_file = "./mash_doc_milvus_failed.json"
    # milvus_collection_name = "food_Qwen2_1B_instruct_collection_1000documents_with_parent"
    milvus_collection_name = "mash_doc_milvus"

    # 记录程序开始时间
    start_time = time.time()

    from find_pmid_by_title import mongo_uri, mongo_db_name
    from process_supp_table_publication import supp_collection_name
    # 从MongoDB加载元数据
    metadata_dict = load_metadata_from_mongodb(mongo_uri, mongo_db_name, supp_collection_name)

    # 加载所有文档
    # docs = []
    # for filename in os.listdir(docs_dir)[:1000]:
    #     if filename.endswith(".txt"):
    #         file_path = os.path.join(docs_dir, filename)
    #         loader = TextLoader(file_path, encoding='utf-8')
    #         loaded_docs = loader.load()
    #         for doc in loaded_docs:
    #             if doc.page_content.strip():
    #                 docs.append(doc)

    # 处理文档
    process_documents(model_path, milvus_collection_name, failed_documents_file, metadata_dict)

    # 记录程序结束时间
    end_time = time.time()

    # 打印总的处理时间
    print(f"Total time taken: {end_time - start_time:.4f} seconds")


if __name__ == "__main__":
    main()
