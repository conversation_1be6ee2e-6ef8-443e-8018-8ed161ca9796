# NODE 用户端 前端代码

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```


## 使用yarn
### 安装yarn
```sh
npm install -g yarn
```
### 设置yarn镜像源
```sh
yarn config set registry https://registry.npmmirror.com/
```
### 安装依赖
```sh
yarn
```


部署命令
cd /data/nginx/html

rm -rf /data/nginx/html/node

unzip node.zip
