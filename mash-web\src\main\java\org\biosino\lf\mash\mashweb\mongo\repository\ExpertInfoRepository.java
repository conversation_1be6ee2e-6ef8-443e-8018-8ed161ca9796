package org.biosino.lf.mash.mashweb.mongo.repository;

import org.biosino.lf.mash.mashweb.mongo.entity.ExpertInfo;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExpertInfoRepository extends MongoRepository<ExpertInfo, String> {
    List<ExpertInfo> findByLabelsIn(List<String> labels);

}
