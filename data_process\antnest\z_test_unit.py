# 测试工具
# coding: utf-8
import os
import re
from collections import defaultdict

import geopandas as gpd
import matplotlib
from shapely import Point

from cust_utils import trim_val
from custom_log_config import init_default_logger

matplotlib.use('TkAgg')  # 或者其他标准后端，如 'Qt5Agg'
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from shapely.geometry import shape

# 配置日志记录
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_output_path = os.path.join(log_dir, 'app_run.log')

# 创建一个自定义的日志记录器
log_file_name = 'app_run.log'
logger = init_default_logger(log_file_name)


def mash_excel_to_csv():
    excel_file_path = os.path.join('./data', 'MASH-NODE-Samples_merge.xlsx')
    csv_file_path = os.path.join('./data', 'MASH-NODE-Samples_merge.csv')
    excel_to_csv(excel_file_path, csv_file_path)


def merge_arrays(arrays):
    # 使用defaultdict来记录每个元素的出现次数
    count_dict = defaultdict(int)
    # 遍历所有数组，统计每个元素的出现次数
    for array in arrays:
        for element in array:
            count_dict[element] += 1

    # 初始化两个集合，分别存储出现次数大于1的元素和仅出现一次的元素
    multiple_occurrences = set()
    single_occurrence = set()

    # 根据计数结果分类
    for element, count in count_dict.items():
        if count > 1:
            multiple_occurrences.add(element)
        else:
            single_occurrence.add(element)

    return multiple_occurrences, single_occurrence


def test_merge_arrays():
    # 示例数组
    arrays = [
        ["apple", "banana", "cherry"],
        ["banana", "date", "fig"],
        ["grape", "apple", "kiwi"],
        ["banana", "cherry"]
    ]

    # 合并数组
    s1, s2 = merge_arrays(arrays)

    # 输出结果
    print("出现次数大于1的元素:", s1)
    print("仅出现一次的元素:", s2)


def contains_whole_word(text, word):
    # 创建正则表达式模式，使用 \b 表示单词边界
    pattern = r'\b' + re.escape(word) + r'\b'
    # 使用 re.search 查找匹配
    return re.search(pattern, text) is not None


def test_contains_whole_word():
    context_fields = [
        'Geo_loc_name',
        'Sample_title',
        'Sample_description',
        'BioProject_description',
        'BioProject_name',
        'BioProject_title',
        'Biome',
        'Lat_lon',
    ]

    special_strs = [':', ';', '：', '-']
    pattern = '|'.join(re.escape(char) for char in special_strs)

    # 示例用法
    chunk = pd.read_csv(os.path.join('../data', 'test1.csv'))
    iter_rows = chunk.iterrows()
    for index, row in iter_rows:
        name_ = 'Green'
        join = ' _@_ '.join(trim_val(row, field) for field in context_fields)
        join = re.sub(pattern, ' ', join).strip().lower()
        if contains_whole_word(join, name_.lower()):
            print(join)
        else:
            print(f"不包含字典数据：{join}")


def test_replace():
    input_string = '这是一个测试:字符串;包含：特殊字符NA aad32'
    special_strs = [':', ';', '：', 'NA']
    pattern = '|'.join(re.escape(char) for char in special_strs)
    sub = re.sub(pattern, ' ', input_string)
    print(sub)


def excel_to_csv(excel_file, csv_file):
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)

        # 将DataFrame写入CSV文件
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')

        print(f"成功将 {excel_file} 转换为 {csv_file}")
    except Exception as e:
        print(f"转换过程中发生错误: {e}")


def csv_to_excel(csv_file, excel_file):
    try:
        # 读取Excel文件
        df = pd.read_csv(csv_file)

        # 将DataFrame写入CSV文件
        df.to_excel(excel_file, index=False)

        print(f"成功将 {csv_file} 转换为 {excel_file}")
    except Exception as e:
        print(f"转换过程中发生错误: {e}")


def read_and_write_csv(input_file, output_file, nrows=5300):
    try:
        # 读取CSV文件的前1000行
        df = pd.read_csv(input_file, nrows=nrows)

        # 将DataFrame写入新的CSV文件
        df.to_csv(output_file, index=False, encoding='utf-8-sig')

        print(f"成功将 {input_file} 的前 {nrows} 行写入 {output_file}")
    except Exception as e:
        print(f"读取或写入过程中发生错误: {e}")


def sub_csv():
    csv_file_path = os.path.join('../data', 'MASH_28916_temp.csv')
    output_file_path = os.path.join('../data', 'MASH_28916_temp_sub.csv')
    read_and_write_csv(csv_file_path, output_file_path)


def test_count():
    same_name = defaultdict(int)
    for i in range(1, 10):
        same_name['t123'] += 1

    item = same_name['t123']
    print(item)
    print(item == 9)


def add_ext_col():
    # 读取CSV文件
    mash_all_excel = os.path.join('../data', 'mash_all_samples_sub.csv')
    chunk_size = 50
    # 如果文件很大，可以使用chunksize参数分块读取
    chunks = pd.read_csv(mash_all_excel, chunksize=chunk_size)

    output_file = os.path.join('../data', 'path_to_output_file.csv')
    chunk_index = 0
    for chunk in chunks:
        for index, row in chunk.iterrows():
            # 在每个row中添加新的列random_num,值为随机数
            chunk.at[index, 'random_num'] = np.random.rand()  # 注意：pd.np在新版本中已被弃用，建议使用numpy

        # 将chunk追加到文件中
        if chunk_index == 0:
            chunk.to_csv(output_file, index=False)
        else:
            # 追加模式，不包含标题
            chunk.to_csv(output_file, mode='a', header=False, index=False)

        chunk_index += 1


def test_lat_lon():
    # 定义正方形的四个顶点坐标（这里以原点为中心，边长为2举例）
    # square_coords = [(-1, -1), (-1, 1), (1, 1), (1, -1)]
    # 创建多边形对象（也就是我们的正方形）
    # square = Polygon(square_coords)
    # print(square)

    mash_input_file = os.path.join('data', 'MASH_28916_temp_sub.csv')
    input_basename = os.path.basename(mash_input_file)
    name_without_ext, _ext = os.path.splitext(input_basename)
    print(name_without_ext)
    print(_ext)

    feature = {
        'type': 'Feature',
        'geometry': {
            'type': 'Polygon',
            'coordinates': [
                [
                    [-1, -1],
                    [-1, 1],
                    [1, 1],
                    [1, -1]
                ]
            ]
        }
    }

    # 打印 feature 以验证其内容
    # print(feature)

    # 创建多边形对象
    square = shape(feature['geometry'])

    lat_lon = 'z34.297900, 132.917100'
    if lat_lon != '' and ',' in lat_lon:
        index = lat_lon.index(',')
        try:
            lat_str = lat_lon[:index].strip()
            lon_str = lat_lon[index + 1:].strip()
            latitude = float(lat_str)
            longitude = float(lon_str)
            print(f"Latitude: ", latitude)
            print(f"Longitude:", longitude)
        except Exception as e:
            # logger.exception(f'经纬度查询范围出错：{e}')
            logger.error(f'经纬度查询范围出错：{e}')
            # print(f"转换失败: {e}")


def show_shape(square):
    # 打印 polygon 以验证其内容 POLYGON ((-1 -1, -1 1, 1 1, 1 -1, -1 -1))
    print(square)

    # 获取多边形的外部坐标序列用于绘图
    x, y = square.exterior.xy

    # 使用matplotlib进行可视化
    plt.plot(x, y)
    # 设置图形为等比例显示，避免图形变形
    plt.axis('equal')

    # 添加标题
    plt.title("Square Visualization")
    # 添加x轴标签
    plt.xlabel("X-axis")
    # 添加y轴标签
    plt.ylabel("Y-axis")

    # 显示图形
    plt.show()


def show_img():
    # 假设 feature 是一个包含 GeoJSON 格式几何信息的字典
    feature = {
        'type': 'Feature',
        'geometry': {
            'type': 'Polygon',
            'coordinates': [
                [
                    [-1, -1],
                    [-1, 1],
                    [1, 1],
                    [1, -1]
                ]
            ]
        }
    }

    # 打印 feature 以验证其内容
    print(feature)

    # 创建多边形对象
    polygon = shape(feature['geometry'])
    show_shape(polygon)


def find_nearest_region_by_gpd():
    geojson_file = "../geojson/ne_10m_rivers_lake_centerlines.geojson"
    latitude, longitude = 30.128200, 122.745990
    # 注意经纬度的顺序：经度在前，纬度在后
    # 加载河流 GeoJSON 数据
    rivers_gdf = gpd.read_file(geojson_file)

    print(f'rivers_gdf.crs: {rivers_gdf.crs}')
    # 将河流数据转换为投影坐标系，例如 EPSG:3857
    # rivers_gdf = rivers_gdf.to_crs(epsg=3857)
    # print(f'rivers_gdf.crs: {rivers_gdf.crs}')

    # 确保河流数据的坐标参考系是 WGS 84 (EPSG:4326)
    if rivers_gdf.crs != "EPSG:4326":
        rivers_gdf = rivers_gdf.to_crs("EPSG:4326")

    # 创建点对象
    point = Point(longitude, latitude)
    # point = gpd.GeoSeries([Point(longitude, latitude)], crs="EPSG:4326")
    # 将 point 也转换为相同的投影坐标系
    # point = point.to_crs(epsg=3857)

    # 计算点到每条河流的距离
    rivers_gdf["distance"] = rivers_gdf.geometry.distance(point)

    # 找到距离最近的河流
    nearest_river = rivers_gdf.loc[rivers_gdf["distance"].idxmin()]
    print(f'nearest_river: {nearest_river}')

    distance_threshold = 5000
    # 判断距离是否小于阈值
    if nearest_river["distance"] <= distance_threshold:
        return {
            "river_name": nearest_river.get("name", "Unknown"),  # 返回河流名称或其他标识字段
            "dissolve": nearest_river["dissolve"],
            "distance": nearest_river["distance"],
            "geometry": nearest_river.geometry
        }
    else:
        return {
            "message": "没有找到在阈值范围内的河流",
            "distance": nearest_river["distance"]
        }


def filter_result_file():
    input_file = os.path.join('../data/test', 'MASH_448_plus.csv')

    input_basename = os.path.basename(input_file)
    name_without_ext, _ext = os.path.splitext(input_basename)

    input_dir = os.path.dirname(input_file)
    output_file = os.path.join(input_dir, f'{name_without_ext}_final{_ext}')

    # 距离超过0.3度的数据为无效数据，清空对应经纬度检索字段
    threshold_val = 0.3
    logger.info(f"开始清理无效数据，超过阈值：{threshold_val}")

    chunk_size = 100  # 每次读取的行数，可以根据实际情况调整
    try:
        # 使用chunksize分批次读取CSV文件
        csv_chunks = pd.read_csv(input_file, chunksize=chunk_size)
        chunk_index = 0

        clean_columns = ['name_by_lat_lon', 'search_type_by_lat_lon', 'search_type_by_lat_lon', 'source_by_lat_lon']
        for chunk in csv_chunks:
            # 处理每个chunk
            chunk.loc[chunk['min_distance_degree'] > threshold_val, clean_columns] = ''

            # 将chunk追加新的CSV文件
            if chunk_index == 0:
                chunk.to_csv(output_file, index=False, encoding='utf-8-sig')
            else:
                # 追加模式，不包含标题
                chunk.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8')

            chunk_index += 1

        logger.info(f"成功将过滤后的数据写入 {output_file}")
    except Exception as e:
        logger.error(f"清理无效数据出错: {e}")


def replace_he(val_str: str):
    if val_str is None:
        return None
    return re.sub(r'\b(\w+)he\b', r'\1', val_str, flags=re.IGNORECASE)


if __name__ == '__main__':
    mash_excel_to_csv()
    # filter_result_file()
    # test_merge_arrays()
    # test_contains_whole_word()
    # test_replace()
    # sub_csv()
    # add_ext_col()
    # show_img()
    # test_lat_lon()
    # by_gpd = find_nearest_region_by_gpd()
    # print(by_gpd)
    # he = replace_he('China: LiaoHe hei he RiverHE')
    # print(he)
    # min_distance = float('inf')
    # print(f's:{min_distance <= 0}')
