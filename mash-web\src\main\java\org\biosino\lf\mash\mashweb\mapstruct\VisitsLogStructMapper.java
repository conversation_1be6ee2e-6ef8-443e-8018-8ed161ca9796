package org.biosino.lf.mash.mashweb.mapstruct;

import org.biosino.lf.mash.mashweb.dto.VisitsLogDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.VisitsLog;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Mapper
public interface VisitsLogStructMapper extends CommonMapper {
    VisitsLogStructMapper INSTANCE = Mappers.getMapper(VisitsLogStructMapper.class);

    void copyToDb(VisitsLogDTO source, @MappingTarget VisitsLog target);

}
