package org.biosino.lf.mash.mashweb.enums;

import lombok.Getter;
import org.biosino.lf.mash.mashweb.vo.SelectVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 文献检索下拉框
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Getter
public enum ScArcQueryItemEnum {
    ai("AI semantic search"),
    title("Title"),
    pmid("PMID"),
    doi("DOI"),
    journal("Journal"),
    author("Author"),
    bioId("BioProject ID/Sample ID/Run ID");


    private final String text;

    ScArcQueryItemEnum(String text) {
        this.text = text;
    }

    public static List<SelectVO> getSelectVOList() {
        final ScArcQueryItemEnum[] values = values();
        final List<SelectVO> list = new ArrayList<>();
        for (ScArcQueryItemEnum value : values) {
            final SelectVO vo = new SelectVO();
            vo.setLabel(value.getText());
            vo.setValue(value.name());
            vo.setSelected(value == ai);
            list.add(vo);
        }
        return list;
    }

    public static Optional<ScArcQueryItemEnum> findByName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        final ScArcQueryItemEnum[] values = values();
        for (ScArcQueryItemEnum value : values) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

}
