package org.biosino.lf.mash.mashweb.mapstruct;

import org.biosino.lf.mash.mashweb.dto.ExpertQaDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.ExpertQa;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Mapper
public interface ExpertQaStructMapper extends CommonMapper {
    ExpertQaStructMapper INSTANCE = Mappers.getMapper(ExpertQaStructMapper.class);

    void copyToDb(ExpertQaDTO source, @MappingTarget ExpertQa target);

    ExpertQa copyDb(ExpertQa target);
}
