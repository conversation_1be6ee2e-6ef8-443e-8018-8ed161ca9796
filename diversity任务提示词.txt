请帮我完成 页面 biogeography 功能，功能的接口我已经在 controller里面写好了，请你先阅读整个原型页面的逻辑，然后再进行修改。
我简单介绍一下逻辑：
1. 下方有四个按钮，用于切换不同4域6分类，选中不同的域和分类，通过请求/getSpeciesName 拿到下拉框的选项数据，选择框最多选10个
3. 然后和下方的Data selected 中填写的数据一起提交到/biogeography接口，提交参数对应DTO是BiogeographyCreateDTO
4. 当任务提交后，等待任务完成，任务完成后会返回一个任务id
5. 上方地图的下拉框中的内容是同个/getSpeciesNameList 接口获取，默认选中第一个，通过任务id和选中的SpeciesName 请求/getResultMapData 结果获取数据并渲染
6. 下方表格中的内容是通过任务id和选中的SpeciesName请求/getTableResult 接口获取结果
7. 上面提到的接口你需要新建一个js文件，将请求方法放到里面，可以参考@samples

IMPORTANT：
1. vue里面的编码风格请参考 @index 页面，例如 变量的命名，函数的风格，proxy下的modal 等等
2. 只修改biogeography 功能涉及到的代码和js，请不要影响原型页面里面的其他功能
3. 不需要生成任何测试代码和总结报告，你生成完代码自己检查一遍代码的正确性就可以了
4. 生成的代码不需要过多的异常处理，保证功能正常实现就行，这样方便我阅读你生成的代码
