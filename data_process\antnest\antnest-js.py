import json

import pandas as pd

# 读取Excel文件
file_path = 'data/MASH_AntNest_Samples_250102.xlsx'
df = pd.read_excel(file_path)

# 初始化存储结果的列表
result = []

# 遍历每一行
for _, row in df.iterrows():
    if row['Water Body Type'] == 'unspecified':
        continue

    # 解析Lat_lon列
    lat_lon = row['Lat_lon']
    if isinstance(lat_lon, str) and ',' in lat_lon:
        lat, lon = lat_lon.split(',')

        # 获取type字段的值
        if row['Critical Zone'] == 'IN':
            body_type = 'Critical Zone'
        elif row['Water Body Type'] == 'Oceans':
            body_type = 'Oceans'
        else:
            body_type = 'Inland'

        # 获取analysis字段的值
        analysis_fields = ['MASH-Ocean', 'MASH-Lake', 'MASH-Chinasea', 'MASH-China', 'MEER']
        analysis = any(row[field] == 'IN' for field in analysis_fields)

        # 生成JS数组对象
        # 'technology_type': row['Technology_type'],
        entry = {
            'lat': lat.strip(),
            'lon': lon.strip(),
            'type': body_type,
            'node': False,
            'analysis': analysis,
            'omics_type': str(row['tOmics_type']).upper(),
            'technology_type': str(row['Technology_type']),
            'run_id': row['RunID'],
            'project_id': row['BioprojectID']
        }

        # 将结果添加到列表
        result.append(entry)

# 转换为 JSON 格式并格式化为 JavaScript 数组
js_array = f"const data = {json.dumps(result, indent=4)};"

# 保存为 JavaScript 文件
output_file = "antnest-samples.js"
with open(output_file, "w", encoding="utf-8") as f:
    f.write(js_array)

print("\n蚁巢JS生成完成")

