{"name": "MASH", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite.js build", "build:stage": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite.js build --mode staging", "preview": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/dompurify": "^3.2.0", "axios": "^1.7.9", "dompurify": "^3.2.5", "echarts": "^5.5.1", "el-table-infinite-scroll": "^3.0.3", "element-plus": "^2.9.5", "element-resize-detector": "^1.2.4", "file-saver": "^2.0.5", "gsap": "^3.12.7", "html-to-text": "^9.0.5", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "leaflet-echarts": "^0.2.1", "leaflet.markercluster": "^1.5.3", "lodash": "^4.17.21", "marked": "^15.0.10", "moment": "^2.30.1", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "pinia": "^2.3.1", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.2.3", "swiper": "^11.1.14", "v3-carousel": "^2.0.3", "vue": "^3.5.13", "vue-i18n": "^9.14.4", "vue-router": "^4.5.0", "vue-slick-carousel": "^1.0.6", "vue3-carousel": "^0.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "@vue/compiler-sfc": "3.4.27", "@vue/eslint-config-standard": "^8.0.1", "eslint": "8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-html": "^8.1.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "sass": "^1.67.0", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "^4.5.13", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1"}}