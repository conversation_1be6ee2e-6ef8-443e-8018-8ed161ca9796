# -*- coding: utf-8 -*-

"""
 统计名称
 Author: lrj
 Date: 2025-01-06
 Version: 1.0.0

"""

import pandas as pd
from openpyxl import load_workbook

from back_fill_to_total import back_filling_plus_name
from cust_utils import load_data, mk_dir, time_stat
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item

# 创建一个自定义的日志记录器
log_file_name = 'statistics_type_name.log'
logger = init_default_logger(log_file_name)

# 获取当前配置项
curr_process_item = get_curr_process_item()
file_path = curr_process_item.get('all_file_path')


@time_stat(logger)
def statistics_name():
    # 读取Excel文件
    # df = pd.read_excel('./data/MASH_NODE_Samples_final.xlsx')  # 请替换为你的Excel文件路径
    df = load_data(file_path, back_filling_plus_name)

    # 创建一个空的DataFrame来存储结果
    results = pd.DataFrame(columns=['Water Body Type', 'Water Body Name', 'Count', 'Percentage'])

    # 统计每种Water Body Type的数量
    water_body_types = df['Water Body Type'].unique()

    # 获取df的总行数
    total_rows = len(df)

    output_dir = mk_dir('./data/statistics')
    # 使用for循环遍历每种Water Body Type
    for water_body_type in water_body_types:
        # 筛选出对应类型的数据
        filtered_df = df[df['Water Body Type'] == water_body_type]

        # 统计'unspecified'和'非unspecified'的数量
        unspecified_count = (filtered_df['Water Body Name'] == 'unspecified').sum()
        not_unspecified_count = (filtered_df['Water Body Name'] != 'unspecified').sum()

        # 计算百分比占比
        total_count = unspecified_count + not_unspecified_count
        unspecified_percentage = round((unspecified_count / total_count) * 100, 4) if total_count > 0 else 0
        not_unspecified_percentage = round((not_unspecified_count / total_count) * 100, 4) if total_count > 0 else 0

        # 计算Count占整个DataFrame的百分比，保留4位小数
        unspecified_percentage_of_total = round((unspecified_count / total_rows) * 100, 4)
        not_unspecified_percentage_of_total = round((not_unspecified_count / total_rows) * 100, 4)

        # 创建一个临时的DataFrame来存储每个循环的结果
        temp_results = pd.DataFrame({
            'Water Body Type': [water_body_type] * 2,
            'Water Body Name': ['unspecified', 'specified'],
            'Count': [unspecified_count, not_unspecified_count],
            'Percentage': [unspecified_percentage, not_unspecified_percentage],
            'Percentage of Total': [unspecified_percentage_of_total, not_unspecified_percentage_of_total]
        })

        temp_results = temp_results.dropna(axis=1, how='all')

        # 使用pd.concat将临时结果追加到主DataFrame中
        results = pd.concat([results, temp_results], ignore_index=True)

    # 输出结果到Excel文件
    output_path = f'{output_dir}/water_body_statistics.xlsx'  # 请替换为你想保存的路径
    results.to_excel(output_path, index=False)

    # 使用openpyxl加载Excel文件并处理合并单元格
    wb = load_workbook(output_path)
    ws = wb.active

    # 合并相同的单元格（即"Water Body Type"列）
    previous_value = None
    start_row = 2  # 从第2行开始（第1行是标题）
    for row in range(2, len(results) + 2):  # 从第2行到最后一行
        cell_value = ws[f'A{row}'].value  # A列是'Water Body Type'

        if cell_value == previous_value:
            # 如果当前单元格的值和上一行的相同，跳过当前行
            continue
        else:
            # 如果当前单元格的值和上一行不同，检查是否需要合并
            if previous_value is not None:
                ws.merge_cells(start_row=start_row, start_column=1, end_row=row - 1, end_column=1)

            # 更新start_row和previous_value
            start_row = row
            previous_value = cell_value

    # 最后一个分组的合并
    ws.merge_cells(start_row=start_row, start_column=1, end_row=len(results) + 1, end_column=1)

    # 保存合并后的Excel文件
    wb.save(output_path)

    logger.info(f"结果已保存并且第一列相同的单元格已合并，文件路径: {output_path}")


if __name__ == '__main__':
    statistics_name()
