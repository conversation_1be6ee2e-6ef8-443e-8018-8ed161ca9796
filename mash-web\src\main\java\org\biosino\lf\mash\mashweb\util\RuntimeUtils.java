package org.biosino.lf.mash.mashweb.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RuntimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
@Slf4j
public class RuntimeUtils {

    private final static ConcurrentHashMap<String, Process> processMap = new ConcurrentHashMap<>();

    public static int execForExitCode(String cmd, String taskId) {
        log.info("开始执行命令：{}", cmd);
        Process process;
        if (FileUtil.isWindows()) {
            process = RuntimeUtil.exec("cmd.exe", "/c", cmd);
        } else {
            process = RuntimeUtil.exec("sh", "-c", cmd);
        }
        int exitCode = 0;
        try {
            if (taskId != null) {
                processMap.put(taskId, process);
            }
            ExecutorService executor = Executors.newFixedThreadPool(2);
            executor.submit(() -> consumeStream(process.getInputStream(), "[stdout]"));
            executor.submit(() -> consumeStream(process.getErrorStream(), "[stderr]"));
            executor.shutdown();
            exitCode = process.waitFor();
            log.info("执行命令：{} 完成，退出状态码:{}", cmd, exitCode);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (taskId != null) {
                processMap.remove(taskId);
            }
        }
        return exitCode;
    }

    public static int execForExitCode(String cmd) {
        return execForExitCode(cmd, null);
    }

    private static void consumeStream(InputStream inputStream, String prefix) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            while (reader.readLine() != null) ; // 只读取不打印，防止阻塞
        } catch (IOException e) {
            log.error("读取进程输出流时出错", e);
        }
    }


}
