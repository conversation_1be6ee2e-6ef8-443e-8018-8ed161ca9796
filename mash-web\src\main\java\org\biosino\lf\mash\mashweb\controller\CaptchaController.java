package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.google.code.kaptcha.Producer;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.constant.CacheConstants;
import org.biosino.lf.mash.mashweb.core.CustomCache;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class CaptchaController {
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    private final CustomCache customCache;
    private final AppConfig appConfig;


    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode() throws IOException {
        AjaxResult ajax = AjaxResult.success();
        final boolean captchaEnabled = appConfig.isCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled) {
            return ajax;
        }

        // 保存验证码信息
        final String uuid = IdUtil.fastSimpleUUID();
        final String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr, code;
        BufferedImage image;

        // 生成验证码
        final String captchaType = appConfig.getCaptchaType();
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        } else {
            return AjaxResult.error("验证码类型不能为空");
        }

        customCache.setCacheObject(verifyKey, code, CacheConstants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = null;
        try {
            os = new FastByteArrayOutputStream();
            ImageIO.write(image, "jpg", os);

            ajax.put("uuid", uuid);
            ajax.put("img", Base64.encode(os.toByteArray()));
            return ajax;
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage());
        } finally {
            IoUtil.close(os);
        }
    }

}
