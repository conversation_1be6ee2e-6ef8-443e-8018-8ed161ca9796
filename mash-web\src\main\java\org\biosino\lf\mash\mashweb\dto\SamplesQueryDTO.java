package org.biosino.lf.mash.mashweb.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10
 */
@Data
public class SamplesQueryDTO extends BaseQuery {

    private List<String> allIds;

    private String field;

    private String keyword;

    private String selectSearchKeyword;

    private String dataset;

    private Double latitudeStart = -90.0;

    private Double latitudeEnd = 90.0;

    private Double longitudeStart = -180.0;

    private Double longitudeEnd = 180.0;

    private String runId;

    private String bioProjectId;

    private String bioSampleId;

    private String dataSource;

    private String organism;

    private String omicsType;

    private String technologyType;

    private String experimentId;

    private String latLon;

    private String geoLocName;

    private String temperature;

    private Double temperatureStart;

    private Double temperatureEnd;

    private String salinity;

    private Double salinityStart;

    private Double salinityEnd;

    private String depth;

    private Double depthStart;

    private Double depthEnd;

    private String pressure;

    private Double pressureStart;

    private Double pressureEnd;

    private String ph;

    private Double phStart;

    private Double phEnd;

    private String mashLake;

    private String mashOceanV2;

    private String taraOcean;

    private String mashChinaSea;

    private String meer;

    private String samplingSubstrate;

    private String biome;

    private String criticalZone;

    private String country;

    private String hydrosphereType;

    private String waterBodyTypeByClassification;

    private String waterBodyTypeByGeographic;

    private String waterBodyName;

    private String waterBodyType;
}
