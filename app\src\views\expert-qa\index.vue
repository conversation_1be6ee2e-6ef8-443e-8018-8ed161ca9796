<template>
  <div class="qa-container">
    <div class="container-fluid">
      <div class="qa-header">
        <h2>Aquatic Microbiome Atlas Q&A System</h2>
        <p class="qa-description">
          Whether it's professional academic questions or daily consultations,
          you can interact with AI or experts through this system to obtain
          efficient answers and advice. The system supports natural language
          input, is easy to operate, provides a smooth experience, and helps you
          easily obtain the information you need.
        </p>
      </div>

      <div class="qa-content">
        <div class="qa-main">
          <!-- 问答模式选择 -->
          <div class="qa-mode">
            <el-button
              :class="{ active: mode === 'ai' }"
              @click="switchMode('ai')"
            >
              <svg-icon
                :key="mode"
                :icon-class="mode === 'ai' ? 'ai-active' : 'ai'"
                class-name="type-svg-icon"
              ></svg-icon>
              <span>Ask AI</span>
            </el-button>
            <el-button
              :class="{ active: mode === 'expert' }"
              @click="switchMode('expert')"
            >
              <el-icon size="18">
                <Avatar />
              </el-icon>
              <span>Consult Expert</span>
            </el-button>
          </div>

          <!-- AI模式 -->
          <aiQa v-if="mode === 'ai'" ref="aiQaRef" />

          <!-- 专家咨询模式 -->
          <div v-else class="expert-mode">
            <div class="consultation-form card">
              <!-- 问题描述 -->
              <div class="form-section">
                <!-- 问题类型选择 -->
                <div class="question-types">
                  <el-tooltip
                    v-for="type in questionTypes"
                    :key="`qa_type_${type.value}`"
                    :teleported="true"
                    :content="type.description"
                    popper-class="cus-el-popper"
                    effect="dark"
                    placement="bottom-end"
                  >
                    <el-button
                      :class="{ active: selectedType === type.value }"
                      @click="selectedType = type.value"
                    >
                      <template #icon>
                        <svg-icon
                          :key="selectedType"
                          :icon-class="
                            selectedType === type.value
                              ? type.icon + '-active'
                              : type.icon
                          "
                          class-name="type-svg-icon"
                        ></svg-icon>
                      </template>

                      <template #default>
                        <span class="font-16">{{ type.label }}</span>
                      </template>
                    </el-button>
                  </el-tooltip>
                </div>
                <el-input
                  v-model="expertForm.question"
                  :show-word-limit="true"
                  :maxlength="1500"
                  type="textarea"
                  :rows="6"
                  placeholder="Please describe your question in detail.."
                />
              </div>

              <!-- 个人信息表单 -->
              <div class="form-section">
                <h3>Your Information</h3>
                <el-form
                  ref="expertFormRef"
                  :model="expertForm"
                  :rules="formRules"
                  label-position="top"
                >
                  <div class="form-row">
                    <el-form-item
                      label="Title"
                      prop="title"
                      class="form-item-half"
                    >
                      <el-input
                        v-model="expertForm.title"
                        placeholder="Enter your title"
                      />
                    </el-form-item>
                    <el-form-item
                      label="Name"
                      prop="name"
                      class="form-item-half"
                    >
                      <el-input
                        v-model="expertForm.name"
                        placeholder="Enter your name"
                      />
                    </el-form-item>
                  </div>

                  <div class="form-row">
                    <el-form-item
                      label="Organization"
                      prop="organization"
                      class="form-item-half"
                    >
                      <el-input
                        v-model="expertForm.organization"
                        placeholder="Enter your organization"
                      />
                    </el-form-item>
                    <el-form-item
                      label="Email"
                      prop="email"
                      class="form-item-half"
                    >
                      <el-input
                        v-model="expertForm.email"
                        placeholder="Enter your email"
                      />
                    </el-form-item>
                  </div>

                  <el-form-item label="Verification code" prop="captchaCode">
                    <el-input
                      v-model="expertForm.captchaCode"
                      size="large"
                      auto-complete="off"
                      placeholder="Verification code"
                      style="width: 50%"
                      @keyup.enter="handleExpertSubmit"
                    >
                    </el-input>

                    <div class="login-code">
                      <img
                        :src="codeUrl"
                        class="login-code-img"
                        alt=""
                        @click="getCode"
                      />
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      class="submit-btn"
                      :disabled="isLoading"
                      @click="handleExpertSubmit"
                    >
                      <el-icon size="18">
                        <Promotion />
                      </el-icon>
                      <span class="font-16">Submit Question</span>
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>

        <!-- 专家团队(暂时隐藏) -->
        <!--        <div class="expert-team">
          <h3 class="team-title mt-0">
            <svg-icon icon-class="team" class-name="team-icon"></svg-icon>

            Expert Team
          </h3>
          <div class="expert-list">
            <div
              v-for="expert in experts"
              :key="expert.id"
              class="expert-card card"
            >
              <div style="display: flex; align-items: center">
                <img
                  :src="expert.avatar"
                  :alt="expert.name"
                  class="expert-avatar"
                />
              </div>

              <div class="expert-info">
                <h3>{{ expert.name }}</h3>
                <p class="expert-title">{{ expert.title }}</p>
                <p class="expert-institute">{{ expert.institute }}</p>
                <div class="expert-tags">
                  <el-tag v-for="tag in expert.tags" :key="tag"
                    >{{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>-->
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    nextTick,
    onMounted,
    reactive,
    ref,
    toRaw,
    toRefs,
  } from 'vue';
  import { trimStr } from '@/utils';
  import { expertQaAsk, expertQaInitInfo } from '@/api/expert_qa';
  import { getCaptchaImg } from '@/api/captcha';
  import aiQa from './aiQa.vue';
  import { doAddVisitsLog } from '@/api/visits_log';
  import { VISITS_LOG_FUNC } from '@/constants';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const { proxy } = getCurrentInstance();

  const imgUrlPre = ref(`${import.meta.env.VITE_APP_BASE_API}`);
  const mode = ref('ai');
  const selectedType = ref('');
  const questionTypes = ref([]);
  const aiQaRef = ref(null);

  // 验证码开关
  const captchaEnabled = ref(true);
  // 验证码base64地址
  const codeUrl = ref('');

  // 定义响应式变量
  const isLoading = ref(false);

  const expertFormRef = ref(null);

  const expertFormReact = reactive({
    expertForm: {
      title: '',
      name: '',
      organization: '',
      email: '',
      question: '',
      captchaCode: '',
      captchaId: '',
    },
  });
  const { expertForm } = toRefs(expertFormReact);
  const expertFormInit = proxy.$_.cloneDeep(toRaw(expertFormReact.expertForm));

  const formRules = {
    title: [
      {
        required: true,
        message: 'Please input your title',
        trigger: 'blur',
      },
    ],
    name: [
      { required: true, message: 'Please enter your name', trigger: 'blur' },
    ],
    organization: [
      {
        required: true,
        message: 'Please enter your organization',
        trigger: 'blur',
      },
    ],
    email: [
      { required: true, message: 'Please enter your email', trigger: 'blur' },
      {
        type: 'email',
        message: 'Please enter a valid email address',
        trigger: 'blur',
      },
    ],
    captchaCode: [
      {
        required: true,
        message: 'Please enter verification code',
        trigger: 'blur',
      },
    ],
  };

  const experts = ref(null);

  function getCode() {
    getCaptchaImg().then(res => {
      captchaEnabled.value =
        res.captchaEnabled === undefined ? true : res.captchaEnabled;
      if (captchaEnabled.value) {
        codeUrl.value = 'data:image/jpeg;base64,' + res.img;
        expertForm.value.captchaId = res.uuid;
        expertForm.value.captchaCode = '';
      }
    });
  }

  function resetExpertForm() {
    expertForm.value = proxy.$_.cloneDeep(expertFormInit);
  }

  const switchMode = newMode => {
    mode.value = newMode;
    // 切换模式时清空表单
    if (newMode === 'expert') {
      getCode();
    } else {
      resetExpertForm();
    }
  };

  // 专家问答
  const handleExpertSubmit = async () => {
    if (!expertFormRef.value || isLoading.value) return;

    await expertFormRef.value.validate(async valid => {
      if (valid) {
        await proxy.$modal
          .confirm('Are you sure to submit the Question?', 'Warning', {
            confirmButtonText: 'Submit',
            cancelButtonText: 'Cancel',
            type: 'warning',
          })
          .then(async () => {
            let val = trimStr(expertForm.value.question);
            if (!val) {
              proxy.$modal.msgWarning('Please enter your question');
              return;
            }

            let param = {
              selectedType: selectedType.value,
              ...toRaw(expertForm.value),
            };
            // 记录访问日志
            doAddVisitsLog(route, VISITS_LOG_FUNC.consultExpert, param);

            isLoading.value = true;
            expertQaAsk(param)
              .then(res => {
                if (res?.msg === 'success') {
                  proxy.$modal.alertSuccess(
                    `Your question has been successfully submitted.<br>
                            Experts will provide answers later and send them to your email.`,
                    true,
                  );

                  // 清空表单
                  resetExpertForm();
                }
              })
              .finally(() => {
                isLoading.value = false;
                // 重新获取验证码
                if (captchaEnabled.value) {
                  getCode();
                }
              });
          });
      }
    });
  };

  // 组件挂载时的效果
  onMounted(async () => {
    // 获取页面初始化信息，包括问题类型和专家信息
    await expertQaInitInfo().then(res => {
      let data = res?.data;
      if (data) {
        let qaTypes = data.questionTypes;
        // { label: 'Ecology & Environment', value: 'ecology', icon: 'ecology' }
        for (let i = 0; i < qaTypes.length; i++) {
          let item = qaTypes[i];
          item['icon'] = item.value;
          if (item.selected) {
            selectedType.value = item.value;
          }
        }
        questionTypes.value = qaTypes;

        let expertInfos = data.expertInfos;
        let expertArr = [];
        for (let i = 0; i < expertInfos.length; i++) {
          let item = expertInfos[i];
          expertArr.push({
            id: item.id,
            name: item.name,
            title: item.researchField,
            institute: item.unit,
            avatar: `${imgUrlPre.value}/img_preview${item.photo}?_t=${new Date().getTime()}`,
            tags: item.labels,
          });
        }
        experts.value = expertArr;

        let queryKeyword = trimStr(proxy.$route.query.keyword);
        if (queryKeyword) {
          // 从首页进行AI问答搜索
          mode.value = 'ai';
          // 使用nextTick确保aiQaRef已经挂载
          nextTick(() => {
            if (aiQaRef.value) {
              aiQaRef.value.homeAiSearch(queryKeyword);
            }
          });
        }
      }
    });
  });
</script>

<style>
  .cus-el-popper {
    max-width: 700px;
  }
</style>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .qa-container {
    padding: 120px 0 45px 0;
    margin: 0 auto;
  }

  .card {
    padding: 0;
  }

  .qa-header {
    margin-bottom: 40px;

    .qa-title {
      font-size: 32px;
      color: #182b49;
      margin-bottom: 20px;
    }

    .qa-description {
      font-size: 18px;
      color: #4b5563;
      line-height: 1.6;
    }
  }

  .qa-content {
    display: flex;
    gap: 40px;
  }

  .qa-main {
    flex: 1;
  }

  .qa-mode {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;

    .el-button {
      flex: 1;
      height: 50px;
      font-size: 16px;
      border: none;
      background-color: #f3f4f6;

      span {
        font-size: 18px;
        color: #374151;
      }

      &:hover {
        transition: all 0.3s linear;
      }

      &:hover,
      &.active {
        background-color: #3498db;
        color: #fff;

        span {
          color: #fff;
        }
      }

      // 当mode为expert时，移除AI按钮的背景色
      &:first-child {
        &.active {
          background-color: #3498db;
          color: #ffffff;
          border: none;
        }
      }
    }
  }

  .question-types {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;

    .el-button {
      flex: 1;
      height: 45px;
      border-radius: 4px;
      border: none;
      background-color: #f3f4f6;

      span {
        font-size: 16px;
      }

      &.active {
        background-color: #3498db;
        color: #ffffff;
        border: none;
      }

      :deep(.el-icon) {
        width: 1.7em;
        height: 1.7em;
      }
    }
  }

  .question-input {
    margin-bottom: 30px;

    .el-textarea {
      margin-bottom: 15px;
    }

    .submit-btn {
      width: 120px;
      float: right;
    }
  }

  .answer-section {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;

    h3 {
      color: #182b49;
      margin-bottom: 15px;
    }

    .answer-content {
      color: #333;
      line-height: 1.6;
    }

    .references {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #e4e7ed;

      h4 {
        color: #182b49;
        margin-bottom: 10px;
      }

      ul {
        padding-left: 20px;
        color: #666;
      }
    }
  }

  .ref {
    color: #6b7280;

    ol {
      padding-left: 20px;

      li {
        list-style-type: disc;
      }
    }
  }

  .expert-team {
    width: 400px;

    .team-title {
      display: flex;
      align-items: center;
      color: #111827;
      font-weight: 500;

      .team-icon {
        width: 24px;
        height: 24px;
        margin-right: 0.8rem;
      }
    }
  }

  .expert-card {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #dadada;

    .expert-avatar {
      width: 121px;
      height: 156px;
      object-fit: cover;
    }

    .expert-info {
      flex: 1;

      p {
        line-height: 1.4;
      }

      h3 {
        font-size: 20px;
        color: #333;
        font-weight: 500;
        margin-top: 0;
        margin-bottom: 0;
      }

      .expert-title {
        font-size: 15px;
        color: #666;
        margin-bottom: 4px;
        font-weight: normal;
      }

      .expert-institute {
        font-size: 15px;
        color: #999;
        margin-bottom: 8px;
      }

      .expert-tags {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .el-tag {
          font-size: 12px;
          background-color: #f5f7fa;
          border: none;
          color: #666;
          padding: 2px 10px;
          border-radius: 4px;
          font-weight: normal;
        }
      }
    }
  }

  .expert-mode {
    .question-types {
      margin-bottom: 30px;
    }

    .consultation-form {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      margin-top: 20px;
    }

    .form-section {
      padding: 20px;

      &:not(:last-child) {
        border-bottom: 1px solid #ebeef5;
      }

      h3 {
        font-size: 18px;
        color: #333;
        margin-bottom: 20px;
        font-weight: 500;
      }
    }

    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .form-item-half {
        flex: 1;
      }
    }

    :deep(.el-form-item__label) {
      color: #666;
      font-weight: 500;
    }

    .submit-btn {
      width: 100%;
      height: 40px;
      background-color: #3498db;
      border: none;
    }
  }

  .svg-icon {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
  }

  .type-svg-icon {
    width: 18px;
    height: 18px;
    margin-right: 0.5rem;
  }

  .login-code {
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .login-code-img {
    height: 45px;
    padding-left: 12px;
  }
</style>
