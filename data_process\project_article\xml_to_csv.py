# -*- coding: utf-8 -*-

"""
grobid pdf解析结果（xml）转csv
Author: LWJ
Version: 1.0.0
"""

import argparse
import csv
import os
import xml.etree.ElementTree as ET

# 定义命名空间
namespaces = {
    "tei": "http://www.tei-c.org/ns/1.0"
}


# python xml_to_csv.py  ./mash/input_3w/output ./mash/input_3w/output_csv

def parse_tei_xml_to_csv(xml_file, csv_file):
    # 解析 XML 文件
    tree = ET.parse(xml_file)
    root = tree.getroot()

    # 用于存储结果的列表
    data = []

    # 提取 <abstract> 下的内容
    abstracts = root.findall('.//tei:abstract', namespaces)
    for abstract in abstracts:
        # 如果 <abstract> 标签内有子标签 <p>，提取其内容
        for p in abstract.findall('.//tei:p', namespaces):
            content = ''.join(p.itertext()).strip()
            data.append(('abstract', content))

    # 提取 <body> 下的内容
    bodies = root.findall('.//tei:body', namespaces)
    for body in bodies:
        # 如果 <body> 标签内有子标签 <p>，提取其内容
        for p in body.findall('.//tei:p', namespaces):
            content = ''.join(p.itertext()).strip()
            data.append(('body', content))

    # 将数据一次性写入 CSV 文件，确保格式严格
    with open(csv_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file, quoting=csv.QUOTE_ALL)
        # 写入表头
        writer.writerow(['type', 'content'])
        # 写入数据，确保每一行严格格式
        for row in data:
            writer.writerow([field.replace("\n", " ").replace("\r", " ").strip() for field in row])

    print(f"CSV 文件已生成: {csv_file}")


def parse_directory(input_directory, output_directory):
    if not input_directory or not output_directory:
        raise ValueError("输入目录或输出目录不能为空")

    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    # 遍历输入目录下所有 .xml 文件
    for filename in os.listdir(input_directory):
        if filename.endswith('.xml'):
            # 获取 XML 文件的完整路径
            xml_file = os.path.join(input_directory, filename)

            # 获取 XML 文件名（去掉路径和扩展名）
            base_filename = os.path.splitext(filename)[0]

            # 定义输出 CSV 文件的路径（取第一个句号前的部分）
            # csv_file = os.path.join(output_directory, f'{base_filename.split(".")[0]}.csv')
            csv_file = os.path.join(output_directory, f'{base_filename}.csv')

            # 解析 XML 文件并生成对应的 CSV 文件
            parse_tei_xml_to_csv(xml_file, csv_file)


if __name__ == '__main__':
    # 使用 argparse 解析命令行参数
    parser = argparse.ArgumentParser(description='Parse TEI XML files and generate CSV files.')
    parser.add_argument('input_dir', type=str, help='Input directory containing .xml files')
    parser.add_argument('output_dir', type=str, help='Output directory for CSV files')

    # 获取命令行参数
    # args = parser.parse_args()
    args = {
        'input_dir': 'data/grobid_xml/xml',
        'output_dir': 'data/grobid_xml/output_csv'
    }

    # 确保输出目录存在
    if not os.path.exists(args['output_dir']):
        os.makedirs(args['output_dir'])

    # 解析输入目录并生成输出目录中的 CSV 文件
    parse_directory(args['input_dir'], args['output_dir'])
