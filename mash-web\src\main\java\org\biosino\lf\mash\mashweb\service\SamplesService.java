package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.dto.HomeMapQueryDTO;
import org.biosino.lf.mash.mashweb.dto.KoDepthPathwayDTO;
import org.biosino.lf.mash.mashweb.dto.SamplesQueryDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesRepository;
import org.biosino.lf.mash.mashweb.util.StrSortUtils;
import org.biosino.lf.mash.mashweb.util.kegg.KeggEntry;
import org.biosino.lf.mash.mashweb.vo.SamplesVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SamplesService {
    private final SamplesRepository samplesRepository;
    private final RemoteNodeService remoteNodeService;
    private final FileProperties fileProperties;

    public Page<SamplesVO> list(SamplesQueryDTO queryDTO) {
        Page<Samples> page = samplesRepository.queryPage(queryDTO);
        try {
            Page<SamplesVO> result = page.map(x -> {
                SamplesVO vo = new SamplesVO();
                BeanUtil.copyProperties(x, vo);
                if (StrUtil.equalsIgnoreCase(x.getRunStatus(), "Unaccessible")) {
                    vo.setLatitude("*");
                    vo.setLongitude("*");
                    vo.setGeoLocName("*");
                    vo.setTemperature("*");
                    vo.setSalinity("*");
                    vo.setDepth("*");
                    vo.setPressure("*");
                    vo.setPh("*");
                    vo.setSamplingSubstrate("*");
                    vo.setBiome("*");
                    vo.setCriticalZone("*");
                    vo.setCountry("*");
                    vo.setWaterBodyTypeByClassification("*");
                    vo.setWaterBodyName("*");
                }

                return vo;
            });
            return result;
        } catch (Exception e) {
            return page.map(x -> {
                SamplesVO vo = new SamplesVO();
                BeanUtil.copyProperties(x, vo);
                return vo;
            });
        }
    }

    public List<String> filterTips(SamplesQueryDTO queryDTO) {
        List<String> list = samplesRepository.findFieldDistinct(queryDTO);
        if (StrUtil.isBlank(queryDTO.getSelectSearchKeyword())) {
            return list.stream().limit(100).collect(Collectors.toList());
        }
        return StrSortUtils.sortBySimilar(queryDTO.getSelectSearchKeyword(), list).stream().limit(100).collect(Collectors.toList());
    }

    public Map<String, Long> getWaterBodyTypeByClassificationDistribution(SamplesQueryDTO queryDTO) {
        return samplesRepository.getWaterBodyTypeByClassificationDistribution(queryDTO);
    }

    public Map<String, Long> getTechnologyTypeDistribution(SamplesQueryDTO queryDTO) {
        return samplesRepository.getTechnologyTypeDistribution(queryDTO);
    }

    public Map<String, Long> getWaterBodyTypeByGeographicDistribution(SamplesQueryDTO queryDTO) {
        return samplesRepository.getWaterBodyTypeByGeographicDistribution(queryDTO);
    }

    /**
     * 查询所有不重复的lat_lon位置信息，包含经纬度和水体类型信息
     *
     * @param queryDTO 查询条件
     * @return 位置和水体类型信息列表
     */
    public List<Map<String, Object>> findDistinctLatLonWithType(SamplesQueryDTO queryDTO) {
        return samplesRepository.findDistinctLatLonWithType(queryDTO);
    }

    /**
     * 获取NODE数据的可访问状态
     *
     * @param ids 样本ID列表
     * @return 四种ID的可访问状态映射
     */
    public Map<String, Object> getNodeDataAccessStatus(List<String> ids) {
        // 根据ID列表查询样本信息
        List<Samples> nodeData = samplesRepository.findAllByIdInAndDataSource(ids, "NODE");

        // 结果
        Map<String, Object> result = new HashMap<>();

        if (nodeData.isEmpty()) {
            return result;
        }

        // 收集四种ID
        List<String> runIdList = new ArrayList<>();
        List<String> projectIdList = new ArrayList<>();
        List<String> sampleIdList = new ArrayList<>();
        List<String> experimentIdList = new ArrayList<>();

        // 从Node数据中提取各种ID
        for (Samples item : nodeData) {
            if (StrUtil.isNotBlank(item.getRunId())) {
                runIdList.add(item.getRunId());
            }
            if (StrUtil.isNotBlank(item.getBioProjectId())) {
                projectIdList.add(item.getBioProjectId());
            }
            if (StrUtil.isNotBlank(item.getBioSampleId())) {
                sampleIdList.add(item.getBioSampleId());
            }
            if (StrUtil.isNotBlank(item.getExperimentId())) {
                experimentIdList.add(item.getExperimentId());
            }
        }

        // 使用CompletableFuture并行处理四种ID的请求
        CompletableFuture<Void> runIdFuture = CompletableFuture.runAsync(() -> {
            if (!runIdList.isEmpty()) {
                List<JSONObject> runData = remoteNodeService.findMetadataByNos("run", runIdList);
                for (JSONObject item : runData) {
                    String runNo = item.getStr("runNo");
                    String visibleStatus = item.getStr("visibleStatus");
                    if (StrUtil.isNotBlank(runNo) && StrUtil.isNotBlank(visibleStatus)) {
                        result.put(runNo, visibleStatus);
                    }
                }
            }
        });

        CompletableFuture<Void> projectIdFuture = CompletableFuture.runAsync(() -> {
            if (!projectIdList.isEmpty()) {
                List<JSONObject> projectData = remoteNodeService.findMetadataByNos("project", projectIdList);
                for (JSONObject item : projectData) {
                    String projectNo = item.getStr("projectNo");
                    String visibleStatus = item.getStr("visibleStatus");
                    if (StrUtil.isNotBlank(projectNo) && StrUtil.isNotBlank(visibleStatus)) {
                        result.put(projectNo, visibleStatus);
                    }
                }
            }
        });

        CompletableFuture<Void> sampleIdFuture = CompletableFuture.runAsync(() -> {
            if (!sampleIdList.isEmpty()) {
                List<JSONObject> sampleData = remoteNodeService.findMetadataByNos("sample", sampleIdList);
                for (JSONObject item : sampleData) {
                    String sapNo = item.getStr("sapNo");
                    String visibleStatus = item.getStr("visibleStatus");
                    if (StrUtil.isNotBlank(sapNo) && StrUtil.isNotBlank(visibleStatus)) {
                        result.put(sapNo, visibleStatus);
                    }
                }
            }
        });

        CompletableFuture<Void> experimentIdFuture = CompletableFuture.runAsync(() -> {
            if (!experimentIdList.isEmpty()) {
                List<JSONObject> experimentData = remoteNodeService.findMetadataByNos("experiment", experimentIdList);
                for (JSONObject item : experimentData) {
                    String expNo = item.getStr("expNo");
                    String visibleStatus = item.getStr("visibleStatus");
                    if (StrUtil.isNotBlank(expNo) && StrUtil.isNotBlank(visibleStatus)) {
                        result.put(expNo, visibleStatus);
                    }
                }
            }
        });

        // 等待所有异步任务完成
        CompletableFuture.allOf(runIdFuture, projectIdFuture, sampleIdFuture, experimentIdFuture).join();

        return result;
    }

    public List<Map<String, Object>> getHomeMapLocation(HomeMapQueryDTO homeMapQueryDTO) {
        return samplesRepository.getHomeMapLocation(homeMapQueryDTO);
    }

    /**
     * 根据水文圈类型获取匹配的水体类型列表
     *
     * @param hydrosphereType 水文圈类型
     * @param category        类别（Geographic或Classification）
     * @return 过滤后的水体类型列表
     */
    public List<String> getWaterBodyTypeByHydrosphere(String hydrosphereType, String category) {

        // 创建查询条件
        SamplesQueryDTO queryDTO = new SamplesQueryDTO();

        // 只有当hydrosphereType不是"All"时才添加过滤条件
        if (!StrUtil.equalsIgnoreCase(hydrosphereType, "All")) {
            queryDTO.setHydrosphereType(hydrosphereType);
        }

        // 使用自定义查询获取符合条件的水体类型
        return samplesRepository.findFieldDistinctByHydrosphere(category, hydrosphereType);
    }

    public List<KoDepthPathwayDTO> getKoPathwayDetail(String runId) {
        String baseDataDir = fileProperties.getBaseDataDir();
        File file = FileUtil.file(baseDataDir, "KO_depth", runId + ".KO_depth.tsv");

        List<String> lines = FileUtil.readUtf8Lines(file);

        List<KoDepthPathwayDTO> list = new ArrayList<>();
        for (String line : lines) {
            try {
                if (StrUtil.isNotBlank(line)) {
                    String[] split = line.split("\t");
                    String s = split[0];
                    String depth = split[1];

                    KoDepthPathwayDTO dto = new KoDepthPathwayDTO();
                    dto.setKo(s.split("---")[1]);
                    dto.setDepth(Double.parseDouble(depth));
                    list.add(dto);
                }
            } catch (Exception e) {
            } finally {
                continue;
            }
        }

        // 排序后取前30条
        list = list.stream()
                .sorted(Comparator.comparingDouble(KoDepthPathwayDTO::getDepth).reversed())
                .limit(30)
                .toList();

        Map<String, List<KeggEntry>> map = CacheService.pathToKeggEntryMap;
        for (KoDepthPathwayDTO koDepthPathwayDTO : list) {
            List<String> pathways = new ArrayList<>();
            String ko = koDepthPathwayDTO.getKo();

            for (String key : map.keySet()) {
                List<KeggEntry> entries = map.get(key);
                List<String> koIds = entries.stream().map(KeggEntry::getKoId).toList();
                if (koIds.contains(ko)) {
                    pathways.add(key);
                }
            }
            koDepthPathwayDTO.setPathways(pathways);
        }

        return list;

    }
}
