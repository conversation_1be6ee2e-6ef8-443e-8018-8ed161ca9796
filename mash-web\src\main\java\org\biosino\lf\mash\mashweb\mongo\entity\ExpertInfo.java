package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * 专家信息
 *
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
@Document(collection = "expert_info")
public class ExpertInfo implements Serializable {
    @Id
    private String id;

    /**
     * 姓名
     */
    private String name;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 研究领域
     */
    private String researchField;
    /**
     * 单位
     */
    private String unit;
    /**
     * 标签
     */
    private List<String> labels;

    /**
     * 头像路径
     */
    private String photo;
    /**
     * 排序序号
     */
    private Integer order;
    /**
     * 描述
     */
    private String description;

}
