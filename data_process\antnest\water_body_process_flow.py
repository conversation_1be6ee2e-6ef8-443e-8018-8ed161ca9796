# -*- coding: utf-8 -*-

"""
 水体数据处理流程
 Author: 尚尉
 Date: 2025-01-02
 Version: 1.0.0

"""


def process_flow():
    print('开始')
    print('配置process_file_config.py')
    print('运行water_body_project_merge.py合并数据')
    print('main_process.py合并数据')
    print('运行water_body_batch_split_data.py切割数据')
    print('手动到智谱AI官网（https://open.bigmodel.cn/console/batch/task）进行分析，得到结果')
    print('再回填结果：water_body_batch_fill_data.py')
    print('手工治理水体类型：water_body_type_standardize.py')
    print('手工治理水体名称：water_body_name_standardize.py')
    print('使用经纬度定位方法得到部分湖泊、河流、海洋位置：water_body_name_process.py')
    print('人工剔除异常点，如点在海洋中，但是类型是内陆，点在内陆，类型却是海洋')
    print('把治理好的简要版数据回填到总表中：back_fill_to_total.py')
    print('导出地图js：gen_map_js.py')
    print('生成统计，看数据量的变化：statistics_filed.py')
    print('生成统计，看数据量的变化：statistics_type_name.py')
    print('剔除geojson中，实际水体名称中没有的数据：remove_none_geojson.py')
    print('结束')


if __name__ == '__main__':
    process_flow()
