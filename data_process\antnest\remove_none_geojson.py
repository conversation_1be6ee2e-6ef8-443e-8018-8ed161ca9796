# -*- coding: utf-8 -*-

"""
 把GeoJSON内的河流、湖泊剔除不在实际数据中有的数据
"""

import geojson

def filter_geojson(input_path, output_path, property_name, valid_values):
    """
    读取一个GeoJSON文件，根据指定属性名过滤值只保留在有效值列表中的特征。

    :param input_path: 输入GeoJSON文件路径
    :param output_path: 输出GeoJSON文件路径
    :param property_name: 用于过滤的属性名
    :param valid_values: 允许的值列表
    """
    # 打开并读取GeoJSON文件
    with open(input_path, 'r', encoding='utf-8') as file:
        data = geojson.load(file)

    # 确保数据是FeatureCollection
    if not isinstance(data, geojson.FeatureCollection):
        raise ValueError("输入的GeoJSON文件不是有效的FeatureCollection。")

    # 筛选特征
    filtered_features = []
    for feature in data.features:
        # 获取属性值
        if property_name in feature.properties:
            property_value = feature.properties[property_name]
            if property_value in valid_values:
                filtered_features.append(feature)

    # 创建新的FeatureCollection
    filtered_data = geojson.FeatureCollection(filtered_features)

    # 写入筛选后的数据到输出文件
    with open(output_path, 'w', encoding='utf-8') as file:
        geojson.dump(filtered_data, file, ensure_ascii=False, separators=(',', ':'))

    print(f"筛选完成，结果已保存到 {output_path}")

# 示例使用
if __name__ == "__main__":
    input_geojson_path = "data/geojson/ne_final_rivers.geojson"  # 输入GeoJSON文件路径
    output_geojson_path = "sample_rivers.geojson"  # 输出GeoJSON文件路径

    lakes_name = "Name"  # 要检查的属性名
    lakes_dict = ["Constance",
                      "Towuti",
                      "Michigan",
                      "Superior",
                      "Tenny Park Locks",
                      "Pangong",
                      "Ta-tse",
                      "Lung Mu",
                      "Tai",
                      "Chilika",
                      "Poyang",
                      "Po",
                      "Balaton",
                      "Lake Tisza",
                      "Chao",
                      "Three Gorges Reservoir",
                      "Geneva",
                      "Saimaa",
                      "Bosten",
                      "Lagkor",
                      "Lake Washington",
                      "Tornetrask",
                      "Grand Lake",
                      "Erh",
                      "Dian Chi",
                      "Kolmajarvi",
                      "Keuruunselka",
                      "Gull Lake Reservoir",
                      "Ma-p'ang yung-ts'o",
                      "Dagze",
                      "Tangra",
                      "P'eng-Ts'o",
                      "Nam",
                      "Ngoin",
                      "Bangkog",
                      "Bong",
                      "Pa-Mu-Ts'o",
                      "Hazen",
                      "Futou",
                      "Namru",
                      "Imandra",
                      "Teletskoye",
                      "Fuxian",
                      "Great Slave",
                      "Tanganyika",
                      "William H. Harsha Lake",
                      "Malawi",
                      "Red Lake Reservoir",
                      "La-ang",
                      "Khanka",
                      "Winnipeg",
                      "Gyaring",
                      "Abayata",
                      "Shala",
                      "Neuchatel",
                      "Luoma",
                      "P'u-mo-ts'o",
                      "Na",
                      "Quagan",
                      "Hala",
                      "Ness",
                      "Caspian Sea",
                      "Vattern",
                      "Champlain",
                      "Cheney",
                      "Neagh",
                      "Kivu",
                      "Walker Lake",
                      "Salton",
                      "Lake Campostosto",
                      "Great Salt",
                      "Mono Lake",
                      "Urmia",
                      "Burr Oak Lake",
                      "Utah Lake",
                      "Afrera ye'ch'ew",
                      "T'a-jo",
                      "Ngangla Ringco",
                      "Jen-ch'ing hsiu-pu-ts'o",
                      "Hsu-ju",
                      "Detroit Lake"]  # 允许的属性值列表

    river_name = "name_en"  # 要检查的属性名
    river_dict = ["Chao Phraya",
              "Tone",
              "Colorado",
              "Mississippi",
              "Wami",
              "James",
              "Douro",
              "San Juan",
              "Grande",
              "Ticino",
              "Wisconsin",
              "Scheldt",
              "Scioto",
              "Mekong",
              "Brahmaputra",
              "Yellow",
              "Yarlung",
              "Tongtian",
              "Zi River",
              "Salween",
              "Jinsha",
              "Xi",
              "Qin He",
              "Yangtze",
              "Cowpasture",
              "Bei",
              "Cape Fear",
              "Rhône",
              "Tapajós",
              "Amazon",
              "Daling",
              "Swan",
              "Kanawha",
              "Fuchun",
              "Madison",
              "Yongding",
              "Hai",
              "Tuscarawas",
              "Zhang",
              "Susquehanna",
              "Allegheny",
              "W. Branch Susquehanna",
              "Tonlé Sap",
              "Salinas",
              "San Joaquin",
              "Sacramento",
              "Bayou Lafourche",
              "Wei",
              "Lek",
              "Nakdong",
              "Adige",
              "Cooper",
              "Waikato",
              "Han",
              "Gan",
              "Min",
              "Oconee",
              "Athabasca",
              "Sava",
              "St. Johns",
              "Tennessee",
              "Derwent",
              "Indaiá",
              "Motueka",
              "Jiulong",
              "Wu",
              "Jialing",
              "Hudi",
              "Paatsjoki",
              "Hutt",
              "Ingoda",
              "Hudson",
              "Main",
              "Songhua",
              "Namhan",
              "Ōreti",
              "Potomac",
              "Xiang",
              "Yamuna",
              "Ganges",
              "Ottawa",
              "Penner",
              "Kaveri",
              "Danube",
              "Churchill",
              "Snake",
              "Rhine",
              "Kola",
              "Columbia",
              "Kennebec",
              "Maritsa",
              "Luan",
              "Kansas",
              "Missouri",
              "Palar",
              "Delaware",
              "Holston",
              "Elbe",
              "Ravi"]

    filter_geojson(input_geojson_path, output_geojson_path, river_name, river_dict)
