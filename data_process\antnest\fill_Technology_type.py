import pandas as pd

# 读取 MASH_Samples_merged.xlsx 文件
mash_samples_file = "data/MASH_Samples_merged.xlsx"
mash_df = pd.read_excel(mash_samples_file)

# 读取 20250102_001.xlsx 文件
run_experiment_file = "data/20250102_001.xlsx"
run_df = pd.read_excel(run_experiment_file, sheet_name="run")
experiment_df = pd.read_excel(run_experiment_file, sheet_name="experiment")

# 筛选 Source Type 为 NODE 的行
node_samples = mash_df[mash_df['Source Type'] == "NODE"].copy()

# 初始化一个列表来存储回填值
technology_types = []

for index, row in node_samples.iterrows():
    run_id = row['RunID']

    # 在 run sheet 中匹配 run_no 列
    run_match = run_df[run_df['run_no'] == run_id]
    if not run_match.empty:
        exp_no = run_match.iloc[0]['exp_no']

        # 在 experiment sheet 中匹配 exp_no 列
        experiment_match = experiment_df[experiment_df['exp_no'] == exp_no]
        if not experiment_match.empty:
            library_strategy = experiment_match.iloc[0]['attributes.library_strategy']
            technology_types.append(library_strategy)
        else:
            technology_types.append(None)  # 未匹配到
    else:
        technology_types.append(None)  # 未匹配到

# 将获取的值回填到原始 DataFrame 的 Technology_type 列
mash_df.loc[node_samples.index, 'Technology_type'] = technology_types

# 保存更新后的文件
output_file = "MASH_Samples_merged_250106.xlsx"
mash_df.to_excel(output_file, index=False)

print(f"处理完成，结果已保存到 {output_file}")
