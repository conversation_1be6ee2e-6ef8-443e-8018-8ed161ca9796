package org.biosino.lf.mash.mashweb.mongo.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.mongo.entity.MashArticleMongo;
import org.biosino.lf.mash.mashweb.mongo.repository.MashArticleMongoCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@RequiredArgsConstructor
public class MashArticleMongoCustomRepositoryImpl implements MashArticleMongoCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Stream<MashArticleMongo> finBySourceWithStream(String source) {
        if (source == null) {
            throw new ServiceException("source cannot be empty");
        }
        final Query query = new Query(Criteria.where("source").is(source))
                .cursorBatchSize(200).noCursorTimeout();
        return mongoTemplate.stream(query, MashArticleMongo.class);
    }

    @Override
    public List<MashArticleMongo> findAllBySource(String source) {
        if (source == null) {
            throw new ServiceException("source cannot be empty");
        }
        return mongoTemplate.find(new Query(Criteria.where("source").is(source)), MashArticleMongo.class);
    }

}
