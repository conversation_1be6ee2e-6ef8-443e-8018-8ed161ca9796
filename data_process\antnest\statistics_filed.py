# -*- coding: utf-8 -*-
"""
统计字段
Author: lrj
Date: 2025-01-06
Version: 1.1.0
"""

import pandas as pd

from back_fill_to_total import back_filling_plus_name
from cust_utils import load_data, mk_dir, time_stat
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item

# 创建自定义日志记录器
log_file_name = 'statistics_filed.log'
logger = init_default_logger(log_file_name)

# 获取当前配置项
curr_process_item = get_curr_process_item()
# file_path = curr_process_item.get('all_file_path')
file_path = "data/MASH_Merge_250326.xlsx"


@time_stat(logger)
def statistics_filed():
    logger.info("开始统计字段")
    # 读取Excel文件
    df = load_data(file_path, "")

    # 定义要统计的字段
    fields = [
        'Hydrosphere Type',
        'Water Body Type (By Classification)',
        'Water Body Type (By Geographic)',
        'Water Body Type',
        'Country',
        'Sampling Substrate',
        'Critical Zone'
    ]

    output_dir = mk_dir('./data/statistics')
    output_path = f'{output_dir}/statistics_fields.xlsx'

    # 使用 pd.ExcelWriter 将所有结果写入同一文件的不同sheet中
    with pd.ExcelWriter(output_path) as writer:
        for field in fields:
            # 统计每种类型的数量
            counts = df[field].value_counts()
            total_count = counts.sum()
            percentages = (counts / total_count * 100).round(4)

            # 创建统计结果 DataFrame
            results = pd.DataFrame({
                'Type': counts.index,
                'Count': counts.values,
                'Percentage(%)': percentages.values
            })

            # 将统计结果写入 Excel 的对应 sheet
            results.to_excel(writer, sheet_name=field, index=False)
            logger.info(f"{field} 的统计结果已写入 sheet '{field}'")

    logger.info(f"所有统计结果已保存到 {output_path}")


if __name__ == '__main__':
    statistics_filed()
