# -*- coding: utf-8 -*-

"""
 水体数据剔除经纬度差别较大数据
 Author: 尚尉
 Date: 2024-12-25
 Version: 1.0.0

"""
import os

from cust_utils import time_stat
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item, is_ant_nest
from water_body_name_process import trim_val, find_nearest_by_gpd, str_val
from water_body_project_merge import load_data, write_result_data
from water_type_and_geojson_config import get_water_type_name_map, get_file_name_map

# 创建一个自定义的日志记录器
log_file_name = 'back_fill_to_total.log'
logger = init_default_logger(log_file_name)

# 全局变量
# 获取当前配置项
curr_process_item = get_curr_process_item()

# 项目数据合并时的基准列
prj_merge_columns_arr = curr_process_item.get('merge_columns', None)

# 输入文件
# input_filename = 'mash_12580.xlsx'
input_filename_merge = curr_process_item.get('main_process_input_filepath')
# input_filename_processed = 'MASH-NODE-Samples_merge_plus_final.xlsx'
input_filename_all = curr_process_item.get('all_file_path')

output_filename_suffix = 'fill_unspecified'
geojson_dir = 'data/geojson'
unspecified_val = 'unspecified'
# unspecified_columns = ['Water Body Type Processed', 'Water Body Name Processed 大模型生成',
#                        'Water Body Name Processed 人工审核', 'Water Body Name 中文翻译']

# input_filename_all = 'MASH-NODE-Samples.xlsx'

fill_to_total_columns_antnest = {
    'cols_of_merge': ['Critical Zone',
                      'Sampling Substrate',
                      'Country',
                      'Type（旧）',
                      'Water_Body_Name（旧）',
                      'Water Body Type',
                      'Water Body Name',
                      'Water Body Name 大模型生成',
                      'Water Body Name 中文翻译',
                      'name_by_lat_lon',
                      'search_type_by_lat_lon',
                      'min_distance_degree',
                      'source_by_lat_lon'],
    'cols_of_total': ['Critical Zone',
                      'Sampling Substrate',
                      'Country',
                      'Type（旧）',
                      'Water_Body_Name（旧）',
                      'Water Body Type',
                      'Water Body Name',
                      'Water Body Name 大模型生成',
                      'Water Body Name 中文翻译',
                      'Water Body Name 经纬度定位生成',
                      '定位方式（IN 包含，Nearest就近）',
                      '就近的距离角度',
                      '定位的数据来源地理信息文件'],
}

fill_to_total_columns_node = {
    'cols_of_merge': ['Lat_lon',
                      'Sampling Substrate',
                      'Country',
                      'Critical Zone',
                      'Water Body Type',
                      'Water Body Name 大模型生成',
                      'Water Body Name',
                      'Water Body Name 中文翻译',
                      'name_by_lat_lon',
                      'search_type_by_lat_lon',
                      'min_distance_degree',
                      'source_by_lat_lon'],
    'cols_of_total': ['Lat_lon',
                      'Sampling Substrate',
                      'Country',
                      'Critical Zone',
                      'Water Body Type',
                      'Water Body Name 大模型生成',
                      'Water Body Name',
                      'Water Body Name 中文翻译',
                      '水体名称 By 经纬度定位',
                      '定位方式（IN 包含，Nearest就近）',
                      '就近的距离角度',
                      '定位的数据来源地理信息文件'],
}

back_filling_plus_name = 'backfilling'


def find_lat_lon_distance_to_oceans(lat_lon: str, water_type_name_map, file_name_map):
    info_by_lat_lon = None
    if ',' in lat_lon:
        lat_lon_index = lat_lon.index(',')
        try:
            lat_str = lat_lon[:lat_lon_index].strip()
            lon_str = lat_lon[lat_lon_index + 1:].strip()
            latitude = float(lat_str)
            longitude = float(lon_str)

            geojson_file_names = water_type_name_map.get('Oceans'.lower(), None)
            if geojson_file_names is None:
                return info_by_lat_lon

            water_name = None
            file_name = None
            min_distance = float('inf')
            min_distance_degree = float('inf')
            for geojson_file_name in geojson_file_names:
                enum_item = file_name_map.get(geojson_file_name, None)
                if enum_item is None:
                    continue
                fields = enum_item.get('fields', None)
                if fields is None:
                    continue

                dict_file = os.path.join(geojson_dir, geojson_file_name + '.geojson')
                if not os.path.exists(dict_file):
                    logger.error(f"文件不存在: {dict_file}")
                    continue
                location_info = find_nearest_by_gpd(latitude, longitude, dict_file, True)
                if location_info is not None:
                    min_distance_curr = location_info['distance_meters']
                    min_distance_curr_degree = location_info['distance_degree']
                    water_name_curr = str_val(location_info, fields[1], False)
                    if water_name_curr:
                        if water_name_curr.upper() == 'NODATA':
                            continue

                        if water_name:
                            if min_distance <= 0:
                                # 若经纬度已经包含在一个区域内，则优先取geojson_files靠前的数据
                                continue

                            if min_distance_curr < min_distance:
                                water_name = water_name_curr
                                file_name = geojson_file_name
                                min_distance = min_distance_curr
                                min_distance_degree = min_distance_curr_degree
                        else:
                            water_name = water_name_curr
                            file_name = geojson_file_name
                            min_distance = min_distance_curr
                            min_distance_degree = min_distance_curr_degree

            if water_name is None:
                return info_by_lat_lon

            info_by_lat_lon = {
                'name': water_name,
                'file_name': file_name,
                'search_type': 'in' if min_distance <= 0 else 'nearest',
                'min_distance_meters': min_distance,
                'min_distance_degree': min_distance_degree,
            }
        except Exception as e:
            logger.exception(f'经纬度查询范围出错：{lat_lon}, msg：{e}')
            info_by_lat_lon = None

    return info_by_lat_lon


def all_correct(arr=[]):
    for e in arr:
        if e == '' or e == unspecified_val:
            return False
    return True


# def set_unspecified_columns(df: pd.DataFrame, index):
#     for column in unspecified_columns:
#         df.at[index, column] = unspecified_val


def add_ocean_error_info():
    """
    若水体类型为海洋，判断经纬度离海洋距离超过3.0度，
    添加'ocean_distance_degree'和'ocean_distance_error'列
    """
    water_type_name_map = get_water_type_name_map()
    file_name_map = get_file_name_map()

    # 海洋枚举配置
    ocean_enum_item = file_name_map.get('ocean', None)
    if ocean_enum_item is None:
        raise ValueError('枚举file_name错误：ocean')
    ocean_enum_water_type = ocean_enum_item.get('water_type', None)
    if ocean_enum_water_type is None:
        raise ValueError('water_type不存在')

    ocean_enum_water_type = [s.lower() for s in ocean_enum_water_type]
    logger.info(f'ocean_enum_water_type:{ocean_enum_water_type}')

    df = load_data(input_filename_merge)

    ocean_distance_degree_arr = []
    ocean_distance_error_arr = []
    for index, row in df.iterrows():
        water_body_type = trim_val(row, 'Water Body Type Processed').lower()
        lat_lon = trim_val(row, 'Lat_lon')

        distance_val = ''
        error_val = ''
        if all_correct([water_body_type, lat_lon]):
            info_by_lat_lon = find_lat_lon_distance_to_oceans(lat_lon, water_type_name_map, file_name_map)
            if info_by_lat_lon is not None:
                ocean_distance_degree = str_val(info_by_lat_lon, 'min_distance_degree')
                if ocean_distance_degree is not None:
                    dis_val = float(ocean_distance_degree)
                    if water_body_type in ocean_enum_water_type:
                        if dis_val > 3.0:
                            # set_unspecified_columns(df, index)
                            error_val = 'not ocean'
                    else:
                        if dis_val < 0:
                            # set_unspecified_columns(df, index)
                            error_val = 'not InLand'

                    distance_val = ocean_distance_degree
                else:
                    distance_val = ''
            else:
                distance_val = ''
        ocean_distance_degree_arr.append(distance_val)
        ocean_distance_error_arr.append(error_val)

    logger.info('处理完成，开始写入文件')
    # write_result_data(df, input_filename_merge, output_filename_suffix)

    df['ocean_distance_degree'] = ocean_distance_degree_arr
    df['ocean_distance_error'] = ocean_distance_error_arr

    output_file = write_result_data(df, input_filename_merge, 'ocean_error')
    logger.info(f'处理完成, 结果文件：{output_file}')


def prj_merge_key(row, prj_id: str = ''):
    if prj_id == '-' or len(prj_id) == 0:
        return 'run_id_' + trim_val(row, 'RunID')

    the_col_vals = []
    for column in prj_merge_columns_arr:
        the_col_vals.append(trim_val(row, column))

    return '_@_'.join(the_col_vals)


@time_stat(logger)
def fill_total_excel():
    logger.info('开始执行总表回填脚本')
    # 读取Excel文件
    logger.info(f'开始读取合并文件: {input_filename_merge}')
    df = load_data(input_filename_merge)
    columns_of_merge = df.columns

    # river_water_types = get_water_types_by_file_name('ne_final_rivers')
    map_info = {}
    for index, row in df.iterrows():
        prj_id = trim_val(row, 'BioprojectID')
        key_str = prj_merge_key(row, prj_id)

        # type_val = str_val(row, 'search_type_by_lat_lon', False)
        # if type_val == 'in':
        #     water_name = str_val(row, 'Water Body Name Processed', False)
        #     water_type = str_val(row, 'Water Body Type Processed', False)
        # if water_name == unspecified_val and water_type not in river_water_types:
        #     row['Water Body Name Processed'] = str_val(row, 'name_by_lat_lon', False)

        map_info[key_str] = row

    logger.info(f'项目合并数据量：{len(map_info)}')
    df_total = load_data(input_filename_all)

    if is_ant_nest():
        fill_cols = fill_to_total_columns_antnest
    else:
        fill_cols = fill_to_total_columns_node

    for index, row in df_total.iterrows():
        prj_id = trim_val(row, 'BioprojectID')
        key_str = prj_merge_key(row, prj_id)

        merge_item = map_info.get(key_str, None)
        if merge_item is None:
            continue
        if index % 2000 == 0:
            logger.info(f'已处理行数：{index}')
        for col_index, col_of_merge in enumerate(fill_cols['cols_of_merge']):
            if col_of_merge not in columns_of_merge:
                continue

            col_of_total = fill_cols['cols_of_total'][col_index]
            # 转换目标列为字符串类型
            # df_total[col_of_total] = df_total[col_of_total].astype(str)
            df_total.at[index, col_of_total] = str_val(merge_item, col_of_merge, False)

    logger.info(f'已处理完毕，开始写入文件')
    output_file = write_result_data(df_total, input_filename_all, back_filling_plus_name)
    logger.info(f"数据回填完成，已保存到 {output_file}")


if __name__ == '__main__':
    fill_total_excel()
