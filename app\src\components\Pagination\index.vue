<template>
  <div :class="{ hidden: hidden }" class="pagination">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :background="background"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      :teleported="false"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <span> Total {{ formatNumber(total) }} </span>
    </el-pagination>
  </div>
</template>

<script setup>
  import { scrollTo } from '@/utils/scroll-to';
  import { formatNumber } from '@/utils';
  import { computed } from 'vue';

  const props = defineProps({
    total: {
      required: true,
      type: Number,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 20,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50];
      },
    },
    // 移动端页码按钮的数量端默认值5
    pagerCount: {
      type: Number,
      default: document.body.clientWidth < 992 ? 5 : 7,
    },
    layout: {
      type: String,
      default: 'slot, sizes, prev, pager, next, jumper',
    },
    background: {
      type: Boolean,
      default: true,
    },
    autoScroll: {
      type: Boolean,
      default: true,
    },
    hidden: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits();
  const currentPage = computed({
    get() {
      return props.page;
    },
    set(val) {
      emit('update:page', val);
    },
  });
  const pageSize = computed({
    get() {
      return props.limit;
    },
    set(val) {
      emit('update:limit', val);
    },
  });

  function handleSizeChange(val) {
    if (currentPage.value * val > props.total) {
      currentPage.value = 1;
    }
    emit('pagination', { page: currentPage.value, limit: val });
    if (props.autoScroll) {
      scrollTo(0, 800);
    }
  }

  function handleCurrentChange(val) {
    emit('pagination', { page: val, limit: pageSize.value });
    if (props.autoScroll) {
      scrollTo(0, 800);
    }
  }
</script>

<style scoped lang="scss">
  .pagination {
    background: #fff;
    margin-top: 20px !important;
    margin-bottom: 10px;

    .el-pagination {
      justify-content: center;
    }
  }

  .pagination.hidden {
    display: none;
  }
</style>
