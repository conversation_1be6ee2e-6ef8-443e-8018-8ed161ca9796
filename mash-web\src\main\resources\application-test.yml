spring:
  data:
    mongodb:
      uri: *******************************************************************
      auto-index-creation: true
  elasticsearch:
    uris: http://dev-es8-svc:9200
    socket-keep-alive: true
  ai:
    embedding:
      transformer:
        enabled: true
        onnx:
          model-uri: file:/data/mash/data/sentence-transformers/bce-embedding-base_v1-onnx/model.onnx
          model-output-name: token_embeddings
        tokenizer:
          uri: file:/data/mash/data/sentence-transformers/bce-embedding-base_v1-onnx/tokenizer.json
          options:
            maxLength: 512
            modelMaxLength: 512


logging:
  level:
    org.springframework.data.elasticsearch: warn
    org.elasticsearch.client: warn


app:
  data-home: "/data/mash/data"
  # 统计页面验证token
  stat-token: "rPMJCJJTpOPQY94"
  # Dify api请求地址
  dify-url: "https://www.biosino.org/radiation/v1"
  # Dify api密钥
  dify-token: "app-YRxePT4sUPjn3VWFW6khXyvl"
  # milvus知识库请求地址
  knowledge-url: "https://www.biosino.org/radiation/zsk"
  # milvus知识库名称
  knowledge-id: "mash_doc_milvus"
  # milvus知识库密钥
  knowledge-token: "lrj_test_token_2025"
  # 专家问答收件人管理员邮箱
  admin-email: "<EMAIL>"
  # 专家问答邮件抄送邮箱
  cc-emails:
    - "<EMAIL>"
  node:
    endpoint: https://dev.biosino.org/keep-node2/prod-api/api
    api-token: d08779ddd3a944aa17ad0f20fb256547
    get-experiment-metadata-url: ${app.node.endpoint}/metadata/experiment/list
    save-request-data-url: ${app.node.endpoint}/node/resourceAuthorize/saveBatch
    get-metadata-url: ${app.node.endpoint}/metadata/{}/list
  antnest:
    admin-email: <EMAIL>
