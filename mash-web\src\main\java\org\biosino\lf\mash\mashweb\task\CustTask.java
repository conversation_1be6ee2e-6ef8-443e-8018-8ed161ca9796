package org.biosino.lf.mash.mashweb.task;

import co.elastic.clients.elasticsearch._types.query_dsl.MatchAllQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.es.entity.MashArticleEs;
import org.biosino.lf.mash.mashweb.service.EsService;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustTask {
    private final EmbeddingModel embeddingModel;
    private final ElasticsearchTemplate elasticsearchTemplate;

    /**
     * 添加定期执行的预热查询，保持缓存活跃：
     * 每10分钟执行一次
     */
    @Scheduled(fixedDelay = (300000 * 2L))
    public void warmupQueries() {
        log.info("开始执行预热查询...");

        // 获取索引操作对象
        IndexOperations indexOps = elasticsearchTemplate.indexOps(MashArticleEs.class);
        // 检查索引是否存在
        if (!indexOps.exists()) {
            log.error("索引 {} 不存在，请先创建索引", MashArticleEs.ES_INDEX_NAME);
            return;
        }

        // 先检查索引状态
        checkIndexStatus();

        // 使用更有含义的文本生成查询向量
        final List<Float> queryVector = EsService.normalizeVectorList(embeddingModel, "medical research genomics");
//        final List<Float> queryVector = EsService.normalizeVectorList(embeddingModel, "An S-methyltransferase that produces the climate-active gas dimethylsulfide is widespread across diverse marine bacteria");

        // 使用KNN查询替代脚本评分
        final Query knnTitleQuery = QueryBuilders.knn(fn -> fn
                .field("title_vector")
                .queryVector(queryVector)
                .k(10)  // 减少k值
                .numCandidates(100)  // 减小候选数
                .similarity(0.3f));  // 降低相似度阈值

        NativeQuery query = new NativeQueryBuilder()
                .withQuery(knnTitleQuery)
                .withPageable(PageRequest.of(0, 5))
                .build();

        // 执行查询并添加日志
        SearchHits<MashArticleEs> searchHits = elasticsearchTemplate.search(query, MashArticleEs.class);

        log.info("预热查询完成，找到结果数量: {}", searchHits.getTotalHits());
        /*if (searchHits.getTotalHits() == 0) {
            log.warn("预热查询未找到任何结果，请检查索引中是否有数据");
        } else {
            searchHits.getSearchHits().forEach(hit -> {
                log.info("找到文档: ID={}, 标题={}, 得分={}",
                        hit.getContent().getPmid(),
                        hit.getContent().getTitle(),
                        hit.getScore());
            });
        }*/
    }

    /**
     * 检查索引状态，验证索引中是否有数据
     */
    private void checkIndexStatus() {
        try {
            // 计算索引中的文档数量
            // 创建匹配所有文档的查询
            Query matchAllQuery = new MatchAllQuery.Builder().build()._toQuery();
            NativeQuery countQuery = new NativeQueryBuilder()
                    .withQuery(matchAllQuery)
                    .build();
            long count = elasticsearchTemplate.count(countQuery, MashArticleEs.class);
            log.info("索引 {} 中的文档总数: {}", MashArticleEs.ES_INDEX_NAME, count);

            if (count == 0) {
                log.warn("索引中没有文档，请先调用 EsService.initEs() 初始化索引数据");
            }
        } catch (Exception e) {
            log.error("检查索引状态时发生错误", e);
        }
    }
}
