package org.biosino.lf.mash.mashweb.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 */
@Data
@Document(indexName = MashArticleEs.ES_INDEX_NAME, createIndex = false)
@Setting(settingPath = "es/es_setting.json")
@Mapping(mappingPath = "es/es_mapping.json")
public class MashArticleEs {
    public static final String ES_INDEX_NAME = "mash_article_es";

    @Id
    private String id;

    private Long pmid;

//    private String language;

    private String title;

    @Field(name = "published_year")
    private Integer publishedYear;

    private Integer year;

    private String volume;

    private String issue;

    private String author;

    private String doi;

    @Field(name = "journal_title")
    private String journalTitle;


    @Field(name = "article_abstract")
    private String articleAbstract;

    @Field("node_ids")
    private List<String> nodeIds;

    @Field("ncbi_sra_ids")
    private List<String> ncbiSraIds;

    @Field(name = "bio_projects_count")
    private Integer bioProjectsCount = 0;

    @Field(name = "create_date", type = FieldType.Date)
    private Date createDate;

    @Field(name = "es_update_date", type = FieldType.Date)
    private Date esUpdateDate;

    @Field(name = "title_vector", type = FieldType.Dense_Vector)
    private float[] titleVector;

    @Field(name = "abstract_vectors", type = FieldType.Nested)
    private List<TextAndVectors> abstractVectors;

    @Field(name = "content_vectors", type = FieldType.Nested)
    private List<TextAndVectors> contentVectors;

    @Data
    public static class TextAndVectors {
        @Field(type = FieldType.Text)
        private String text;
        @Field(type = FieldType.Dense_Vector)
        private float[] vector;
    }

}
