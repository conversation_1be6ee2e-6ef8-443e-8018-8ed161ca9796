<template>
  <div>
    <div :id="props.id" :style="{ width: width, height: height }"></div>
    <div id="container">
      <div id="info"></div>
      <div id="echart" style="width: 100%; height: 100%"></div>
    </div>
  </div>
</template>

<script setup>
  import L from 'leaflet';
  import 'leaflet.markercluster/dist/MarkerCluster.css';
  import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
  import 'leaflet.markercluster';
  import { defineProps, nextTick, onMounted, reactive, ref } from 'vue';
  import mapResult from '../assets/js/map';
  import * as echarts from 'echarts';

  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    height: {
      type: String,
      default: '600px',
    },
    width: {
      type: String,
      default: '100%',
    },
    data: {
      type: Array,
    },
    xAxis: {
      type: Array,
    },
    lat: {
      type: String,
      default: '0',
    },
    lng: {
      type: String,
      default: '0',
    },
  });
  onMounted(() => {
    var tiles = L.tileLayer(
      '//server.arcgisonline.com/ArcGIS/rest/services/Ocean/World_Ocean_Base/MapServer/tile/{z}/{y}/{x}',
      {
        maxZoom: 10,
        attribution: 'Tiles &copy; Esri',
      },
    );

    var map = L.map(props.id, {
      center: L.latLng(props.lat, props.lng),
      zoom: 2,
      minZoom: 2, // 设置最小缩放级别为 10
      layers: [tiles],
      zoomControl: true,
      attributionControl: false,
    });

    mapResult.forEach((sample, index) => {
      if (index <= 1000) {
        const abundance = generateRandomAbundance().toFixed(5);
        const size = getSize(abundance);
        const marker = L.circleMarker([sample.latitude, sample.longitude], {
          radius: size, // 根据丰度设置圆的大小
          fillColor: 'orange ',
          color: '#EAF2FA',
          weight: 1,
          opacity: 1,
          fillOpacity: 0.7,
        }).addTo(map);

        //丰度
        const seriesData = [];
        const abundancePercent = (abundance * 100).toFixed(5);
        seriesData.push(abundancePercent);

        const chartDivId = `marker-${index}`;
        const chartDiv = document.createElement('div');
        chartDiv.id = chartDivId;
        chartDiv.style.width = '350px';
        chartDiv.style.height = '350px';

        const popupContentDiv = document.createElement('div');

        popupContentDiv.innerHTML = `
    Latitude: ${sample.latitude}<br>
    Longitude: ${sample.longitude}<br>
`;
        popupContentDiv.appendChild(chartDiv);
        marker.bindTooltip(popupContentDiv, {
          opacity: 1,
        });

        // 初始化ECharts

        // 绑定工具提示
        // // 鼠标悬浮事件
        marker.on('mouseover', function (e) {
          this.setStyle({
            fillColor: 'red',
          });
          const myChart = echarts.init(document.getElementById(chartDivId));

          // 定义ECharts图表数据
          const option = {
            tooltip: {},
            gride: {
              left: '10',
            },
            xAxis: {
              data: ['SRR7648270'],
              axisLabel: {
                fontSize: 16,
              },
            },
            yAxis: {
              name: 'Abundance (%)',
              nameTextStyle: {
                fontSize: 16,
                padding: [0, 0, 0, 50],
              },
            },
            series: [
              {
                name: 'Abundance',
                type: 'bar',
                data: seriesData,
                label: {
                  show: true,
                  position: 'top',
                },
              },
            ],
          };
          myChart.setOption(option);
        });

        marker.on('mouseout', function () {
          this.setStyle({
            fillColor: 'orange',
          });
        });
      }
    });
  });
  function generateRandomAbundance() {
    const min = 0.0000001;
    const max = 0.01;
    return Math.random() * (max - min) + min; // 保留四位小数
  }

  function getSize(abundance) {
    let size = null;
    if (
      abundance < 0.000001 ||
      (abundance >= 0.000001 && abundance < 0.00001)
    ) {
      size = 4;
    }
    if (abundance >= 0.00001 && abundance < 0.0001) {
      size = 6;
    }
    if (abundance >= 0.0001 && abundance < 0.001) {
      size = 8;
    }
    if (abundance >= 0.001 && abundance < 0.01) {
      size = 10;
    }
    if (abundance >= 0.01 && abundance < 0.1) {
      size = 12;
    }
    if (abundance >= 0.1 && abundance < 1) {
      size = 14;
    }
    if (abundance > 1) {
      size = 16;
    }
    return size;
  }
</script>

<style lang="scss" scoped>
  .leaflet-container {
    background: #ffffff;
    outline-offset: 1px;
  }
  #container {
    width: 400px;
    height: 400px;
    position: absolute;
    display: none; /* 初始隐藏 */
    z-index: 1000;
    border: 1px solid #ccc;
    background-color: white;
    padding: 10px;
  }
  #info {
    font-size: 18px;
    color: #666666;
  }
</style>
