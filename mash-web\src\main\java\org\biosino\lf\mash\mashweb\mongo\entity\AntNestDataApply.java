package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * Ant Nest数据申请记录实体类
 */
@Data
@Document(collection = "ant_nest_data_apply")
public class AntNestDataApply {

    /**
     * 申请ID
     */
    @Id
    private String id;

    /**
     * 研究标题
     */
    private String title;

    /**
     * 申请人姓名
     */
    private String name;

    /**
     * 申请人单位/机构
     */
    private String institution;

    /**
     * 申请人邮箱
     */
    private String email;

    /**
     * 申请说明/请求文本
     */
    @Field("request_text")
    private String requestText;

    /**
     * 申请的项目ID列表
     */
    @Field("run_ids")
    private List<String> runIds;

    /**
     * 创建时间
     */
    @Field("create_time")
    private Date createTime;
}
