import { ref, computed, getCurrentInstance } from 'vue';
import { defineStore } from 'pinia';

// 购物车store
export const useCartStore = defineStore(
  'cart',
  () => {
    // 获取当前实例
    const { proxy } = getCurrentInstance();

    // 购物车分组数据
    const cartGroups = ref([]);

    // 添加分组到购物车
    function addGroup(groupName, runIds) {
      // 检查分组名是否已存在
      const existingGroup = cartGroups.value.find(group => group.name === groupName);

      if (existingGroup) {
        // 如果分组已存在，合并runIds
        const newRunIds = runIds.filter(id => !existingGroup.runIds.includes(id));
        existingGroup.runIds.push(...newRunIds);
        existingGroup.runCount = existingGroup.runIds.length;
        proxy.$modal.msgSuccess(`Added ${newRunIds.length} new items to existing group "${groupName}"`);
      } else {
        // 创建新分组
        const newGroup = {
          id: Date.now(),
          name: groupName,
          runIds: [...runIds],
          runCount: runIds.length,
          createTime: new Date().toISOString()
        };
        cartGroups.value.push(newGroup);
        proxy.$modal.msgSuccess(`Created new group "${groupName}" with ${runIds.length} items`);
      }
    }

    // 删除分组
    function removeGroup(groupId) {
      const index = cartGroups.value.findIndex(group => group.id === groupId);
      if (index > -1) {
        const groupName = cartGroups.value[index].name;
        cartGroups.value.splice(index, 1);
        proxy.$modal.msgSuccess(`Removed group "${groupName}" from cart`);
      }
    }

    // 获取所有分组名称（用于下拉选择）
    const getGroupNames = computed(() => {
      return cartGroups.value.map(group => group.name);
    });

    // 根据分组名称获取runIds
    function getRunIdsByGroupNames(groupNames) {
      const allRunIds = [];
      groupNames.forEach(groupName => {
        const group = cartGroups.value.find(g => g.name === groupName);
        if (group) {
          allRunIds.push(...group.runIds);
        }
      });
      return [...new Set(allRunIds)]; // 去重
    }

    // 获取购物车总数量
    const getTotalCount = computed(() => {
      return cartGroups.value.reduce((total, group) => total + group.runCount, 0);
    });

    // 清空购物车
    function clearCart() {
      cartGroups.value.splice(0, cartGroups.value.length);
      proxy.$modal.msgSuccess('Cart cleared successfully');
    }

    // 获取分组详情
    function getGroupById(groupId) {
      return cartGroups.value.find(group => group.id === groupId);
    }

    // 更新分组名称
    function updateGroupName(groupId, newName) {
      const group = cartGroups.value.find(g => g.id === groupId);
      if (group) {
        // 检查新名称是否已存在
        const nameExists = cartGroups.value.some(g => g.id !== groupId && g.name === newName);
        if (nameExists) {
          proxy.$modal.msgError(`Group name "${newName}" already exists`);
          return false;
        }
        group.name = newName;
        proxy.$modal.msgSuccess('Group name updated successfully');
        return true;
      }
      return false;
    }

    // 从分组中移除特定的runIds
    function removeRunIdsFromGroup(groupId, runIdsToRemove) {
      const group = cartGroups.value.find(g => g.id === groupId);
      if (group) {
        group.runIds = group.runIds.filter(id => !runIdsToRemove.includes(id));
        group.runCount = group.runIds.length;

        // 如果分组为空，删除整个分组
        if (group.runIds.length === 0) {
          removeGroup(groupId);
        } else {
          proxy.$modal.msgSuccess(`Removed ${runIdsToRemove.length} items from group "${group.name}"`);
        }
      }
    }

    return {
      cartGroups,
      addGroup,
      removeGroup,
      getGroupNames,
      getRunIdsByGroupNames,
      getTotalCount,
      clearCart,
      getGroupById,
      updateGroupName,
      removeRunIdsFromGroup
    };
  },
  {
    persist: {
      enabled: true,
      strategies: [
        {
          storage: localStorage,
          paths: ['cartGroups'],
        },
      ],
    },
  },
);
