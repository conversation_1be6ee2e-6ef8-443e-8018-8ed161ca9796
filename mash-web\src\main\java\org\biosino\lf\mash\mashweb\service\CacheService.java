package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.mongo.KEGGEnergyMetabolism;
import org.biosino.lf.mash.mashweb.util.kegg.KeggEntry;
import org.biosino.lf.mash.mashweb.util.kegg.KeggParser;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2025/7/22
 */
@Service
@RequiredArgsConstructor
@Data
public class CacheService {

    private final MongoTemplate mongoTemplate;

    private final FileProperties fileProperties;

    public static Map<String, String> pathKoPathNameMap = new HashMap<>();

    public static List<KeggEntry> keggEntries = new ArrayList<>();

    public static Map<String, List<KeggEntry>> pathToKeggEntryMap = new LinkedHashMap<>();

    public static Map<String, List<String>> domainLevelSpecieNames = new LinkedHashMap<>();

    @PostConstruct
    public void init() {
        loadPathKOAndPathName();
        loadKeggEntries();
        loadDomainLevelSpecieNames();
    }

    private void loadPathKOAndPathName() {
        List<KEGGEnergyMetabolism> list = mongoTemplate.find(new Query(), KEGGEnergyMetabolism.class);
        for (KEGGEnergyMetabolism item : list) {
            pathKoPathNameMap.put(item.getOrthologyEntry(), item.getKoSymbol() + "; " + item.getOrthologyName());
        }
    }

    private void loadKeggEntries() {
        try {
            keggEntries = KeggParser.parseKeggFile(FileUtil.file(fileProperties.getBaseDataDir(), "ko00001.keg"));
            // 分组
            pathToKeggEntryMap = keggEntries.stream().collect(Collectors.groupingBy(KeggEntry::getLevelC, LinkedHashMap::new, Collectors.toList()));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private void loadDomainLevelSpecieNames() {
        ArrayList<String> domains = CollUtil.newArrayList("A", "B", "E", "V");
        ArrayList<String> taxonomys = CollUtil.newArrayList("C", "F", "G", "O", "P", "S");
        for (String domain : domains) {
            for (String taxonomy : taxonomys) {
                File file = FileUtil.file(fileProperties.getBaseDataDir(), "Abundance_Table", domain + "." + taxonomy + ".percent.csv");
                CsvReader reader = CsvUtil.getReader(CsvReadConfig.defaultConfig().setContainsHeader(true));
                CsvData csvData = reader.read(file);
                List<String> distinct = csvData.getRows().stream().map(x -> x.get(0)).collect(Collectors.toList());
                domainLevelSpecieNames.put(domain + taxonomy, distinct);
            }
        }
    }


}
