package org.biosino.lf.mash.mashweb.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ScholarlyArchiveQueryDTO extends BaseQuery {

    private String searchKeyword;

    @NotBlank(message = "The search term cannot be empty")
    private String searchField;

    private String sortKey;

    //是否点击搜索按钮，用于判断是否需要更新热门搜索日志记录
    private String searchBtnFlag;

}
