package org.biosino.lf.mash.mashweb.mongo.repository;

import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface SamplesRepository extends MongoRepository<Samples, String>, SamplesCustomRepository {
    List<Samples> findAllByIdInAndDataSource(List<String> ids, String dataSource);

    List<Samples> findByRunIdIn(Collection<String> runIds);
}
