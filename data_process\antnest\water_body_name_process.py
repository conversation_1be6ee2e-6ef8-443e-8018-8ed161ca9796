# -*- coding: utf-8 -*-

"""
# 水体名称治理，通过经纬度定位geojson方案
# Author: 尚尉
# Date: 2024-12-12
# Version: 1.0.0
"""

import json
import os
import re

import geopandas as gpd
import numpy as np
import pandas as pd
from shapely.geometry import shape, Point

from cust_utils import time_stat, load_data, write_result_data, str_val, trim_val
from custom_log_config import init_default_logger
from process_file_config import get_curr_process_item
from water_type_and_geojson_config import geojson_files
from z_test_unit import excel_to_csv

# filename = "原始MASH-OCEAN-v2元数据治理_补充地理信息_new.xlsx"
# filename = "MASH-NODE-Samples_merge.csv"
# 获取当前配置项
curr_process_item = get_curr_process_item()
# 输入文件
filepath = curr_process_item.get('main_process_input_filepath')

# 字典数据Excel目录
geojson_dir = 'data/geojson'
dict_excel_dir = 'data/geojson_name_dict'
# 扩展文件后缀(.csv)
dict_ext_name = '.csv'

# 经纬度距离计算时，是否搜索最近的水体距离开关
search_nearest_flag = True
# 是否计算投影坐标系下经纬度距离（单位：米）,默认不计算，提高效率
min_distance_meters_flag = False

# 配置日志记录
# 创建一个自定义的日志记录器
log_file_name = 'water_body_name_process.log'
logger = init_default_logger(log_file_name)

regions_gdf_global_map = {}
regions_gdf_global_map_3857 = {}


def init_dict_item(pd_row):
    return {
        'type': str_val(pd_row, 'type', False),
        'name': str_val(pd_row, 'name', False),
        'name_zh': str_val(pd_row, 'name zh', False),
        'source_file': str_val(pd_row, 'source file', False),
    }


def read_dict():
    # 从字典Excel中统计名称出现次数
    # count_dict = defaultdict(int)
    dict_map = {}
    for geo_item in geojson_files:
        geojson_file_name = geo_item.get('file_name', None)
        if geojson_file_name is None:
            logger.error("geojson_file_name is None")
            continue
        geojson_file_name = geojson_file_name + dict_ext_name
        # csv文件路径
        dict_file = os.path.join(dict_excel_dir, geojson_file_name)
        df = load_data(dict_file)

        for index, row in df.iterrows():
            geo_loc_name = str_val(row, 'name', True)
            if geo_loc_name is not None:
                map_val = dict_map.get(geo_loc_name, None)
                if map_val:
                    map_val['item'] = init_dict_item(row)
                    c = map_val.get('count', None)
                    if c is not None:
                        map_val['count'] = c + 1
                    else:
                        map_val['count'] = 1
                else:
                    map_val = {
                        'item': init_dict_item(row),
                        'count': 1,
                    }
                dict_map[geo_loc_name] = map_val
                # count_dict[geo_loc_name] += 1

    # 取出唯一名称结果集合
    unique_dicts = []
    # mult_names = set()
    for key in dict_map:
        map_val = dict_map[key]
        # 去除出现重复的字典数据
        if map_val['count'] == 1:
            unique_dicts.append(map_val['item'])
        # else:
        #     mult_names.add(element)
    return unique_dicts


def contains_whole_word(text, word):
    if word is None:
        return False
    # 创建正则表达式模式，使用 \b 表示单词边界
    pattern = r'\b' + re.escape(word) + r'\b'
    # 使用 re.search 查找匹配
    return re.search(pattern, text) is not None


# 检查点是否在某个区域内
def find_location_in_geojson(geojson_data, latitude, longitude):
    point = Point(longitude, latitude)  # 注意经纬度的顺序：经度在前，纬度在后
    if not point.is_valid or point.is_empty:
        logger.error(f'点对象无效:{longitude}, {latitude}')
        return None

    # 遍历所有数据
    for feature in geojson_data['features']:
        polygon = shape(feature['geometry'])
        if polygon.contains(point):
            # return feature['properties']
            return {
                'prop': feature['properties'],
            }
    return None  # 未找到匹配的区域


def find_nearest_region(geojson_data, latitude, longitude):
    # 注意经纬度的顺序：经度在前，纬度在后
    point = Point(longitude, latitude)
    if not point.is_valid or point.is_empty:
        logger.error(f'点对象无效:{longitude}, {latitude}')
        return None
    nearest_feature = None
    min_distance = float('inf')

    # 遍历所有数据
    for feature in geojson_data['features']:
        polygon = shape(feature['geometry'])
        distance = point.distance(polygon)
        if distance < min_distance:
            min_distance = distance
            nearest_feature = feature

    result = None
    if nearest_feature:
        result = {
            'prop': nearest_feature['properties'],  # 返回最近区域的属性信息
            'min_distance': min_distance,
        }
    return result


def load_geojson(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        geojson_data = json.load(f)
    return geojson_data


def validate_lat_lon(latitude: float, longitude: float):
    if np.isnan(latitude).any() or np.isnan(latitude).any():
        raise ValueError(f"Input data contains NaN values:{latitude}, {longitude}")
    if np.isinf(latitude).any() or np.isinf(latitude).any():
        raise ValueError(f"Input data contains infinite values:{latitude}, {longitude}")
    if latitude > 90 or latitude < -90 or longitude > 180 or longitude < -180:
        raise ValueError(f'经纬度超出范围:{latitude}, {longitude}')


def init_gpd_data(geojson_path: str, input_regions_gdf):
    """
            EPSG:3857
                类型：投影坐标系（由于投影变形，不同区域需要使用不同的投影坐标系，才能准确计算出距离。3857比较常用）
                用途：广泛用于Web地图服务（如Google Maps、OpenStreetMap等）
                特点：将地球表面投影到一个平面，适用于距离、面积等计算。但由于投影变形，高纬度地区的距离和面积计算会有较大误差。
                单位：米
            EPSG:4326
                类型：地理坐标系
                用途：常用作全球定位系统的标准坐标系
                特点：使用经纬度表示位置，适合表示地理位置，但不适合直接进行距离和面积计算。
                单位：度
        """
    regions_gdf = None
    if input_regions_gdf is not None and not input_regions_gdf.empty:
        # 不使用投影坐标距离，可以提高效率
        regions_gdf = regions_gdf_global_map_3857.get(geojson_path, None)
        if regions_gdf is None:
            # 转换为EPSG:3857投影坐标系，distance方计算结果单位是米
            regions_gdf = input_regions_gdf.to_crs(epsg=3857)
            regions_gdf_global_map_3857[geojson_path] = regions_gdf
    else:
        regions_gdf = regions_gdf_global_map.get(geojson_path, None)
        if regions_gdf is None:
            # 加载GeoJSON 数据
            regions_gdf = gpd.read_file(geojson_path)
            # 先使用EPSG:4326计算距离（度数）
            if regions_gdf.crs != "EPSG:4326":
                regions_gdf = regions_gdf.to_crs("EPSG:4326")
            regions_gdf_global_map[geojson_path] = regions_gdf

    return regions_gdf


def find_nearest_by_gpd(latitude: float, longitude: float, geojson_path: str, always_near: bool):
    regions_gdf = init_gpd_data(geojson_path, None)

    point = gpd.GeoSeries([Point(longitude, latitude)], crs=regions_gdf.crs)

    properties = None
    in_region = None
    if not always_near:
        # 统计包含在geojson某区域内数据
        in_region = regions_gdf[regions_gdf.contains(point.iloc[0])]

    if in_region is not None and not in_region.empty:
        properties = in_region.iloc[0].to_dict()
        properties["distance_degree"] = 0
        properties["distance_meters"] = 0
    else:
        if always_near or search_nearest_flag:
            # 计算点到每条河流的距离,EPSG:4326地理坐标系下distance方法计算结果单位是度数
            regions_gdf["distance_degree"] = regions_gdf.geometry.distance(point.iloc[0])

            if min_distance_meters_flag:
                # 不使用投影坐标距离，可以提高效率
                # 转换为EPSG:3857投影坐标系，distance方计算结果单位是米
                regions_gdf = init_gpd_data(geojson_path, regions_gdf)
                point = point.to_crs(epsg=3857)
                regions_gdf["distance_meters"] = regions_gdf.geometry.distance(point.iloc[0])

            # 找到距离最近的河流
            nearest_river = regions_gdf.loc[regions_gdf["distance_degree"].idxmin()]
            if not nearest_river.empty:
                properties = nearest_river.to_dict()
                if not min_distance_meters_flag:
                    properties["distance_meters"] = 0 if properties["distance_degree"] == 0 else 1

    return properties


def find_name_by_lat_lon(latitude: float, longitude: float, water_body_type: str):
    if latitude is None or longitude is None or pd.isnull(latitude) or pd.isnull(longitude):
        return None

    validate_lat_lon(latitude, longitude)

    water_name = None
    file_name = None
    min_distance = float('inf')
    min_distance_degree = float('inf')
    for geo_item in geojson_files:
        geojson_file_name = geo_item.get('file_name', None)
        # 按照最近地理位置查找时排除ocean和glwd_lake_1
        if geojson_file_name is None:
            logger.debug(f"geojson_file_name is invalidated: {geojson_file_name}")
            continue

        fields = geo_item.get('fields', None)
        water_type = geo_item.get('water_type', None)
        always_near = False
        if water_type is not None and len(water_type) > 0 and water_body_type is not None and len(water_body_type) > 0:
            has_type = False
            for type_item in water_type:
                type_lower = water_body_type.lower()
                if type_item.lower() == type_lower:
                    always_near = type_lower == 'river'
                    has_type = True
                    break
            if not has_type:
                continue

        geojson_file_name = geojson_file_name + '.geojson'
        # excel文件路径
        dict_file = os.path.join(geojson_dir, geojson_file_name)
        if not os.path.exists(dict_file):
            logger.error(f"文件不存在: {dict_file}")
            continue

        # geojson_data = load_geojson(dict_file)
        location_info = find_nearest_by_gpd(latitude, longitude, dict_file, always_near)
        if location_info:
            min_distance_curr = location_info['distance_meters']
            min_distance_curr_degree = location_info['distance_degree']
            water_name_curr = str_val(location_info, fields[1], False)
            if water_name_curr:
                if water_name_curr.upper() == 'NODATA':
                    continue

                if water_name:
                    if min_distance <= 0:
                        # 若经纬度已经包含在一个区域内，则优先取geojson_files靠前的数据
                        continue

                    if min_distance_curr < min_distance:
                        water_name = water_name_curr
                        file_name = geojson_file_name
                        min_distance = min_distance_curr
                        min_distance_degree = min_distance_curr_degree
                else:
                    water_name = water_name_curr
                    file_name = geojson_file_name
                    min_distance = min_distance_curr
                    min_distance_degree = min_distance_curr_degree

    if water_name is None:
        return None

    return {
        'name': water_name,
        'file_name': file_name,
        'search_type': 'in' if min_distance <= 0 else 'nearest',
        'min_distance_meters': min_distance,
        'min_distance_degree': min_distance_degree,
    }


def replace_he(val_str: str):
    if val_str is None:
        return None
    return re.sub(r'\b(\w+)he\b', r'\1', val_str, flags=re.IGNORECASE)


def filter_result_file(df: pd.DataFrame) -> pd.DataFrame:
    # 距离超过0.3度的数据为无效数据，清空对应经纬度检索字段
    threshold_val = 0.3
    logger.info(f"开始清理无效数据，阈值：{threshold_val}")

    # 确保'min_distance_degree'列存在
    if 'min_distance_degree' not in df.columns:
        logger.error("'min_distance_degree'列不存在")
        raise KeyError("'min_distance_degree'列不存在")

    # 将'min_distance_degree'列转换为数值型，转换错误设为NaN
    df['min_distance_degree'] = pd.to_numeric(df['min_distance_degree'], errors='coerce')

    # 定义待清理的列
    clean_columns = ['name_by_lat_lon', 'search_type_by_lat_lon']

    # 确保待清理的列存在
    for col in clean_columns:
        if col not in df.columns:
            logger.error(f"列'{col}'不存在")
            raise KeyError(f"列'{col}'不存在")

    # 创建条件掩码，选择'min_distance_degree'大于阈值的行
    mask = df['min_distance_degree'] > threshold_val

    # 将满足条件的行的指定列设为空字符串
    df.loc[mask, clean_columns] = ''
    return df


def mash_excel_to_csv(input_file_name):
    input_dir = os.path.join('data')

    mash_input_file = os.path.join(input_dir, input_file_name)
    input_basename = os.path.basename(mash_input_file)
    name_without_ext, _ext = os.path.splitext(input_basename)
    if _ext == '.xlsx':
        # 将Excel文件转换为CSV文件
        csv_file = os.path.join(input_dir, f'{name_without_ext}{dict_ext_name}')
        excel_to_csv(mash_input_file, csv_file)
        mash_input_file = csv_file
        _ext = dict_ext_name

    output_file = os.path.join(input_dir, f'{name_without_ext}_plus{_ext}')
    return {
        'input': mash_input_file,
        'output': output_file,
    }


def add_dict_info():
    # file_map = mash_excel_to_csv(filepath)
    mash_input_file = filepath
    logger.info(f'输入文件：{os.path.abspath(mash_input_file)}')

    if not os.path.exists(mash_input_file):
        logger.error(f"文件不存在: {mash_input_file}")
        raise FileNotFoundError(f"文件不存在: {mash_input_file}")

    if not os.path.isfile(mash_input_file):
        logger.error(f"该路径不是文件: {mash_input_file}")
        raise IsADirectoryError(f"该路径不是文件: {mash_input_file}")

    df = load_data(mash_input_file)

    # 获取 DataFrame 的长度
    name_by_lat_lon_arr = []
    source_by_lat_lon_arr = []
    search_type_by_lat_lon_arr = []
    min_distance_meters_arr = []
    min_distance_degree_arr = []

    curr_index = 0
    iter_rows = df.iterrows()
    for index, row in iter_rows:
        water_body_type = trim_val(row, "Water Body Type").lower()
        if len(water_body_type) > 0:
            test1 = None

        curr_index += 1
        if curr_index % 200 == 0:
            logger.info(f'已处理行数：{curr_index}')

        # 根据经纬度，查询最近的水体名称
        lat_lon = trim_val(row, 'Lat_lon')
        info_by_lat_lon = None
        if lat_lon != '' and ',' in lat_lon:
            lat_lon_index = lat_lon.index(',')
            try:
                lat_str = lat_lon[:lat_lon_index].strip()
                lon_str = lat_lon[lat_lon_index + 1:].strip()
                info_by_lat_lon = find_name_by_lat_lon(float(lat_str), float(lon_str), water_body_type)
            except Exception as e:
                logger.exception(f'经纬度查询范围出错：{lat_lon}, msg：{e}')
                info_by_lat_lon = None

        if info_by_lat_lon is not None:
            name_by_lat_lon_arr.append(info_by_lat_lon['name'])
            source_by_lat_lon_arr.append(info_by_lat_lon['file_name'])
            search_type_by_lat_lon_arr.append(info_by_lat_lon['search_type'])
            min_distance_meters_arr.append(info_by_lat_lon['min_distance_meters'])
            min_distance_degree_arr.append(info_by_lat_lon['min_distance_degree'])
        else:
            name_by_lat_lon_arr.append(None)
            source_by_lat_lon_arr.append(None)
            search_type_by_lat_lon_arr.append(None)
            min_distance_meters_arr.append(None)
            min_distance_degree_arr.append(None)

    df['name_by_lat_lon'] = name_by_lat_lon_arr
    df['search_type_by_lat_lon'] = search_type_by_lat_lon_arr
    df['min_distance_degree'] = min_distance_degree_arr
    df['source_by_lat_lon'] = source_by_lat_lon_arr
    if min_distance_meters_flag:
        df['min_distance_meters'] = min_distance_meters_arr

    # 清理无效数据
    df = filter_result_file(df)
    # 将数据写入文件
    output_file = write_result_data(df, mash_input_file)
    logger.info(f'所有文件处理完毕，结果文件：{output_file}')


@time_stat(logger)
def main():
    logger.info("开始执行 water_body_name_process.py")

    add_dict_info()

    logger.info("water_body_name_process.py 执行完毕")


if __name__ == '__main__':
    main()
