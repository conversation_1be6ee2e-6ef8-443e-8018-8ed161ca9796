variables:
  GIT_DEPTH: "3"
stages:
  - build
  - deploy
# 打包war包
mvn_build_job:
  stage: build
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/maven:3.9.9-eclipse-temurin-17
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - cd mash-web
    - mvn clean package -Dmaven.test.skip=true -P test
    - rm -rf /project/mash/app/mash-web.jar
    - mkdir -p /project/mash/app
    - mv ./target/mash-web.jar /project/mash/app/mash-web.jar
nodejs_build_job_app:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/node:20.19-alpine3.21
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - rm -rf /node-cloud/node-ui/html/keep-mash/*
    - cd app
    - npm config set registry https://registry.npmmirror.com
    - npm install
    - npm run build:stage
    - mkdir -p /node-cloud/node-ui/html/keep-mash
    - cp -r ./keep-mash/* /node-cloud/node-ui/html/keep-mash/

# 部署
deploy_k8s_job:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/bitnami.kubectl:1.20.15
  stage: deploy
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    # 重启
    - kubectl -n xuqw rollout restart deployment mash-web node-nginx
