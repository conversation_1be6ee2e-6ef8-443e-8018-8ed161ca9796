# -*- coding: utf-8 -*-

"""
Excel数据回填
Author: 尚尉
Date: 2024-12-25
Version: 1.0.0
"""
import json
import os

import pandas as pd
from pymongo import MongoClient
from pymongo.synchronous.collection import Collection

from cust_utils import time_stat, str_val
from custom_log_config import init_default_logger
from find_pmid_by_title import mongo_uri, mongo_db_name, mongo_collection_name

# 初始化日志
logger = init_default_logger('fill_mash_excel.log')

filepath = 'data/水圈项目&论文.xlsx'
new_filepath = 'data/水圈项目&论文_补充plosp数据.xlsx'


def to_int_num(pmid_val) -> int:
    try:
        pmid = int(float(pmid_val))
    except BaseException:
        pmid = None
    return pmid


def process_excel_files(collection: Collection):
    # 检查文件是否存在
    if not os.path.exists(filepath):
        logger.error(f'文件不存在: {filepath}')
        return

    # 读取Excel文件的'名称'列
    df = pd.read_excel(filepath, engine='openpyxl')
    if df.empty:
        logger.error(f'文件内容为空: {filepath}')
        return

    # 打印文件路径和列内容
    logger.info(f'处理文件：{filepath}')

    for index, row in df.iterrows():
        # title = trim_to_none(row['名称'])
        pmid = str_val(row, 'pmid')
        if pmid:
            pmid = to_int_num(pmid)
            print(f'行{index + 1}: {pmid}')
            one_item = collection.find_one({"pmid": pmid})
            if one_item:
                # 更新'名称'列和添加新列
                df.at[index, '名称'] = one_item.get('title')
                df.at[index, 'pmid'] = pmid

                pdf_info = one_item.get('pdf', None)
                has_pdf = pdf_info and pdf_info.get('path', None)
                df.at[index, '是否存在PDF'] = 'YES' if has_pdf else 'NO'

                df.at[index, 'pmcId'] = str_val(one_item, 'pmcId')
                df.at[index, 'doi'] = str_val(one_item, 'doi')
                df.at[index, '期刊名称'] = str_val(one_item, 'journalTitle')
                df.at[index, '卷volume'] = str_val(one_item, 'volume')
                df.at[index, '期号issue'] = str_val(one_item, 'issue')
                df.at[index, '期刊Issn Print'] = str_val(one_item, 'journalIssnPrint')
                df.at[index, '期刊 Issn Electronic'] = str_val(one_item, 'journalIssnElectronic')
                df.at[index, '页码'] = str_val(one_item, 'page')
                df.at[index, '作者'] = str_val(one_item, 'author')
                df.at[index, '关键词'] = str_val(one_item, 'keyword')
                df.at[index, '摘要'] = str_val(one_item, 'abs')
                df.at[index, 'year'] = str_val(one_item, 'year')
                df.at[index, '出版社'] = str_val(one_item, 'publisherTitle')
                df.at[index, 'publisherIoc'] = str_val(one_item, 'publisherIoc')
                year_ifs = one_item.get('year_ifs', None)
                year_ifs_str = ''
                if year_ifs:
                    year_ifs_str = json.dumps(year_ifs)
                df.at[index, '影响因子'] = year_ifs_str

                zky_sections = one_item.get('zky_sections', None)
                zky_sections_str = ''
                if zky_sections:
                    name_cn_list = [item["name_cn"] for item in zky_sections]
                    zky_sections_str = ", ".join(name_cn_list)
                df.at[index, '中科院分区'] = zky_sections_str

    # 保存到新的Excel文件
    df.to_excel(new_filepath, index=False, engine='openpyxl')
    logger.info(f'更新后的文件已保存到: {new_filepath}')


@time_stat(logger)
def fill_excel():
    client = MongoClient(mongo_uri)
    db = client[mongo_db_name]
    collection = db[mongo_collection_name]
    try:
        process_excel_files(collection)
    finally:
        client.close()


if __name__ == '__main__':
    fill_excel()
