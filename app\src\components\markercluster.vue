<template>
  <div>
    <div :id="props.id" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup>
  import L from 'leaflet';
  import 'leaflet.markercluster/dist/MarkerCluster.css';
  import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
  import 'leaflet.markercluster';
  import { defineProps, nextTick, onMounted, reactive, ref } from 'vue';
  import mapResult from '../assets/js/map';

  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    height: {
      type: String,
      default: '600px',
    },
    width: {
      type: String,
      default: '100%',
    },
    data: {
      type: Array,
    },
    xAxis: {
      type: Array,
    },
    lat: {
      type: String,
      default: '0',
    },
    lng: {
      type: String,
      default: '0',
    },
  });
  onMounted(() => {
    var tiles = L.tileLayer(
      '//server.arcgisonline.com/ArcGIS/rest/services/Ocean/World_Ocean_Base/MapServer/tile/{z}/{y}/{x}',
      {
        maxZoom: 10,
        attribution: 'Tiles &copy; Esri',
      },
    );

    var map = L.map(props.id, {
      center: L.latLng(props.lat, props.lng),
      zoom: 2,
      minZoom: 2, // 设置最小缩放级别为 10
      layers: [tiles],
      zoomControl: true,
      attributionControl: false,
    });

    var markers = L.markerClusterGroup({
      showCoverageOnHover: true, // 为true时,当鼠标悬停在点上时，它会显示它聚合的边界
      zoomToBoundsOnClick: true, //  为true时,当鼠标点击某个点时，会缩放到它的边界范围
      chunkedLoading: true,
      maxClusterRadius: 60, // 聚类从中心标记覆盖的最大半径（以像素为单位）,默认值 80
    });

    // 创建分类聚合组
    const clusterGroups = {};

    // 遍历数据并添加标记
    mapResult.forEach(function (item) {
      // 如果还没有这个分类的聚合组，则创建一个
      if (!clusterGroups[item.depth]) {
        const clusterClassName = `mycluster-${item.depth}`; // 例如：mycluster-depth-1, mycluster-depth-2 等
        clusterGroups[item.depth] = L.markerClusterGroup({
          singleMarkerMode: true,
          iconCreateFunction: function (cluster) {
            return L.divIcon({
              html: '<div>' + cluster.getChildCount() + '</div>',
              className: clusterClassName,
            });
          },
          showCoverageOnHover: true, // 为true时,当鼠标悬停在点上时，它会显示它聚合的边界
          zoomToBoundsOnClick: true, //  为true时,当鼠标点击某个点时，会缩放到它的边界范围
          chunkedLoading: true,
          maxClusterRadius: 60, // 聚类从中心标记覆盖的最大半径（以像素为单位）,默认值 80
          // chunkedLoading: true,
          // iconCreateFunction: function (cluster) {
          //   return L.divIcon({
          //     html: '<b>' + cluster.getChildCount() + '</b>',
          //   });
          // },
        });
      }

      // 创建标记并添加到对应的聚合组
      var marker = L.marker(L.latLng(item.latitude, item.longitude));
      var popupContent = `
        Number of samples: 266<br>
        Latitude: ${item.latitude}<br>
        Longitude: ${item.longitude}<br>
    `;

      // 绑定弹出窗口
      // marker.bindPopup(popupContent);

      marker.bindTooltip(popupContent, {
        direction: 'right', // 方向设置为自动
        opacity: 0.9, // 透明度设置为 0.9
        fontSize: 18,
      });
      clusterGroups[item.depth].addLayer(marker);
    });

    // 将所有聚合组添加到地图
    for (const category in clusterGroups) {
      map.addLayer(clusterGroups[category]);
    }
    // 监听缩放结束事件
    map.on('zoomend', function () {
      var currentZoom = map.getZoom(); // 获取当前缩放级别
      console.log('当前缩放级别:', currentZoom);
    });
  });
</script>

<style lang="scss" scoped>
  .leaflet-container {
    background: #ffffff;
    outline-offset: 1px;
  }
</style>
