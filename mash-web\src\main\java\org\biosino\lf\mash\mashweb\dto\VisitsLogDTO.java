package org.biosino.lf.mash.mashweb.dto;

import com.alibaba.fastjson2.JSONObject;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;


/**
 * 访问日志表
 *
 * <AUTHOR>
 */
@Data
public class VisitsLogDTO implements Serializable {
    /**
     * 前端生成随机ID，放在sessionStorage中
     */
    @NotBlank(message = "Session Id cannot be empty")
    @Size(max = 36, message = "Session Id cannot exceed 36 characters")
    private String sessionId;

    /**
     * 访问的URL(当前页面url的上下文之后的部分)
     */
    @NotBlank(message = "URL cannot be empty")
    @Size(max = 50, message = "URL cannot exceed 50 characters")
    private String url;

    /**
     * 访问的功能模块
     */
    @Size(max = 50, message = "Function Module cannot exceed 50 characters")
    private String functionModule;

    /**
     * 从请求头中获取访问的浏览器的User-Agent
     */
    @NotBlank(message = "Browser info cannot be empty")
    @Size(max = 600, message = "Browser info cannot exceed 600 characters")
    private String browser;

    /**
     * 参数
     */
    private Object queryObj;
    private Object paramsObj;
    private Object dataObj;

}
