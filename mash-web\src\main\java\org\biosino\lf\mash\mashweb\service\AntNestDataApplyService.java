package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.dto.AntNestDataApplyDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.AntNestDataApply;
import org.biosino.lf.mash.mashweb.mongo.repository.AntNestDataApplyRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Ant Nest数据申请服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AntNestDataApplyService {

    private final AntNestDataApplyRepository antNestDataApplyRepository;
    private final MailService mailService;

    @Value("${app.antnest.admin-email:<EMAIL>}")
    private String antNestEmail;

    /**
     * 获取AntNest管理员邮箱
     *
     * @return 管理员邮箱
     */
    public String getAdminEmail() {
        return antNestEmail;
    }

    /**
     * 提交Ant Nest数据申请并发送通知邮件
     *
     * @param applyDTO 申请信息
     * @return 申请记录ID
     */

    public String submitApplication(AntNestDataApplyDTO applyDTO) {
        // 创建申请记录
        AntNestDataApply apply = new AntNestDataApply();
        BeanUtils.copyProperties(applyDTO, apply);
        apply.setCreateTime(new Date());

        // 保存到数据库
        AntNestDataApply savedApply = antNestDataApplyRepository.save(apply);

        // 发送邮件通知
        ThreadUtil.execAsync(() -> sendApplicationEmail(apply));

        return savedApply.getId();
    }

    /**
     * 发送申请邮件通知
     *
     * @param apply 申请记录
     */
    private void sendApplicationEmail(AntNestDataApply apply) {
        String subject = "New Ant Nest Data Application";

        // 构建邮件内容
        StringBuilder content = new StringBuilder();
        content.append("<h2>New Ant Nest Data Application</h2>");
        content.append("<p><strong>Title:</strong> ").append(apply.getTitle()).append("</p>");
        content.append("<p><strong>Name:</strong> ").append(apply.getName()).append("</p>");
        content.append("<p><strong>Institution:</strong> ").append(apply.getInstitution()).append("</p>");
        content.append("<p><strong>Email:</strong> ").append(apply.getEmail()).append("</p>");

        // 添加请求文本
        if (apply.getRequestText() != null && !apply.getRequestText().isEmpty()) {
            content.append("<p><strong>Request Text:</strong> ").append(apply.getRequestText()).append("</p>");
        }

        content.append("<p><strong>Applied for AntNestData:</strong> [");
        content.append(String.join(", ", apply.getRunIds()));
        content.append("]</p>");
        content.append("<p><strong>Application Time:</strong> ").append(DateUtil.formatDateTime(apply.getCreateTime())).append("</p>");

        try {
            // 发送HTML邮件
            mailService.sendHtmlMail(antNestEmail, subject, content.toString());
            log.info("AntNest application email sent successfully to {} for application ID: {}", antNestEmail, apply.getId());
        } catch (MessagingException e) {
            log.error("Failed to send AntNest application email for ID: {}", apply.getId(), e);
        }
    }
}
