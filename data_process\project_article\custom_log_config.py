# -*- coding: utf-8 -*-

"""
 日志配置
 Author: 尚尉
 Date: 2024-12-25
 Version: 1.0.0

"""
import logging
import os


def init_default_logger(log_file_name: str, curr_logger: logging.Logger = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)
    # log_output_path = os.path.abspath(log_output_path)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)

    return curr_logger
