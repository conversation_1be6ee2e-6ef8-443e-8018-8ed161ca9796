package org.biosino.lf.mash.mashweb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
@Data
@ConfigurationProperties(prefix = "app.node")
public class NodeProperties {

    private String endpoint;

    private String apiToken;

    private String getExperimentMetadataUrl;

    /**
     * 需要用StrUtil.format填充
     */
    private String getMetadataUrl;

    private String saveRequestDataUrl;
}
