# -*- coding: utf-8 -*-

"""
# 水体geojson地图数据转为csv字典数据
# Author: 尚尉
# Date: 2024-12-12
# Version: 1.0.0
"""

import json
import os

import pandas as pd
from opencc import OpenCC
from sortedcontainers import SortedSet

from cust_utils import time_stat, str_val
from custom_log_config import init_default_logger
from water_type_and_geojson_config import geojson_files

# 配置日志记录
# 创建一个自定义的日志记录器
log_file_name = 'water_body_dict.log'
logger = init_default_logger(log_file_name)

# 创建OpenCC对象，配置为繁体转简体
cc = OpenCC('t2s')
# 是否保留繁体名称
keep_traditional_name = True
# 扩展文件后缀(.csv、.xlsx)
dict_ext_name = '.csv'

# geojson文件所在目录
geojson_dir = 'data/geojson'
dict_dir = 'data/dict'


# 加载 GeoJSON 文件
def load_geojson(file_path):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    if os.path.isdir(file_path):
        raise IsADirectoryError(f"路径为目录而不是文件: {file_path}")

    with open(file_path, 'r', encoding='utf-8') as f:
        geojson_data = json.load(f)
    return geojson_data


def traditional_to_simplified(text: str):
    if text is None:
        return None
    # traditional_text = "這是一個測試文本。"
    # 转换为简体中文
    return cc.convert(str(text).strip())


def join_set(data):
    if data is None:
        return None

    if isinstance(data, str):
        join = data
    elif len(data) > 0:
        join = ';'.join(data)
    else:
        return None

    return join if join != "" else None


def init_set(val):
    my_set = SortedSet()
    if val is not None:
        my_set.add(val)
    return my_set


def add_set(set_obj, val):
    if (set_obj is not None) and (val is not None):
        if not isinstance(set_obj, SortedSet):
            raise TypeError("set_obj is not a SortedSet.")
        set_obj.add(val)


# 将GeoJSON数据转换为DataFrame，并提取指定字段
def geojson_to_dataframe(geojson_data, fields, file_name, fix_type):
    features = geojson_data.get('features', None)
    if features is None:
        return None

    data_map = {}
    for feature in features:
        properties = feature.get('properties', None)
        if properties is None:
            continue
        # row = {field: properties.get(field, None) for field in fields}

        name_en = str_val(properties, fields[1])
        if name_en is None:
            continue

        type_val = str_val(properties, fields[0])
        if (type_val is None) and (fix_type is not None):
            type_val = fix_type

        row = data_map.get(name_en, None)
        name_traditional = str_val(properties, fields[2])
        name_simplified = traditional_to_simplified(name_traditional)
        if row is None:
            row = {
                'type': init_set(type_val),
                'name': name_en,
                'name zh': init_set(name_simplified),
            }
            if keep_traditional_name:
                row['name traditional'] = init_set(name_traditional)
        else:
            add_set(row['type'], type_val)
            add_set(row['name zh'], name_simplified)
            if keep_traditional_name:
                add_set(row['name traditional'], name_traditional)

        row['source file'] = file_name
        data_map[name_en] = row

    data = []
    for row_key in data_map:
        row = data_map[row_key]
        for key in row:
            row[key] = join_set(row[key])
        data.append(row)
    return pd.DataFrame(data)


# 将DataFrame写入CSV(Excel)文件
def dataframe_to_file(df, file_path):
    # df.to_excel(excel_file, index=False, engine='openpyxl')
    df.to_csv(file_path, index=False, encoding='utf-8-sig')


def parse_geojson_to_csv(geo_item):
    geojson_file_name = geo_item.get('file_name', None)
    if geojson_file_name is None:
        logger.error("geojson_file_name is None")
        return
    geojson_file_name = geojson_file_name + '.geojson'

    fields = geo_item.get('fields', None)
    fix_type = geo_item.get('fix_type', None)

    # 读取geojson目录下的geojson文件
    geojson_file = os.path.join(geojson_dir, geojson_file_name)
    # 读取json数据
    geojson_data = None
    try:
        geojson_data = load_geojson(geojson_file)
    except Exception as e:
        logger.error(f"无法读取文件 {geojson_file}: {e}")
        return

    base_name = os.path.basename(geojson_file)  # 提取文件名
    # 将GeoJSON数据转换为DataFrame
    df = geojson_to_dataframe(geojson_data, fields, base_name, fix_type)
    if df is None:
        logger.error(f"无法解析文件 {geojson_file}.")
        return

    # 获取geojson_file的文件名，并替换后缀
    name_without_ext, _ = os.path.splitext(base_name)  # 分离文件名和后缀
    dict_file_name = name_without_ext + dict_ext_name  # 替换后缀

    if not os.path.exists(dict_dir):
        os.makedirs(dict_dir)

    # CSV文件输出路径
    csv_file = os.path.join(dict_dir, dict_file_name)

    # 将DataFrame写入CSV文件
    dataframe_to_file(df, csv_file)
    logger.info(f"数据已成功写入 {csv_file}.")


@time_stat(logger)
def gen_dict_main():
    logger.info("开始生成字典数据")
    # 遍历数组并调用方法
    for item in geojson_files:
        parse_geojson_to_csv(item)


if __name__ == '__main__':
    gen_dict_main()
