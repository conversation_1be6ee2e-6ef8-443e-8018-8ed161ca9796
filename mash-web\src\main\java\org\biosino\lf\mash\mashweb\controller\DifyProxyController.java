package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.config.AppConfig;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.ChatMessagesQueryDTO;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * AI流请求中转控制层
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Slf4j
@RestController
public class DifyProxyController extends BaseController {
    private final WebClient WEB_CLIENT;
    private final AppConfig appConfig;

    public DifyProxyController(AppConfig appConfig) {
        this.appConfig = appConfig;
        this.WEB_CLIENT = WebClient.builder()
                .baseUrl(appConfig.getDifyUrl()) // 替换为实际远程服务地址
                .defaultHeader("Authorization", TOKEN_PREFIX + appConfig.getDifyToken()) // 替换为实际token
                .build();
    }

    /**
     * 转发AI问答请求
     * <p>
     * 使用produces控制返回类型text/event-stream
     */
    @PostMapping(value = "/chat-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> proxyStreaming(@RequestHeader("Authorization") final String auth,
                                       @Validated @RequestBody final ChatMessagesQueryDTO queryDTO) {

        final JSONObject jsonObject = (JSONObject) JSON.toJSON(queryDTO);
        // response_mode 固定设置为streaming
        jsonObject.put("response_mode", "streaming");

        // 需要先使用validateLocalToken验证本地token
        return Mono.justOrEmpty(auth)
                .filter(BaseController::validateLocalToken)
                .switchIfEmpty(Mono.error(new ServiceException("Unauthorized")))
                .flatMapMany(__ -> WEB_CLIENT.post()
                        .uri("/chat-messages")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(jsonObject)
                        .retrieve()
                        .bodyToFlux(String.class)
                        .limitRate(16) // 关键限流控制
                        .doOnError(ex -> log.error("AI问答流式传输中断", ex))
                        .onErrorResume(ex -> Flux.just("{\"code\":503,\"message\":\"Service busy\"}"))
                );
    }


    @PostMapping(value = "/chat-messages/{taskId}/stop")
    public String proxyStreamingStop(@RequestHeader("Authorization") final String auth,
                                     @PathVariable("taskId") final String taskId,
                                     @RequestBody final ChatMessagesQueryDTO queryDTO) {
        // 1. 验证本地token
        if (!validateLocalToken(auth)) {
            throw new ServiceException("Invalid token!");
        }

        final String user = queryDTO.getUser();
        if (user == null) {
            throw new ServiceException("User ID cannot be empty");
        }

        final JSONObject param = new JSONObject();
        param.put("user", user);
        final String urlString = appConfig.getDifyUrl() + StrUtil.format("/chat-messages/{}/stop", taskId);
        try (HttpResponse response = post(urlString, param.toJSONString(), appConfig.getDifyToken())
                .execute()) {
            final int status = response.getStatus();
            if (status != 200) {
                throw new ServiceException("Stop failed, Http code:" + status);
            }
            final JSONObject resultObj = JSON.parseObject(response.body());
            if ("success".equalsIgnoreCase(resultObj.getString("result"))) {
                return "success stop";
            } else {
                throw new ServiceException("Stop failed: " + resultObj.toJSONString());
            }
        }
    }
}
