package org.biosino.lf.mash.mashweb.config;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;

/**
 * Jackson配置，添加字符串的trim处理
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        // 先应用yml中的配置
        final ObjectMapper objectMapper = builder.build();
        // 然后添加自定义模块
        objectMapper.registerModule(stringTrimModule());
        return objectMapper;
    }

    /**
     * 字符串处理自定义模块
     */
    public SimpleModule stringTrimModule() {
        final SimpleModule module = new SimpleModule();
        // 添加自定义解析器
        module.addDeserializer(String.class, new StringTrimDeserializer());
        return module;
    }

    /**
     * 字符串自定义解析器，对字符串执行trimToNull操作
     */
    public static class StringTrimDeserializer extends JsonDeserializer<String> {
        @Override
        public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
            return StrUtil.trimToNull(p.getValueAsString());
        }
    }

}
