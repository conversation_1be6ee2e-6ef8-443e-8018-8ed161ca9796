<template>
  <div class="visits-log-page">
    <div class="container-fluid">
      <!-- 标题 -->
      <h1 class="page-title">访问日志统计</h1>

      <!-- 登录表单 -->
      <div v-if="!isAuthenticated" class="auth-container">
        <div class="card">
          <div class="card-body">
            <h3>访问授权</h3>
            <p>请输入访问令牌以查看统计数据</p>

            <el-form ref="authFormRef" :model="authForm" :rules="authRules">
              <el-form-item prop="token">
                <el-input
                  v-model="authForm.token"
                  placeholder="请输入访问令牌"
                  show-password
                  @keyup.enter="handleLogin"
                ></el-input>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="handleLogin"
                >
                  验证
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 统计数据展示 -->
      <div v-else class="statistics-container">
        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <div
                  class="stats-item"
                  style="display: flex; justify-content: space-around"
                >
                  <div class="stats-info">
                    <h2>{{ stats.uv }}</h2>
                    <p>独立访问量 (UV)</p>
                    <small
                      >相同访客（当前标签页关闭前都算一个访客）多次访问网站，只计算为1个独立访客</small
                    >
                  </div>
                  <div class="stats-info" style="margin-left: 10px">
                    <h2>{{ stats.pv }}</h2>
                    <p>网站浏览量 (PV)</p>
                    <small>用户每打开一个页面记录1次PV</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 页面访问统计表格 -->
        <div class="card mt-4">
          <div class="card-body">
            <div class="table-header">
              <h3>页面访问统计</h3>
              <div class="table-summary">
                <el-tag type="info" size="large"
                  >总访问次数: {{ getPageVisitsTotal() }}
                </el-tag>
              </div>
            </div>
            <div v-loading="loading" class="table-container">
              <el-table
                :data="stats.pageStats"
                stripe
                style="width: 100%"
                border
              >
                <el-table-column
                  type="index"
                  label="序号"
                  width="60"
                  align="center"
                />
                <el-table-column prop="url" label="URL路径" min-width="220">
                  <template #default="scope">
                    <span>{{ scope.row.url }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" min-width="150">
                  <template #default="scope">
                    <el-tag
                      v-if="getRouteTitle(scope.row.url)"
                      type="success"
                      effect="plain"
                    >
                      {{ getRouteTitle(scope.row.url) }}
                    </el-tag>
                    <span v-else class="text-muted">未知页面</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="访问次数"
                  width="150"
                  align="center"
                >
                  <template #default="scope">
                    <el-tag type="info" effect="light"
                      >{{ scope.row.count }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="占比" width="150" align="center">
                  <template #default="scope">
                    <div class="percentage-cell">
                      <span>{{ scope.row.percentage }}%</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 功能统计表格 -->
        <div class="card mt-4">
          <div class="card-body">
            <div class="table-header">
              <h3>功能统计</h3>
              <div class="table-summary">
                <el-tag type="primary" size="large"
                  >总访问次数: {{ getFuncVisitsTotal() }}
                </el-tag>
              </div>
            </div>
            <div v-loading="loading" class="table-container">
              <el-table
                :data="stats.funcStats"
                stripe
                style="width: 100%"
                border
              >
                <el-table-column
                  type="index"
                  label="序号"
                  width="60"
                  align="center"
                />
                <el-table-column prop="name" label="功能模块" min-width="150">
                  <template #default="scope">
                    <span>{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="url" label="URL路径" min-width="180">
                  <template #default="scope">
                    <span>{{ scope.row.url }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" min-width="120">
                  <template #default="scope">
                    <el-tag
                      v-if="getRouteTitle(scope.row.url)"
                      type="success"
                      effect="plain"
                    >
                      {{ getRouteTitle(scope.row.url) }}
                    </el-tag>
                    <span v-else class="text-muted">未知页面</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="访问次数"
                  width="150"
                  align="center"
                >
                  <template #default="scope">
                    <el-tag type="primary" effect="light"
                      >{{ scope.row.count }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="占比" width="150" align="center">
                  <template #default="scope">
                    <div class="percentage-cell">
                      <span>{{ scope.row.percentage }}%</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <div class="actions mt-3" style="margin-top: 1rem">
          <!--<el-button type="info" @click="handleLogout">退出</el-button>-->
          <el-button type="primary" :loading="loading" @click="fetchStatistics">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref } from 'vue';
  import { getVisitStatistics } from '@/api/visits_log';
  import { router_info_export } from '@/router';

  const { proxy } = getCurrentInstance();

  // 状态
  const isAuthenticated = ref(false);
  const loading = ref(false);
  const stats = reactive({
    uv: 0,
    pv: 0,
    pageStats: [],
    funcStats: [],
  });

  // 表单相关
  const authFormRef = ref(null);
  const authForm = reactive({
    token: '',
  });
  const authRules = {
    token: [{ required: true, message: '请输入访问令牌', trigger: 'blur' }],
  };

  // 根据百分比获取进度条颜色
  const getProgressColor = percentage => {
    if (percentage < 30) return '#909399';
    if (percentage < 70) return '#409EFF';
    return '#1e7cb2';
  };

  // 获取路由标题
  const getRouteTitle = url => {
    if (!url) return '';
    /*// 从URL中提取路径部分（去除查询参数）
  const path = url.split('?')[0];*/
    const path = url;

    let headTitle = '';
    // 查找匹配的路由信息
    for (let i = 0; i < router_info_export.length; i++) {
      let item = router_info_export[i];
      if (path === item.path || item.path.startsWith(`${path}/`)) {
        headTitle = item.headTitle;
        break;
      }
    }

    return headTitle;
  };

  // 获取页面访问总数
  const getPageVisitsTotal = () => {
    return stats.pageStats.reduce((sum, item) => sum + item.count, 0);
  };

  // 获取功能访问总数
  const getFuncVisitsTotal = () => {
    return stats.funcStats.reduce((sum, item) => sum + item.count, 0);
  };

  // 处理登录
  const handleLogin = () => {
    authFormRef.value.validate(valid => {
      if (!valid) return;

      fetchStatistics();
    });
  };

  // 处理退出
  const handleLogout = () => {
    isAuthenticated.value = false;
    authForm.token = '';
  };

  // 获取统计数据
  const fetchStatistics = () => {
    loading.value = true;
    getVisitStatistics(authForm.token)
      .then(response => {
        if (response.code === 200) {
          isAuthenticated.value = true;
          stats.uv = response.data.uv || 0;
          stats.pv = response.data.pv || 0;
          stats.pageStats = response.data.pageStats || [];
          stats.funcStats = response.data.funcStats || [];
        } else {
          proxy.$modal.msgError(response.msg || '获取数据失败');
          isAuthenticated.value = false;
        }
      })
      .catch(error => {
        proxy.$modal.msgError('获取数据失败：' + error.message);
        isAuthenticated.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  // 生命周期
  // onMounted(() => {
  // });
</script>

<style lang="scss" scoped>
  .visits-log-page {
    padding: 120px 0 45px 0;

    .page-title {
      margin-bottom: 2rem;
      color: #1e7cb2;
      text-align: center;
    }

    .auth-container {
      max-width: 500px;
      margin: 0 auto;

      .card {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: none;

        .card-body {
          padding: 2rem;
        }

        h3 {
          color: #1e7cb2;
          margin-bottom: 1rem;
        }
      }
    }

    .statistics-container {
      .stats-item {
        display: flex;
        align-items: center;

        .stats-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background-color: rgba(30, 124, 178, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1.5rem;

          i {
            font-size: 40px;
            color: #1e7cb2;
          }

          &.pv-icon {
            background-color: rgba(66, 153, 225, 0.2);
          }
        }

        .stats-info {
          h2 {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin: 0;
          }

          p {
            font-size: 1.2rem;
            margin: 0.5rem 0;
            color: #555;
          }

          small {
            color: #888;
            font-size: 0.9rem;
          }
        }
      }

      .actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
      }
    }

    .table-container {
      min-height: 200px;
    }

    .percentage-cell {
      display: flex;
      flex-direction: column;
      align-items: center;

      span {
        margin-top: 4px;
        font-size: 12px;
        color: #606266;
      }
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h3 {
        margin: 0;
        color: #1e7cb2;
      }

      .table-summary {
        .el-tag {
          font-size: 14px;
          padding: 6px 12px;
        }
      }
    }

    .text-muted {
      color: #909399;
      font-style: italic;
      font-size: 13px;
    }
  }
</style>
