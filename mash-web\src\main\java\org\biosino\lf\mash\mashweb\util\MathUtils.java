package org.biosino.lf.mash.mashweb.util;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
public class MathUtils {
    public static double calculateMedian(List<Double> sorted) {
        int n = sorted.size();
        double H = (n - 1) * 0.5 + 1;
        int h = (int) Math.floor(H);
        double v = sorted.get(h - 1);
        double e = H - h;

        return e > 0 ? v + e * (sorted.get(h) - v) : v;
    }
}
