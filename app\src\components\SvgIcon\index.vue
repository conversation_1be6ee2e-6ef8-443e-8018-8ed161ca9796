<template>
  <svg
    :class="svgClass"
    aria-hidden="true"
    :style="{ width: width, height: height, color: color }"
  >
    <use :xlink:href="iconName" />
  </svg>
</template>

<script setup>
  import { defineProps, ref } from 'vue';
  const props = defineProps({
    iconClass: {
      type: String,
      required: true,
    },
    className: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      required: false,
    },
    height: {
      type: String,
      required: false,
    },
    color: {
      type: String,
      required: false,
    },
  });
  const iconName = ref(`#icon-${props.iconClass}`);
  const svgClass = ref(props.className);
</script>

<style scope lang="scss"></style>
